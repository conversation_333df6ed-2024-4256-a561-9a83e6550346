# Mente en Calma - Aplicación Android Nativa

Una aplicación Android nativa desarrollada con **Jetpack Compose**, **Navigation Component** y arquitectura **MVVM** para la plataforma de bienestar mental "Mente en Calma".

## 🏗️ Arquitectura

La aplicación sigue los principios de **Clean Architecture** y el patrón **MVVM**:

### Capas de la Arquitectura

```
📱 Presentation Layer (UI)
├── 🎨 Composables (Screens & Components)
├── 🧠 ViewModels
└── 🎯 Navigation

🏢 Domain Layer
├── 📋 Models
├── 🔄 Use Cases
└── 📝 Repository Interfaces

💾 Data Layer
├── 🗄️ Repository Implementations
├── 🌐 Remote Data Sources (Firebase)
└── 💿 Local Data Sources
```

## 🧭 Navegación

### Estructura de Navegación

La aplicación utiliza **Navigation Component** con las siguientes rutas:

#### Pantallas Principales (con Bottom Navigation)
- **Chat** (`/chat`) - Chat terapéutico con IA
- **Recomendaciones** (`/recommendations`) - Actividades personalizadas
- **Artículos** (`/articles`) - Contenido educativo
- **Perfil** (`/profile`) - Gestión de cuenta

#### Pantallas de Autenticación
- **Splash** (`/splash`) - Pantalla de carga inicial
- **Login** (`/login`) - Inicio de sesión
- **Crear Perfil** (`/create_profile`) - Configuración inicial

#### Pantallas Adicionales
- **Suscripción** (`/subscribe`) - Planes premium
- **Detalle de Artículo** (`/article_detail/{id}`) - Vista detallada

### Flujo de Navegación Inicial

```mermaid
graph TD
    A[Splash Screen] --> B{¿Usuario autenticado?}
    B -->|No| C[Login Screen]
    B -->|Sí| D{¿Tiene perfil?}
    D -->|No| E[Create Profile Screen]
    D -->|Sí| F[Chat Screen - Main App]
    C --> G[Registro exitoso]
    G --> E
    E --> F
```

## 🎨 Componentes UI

### MainActivity
- **Scaffold principal** con navegación inferior
- **Gestión de estado de autenticación**
- **Control de visibilidad de Bottom Navigation**

### BottomNavigationBar
- **4 elementos de navegación** con iconos Material
- **Indicadores visuales** para el elemento activo
- **Navegación optimizada** con estado guardado

### Pantallas Principales
Cada pantalla principal implementa:
- **ViewModel** para gestión de estado
- **Composables** reutilizables
- **Integración con repositorios**

## 🔧 Tecnologías Utilizadas

### Core Android
- **Jetpack Compose** - UI moderna y declarativa
- **Navigation Component** - Navegación entre pantallas
- **ViewModel** - Gestión de estado UI
- **Hilt** - Inyección de dependencias

### Firebase
- **Firebase Auth** - Autenticación de usuarios
- **Firestore** - Base de datos en tiempo real
- **Firebase Analytics** - Métricas de uso

### Otras Librerías
- **Google Play Billing** - Suscripciones in-app
- **Coroutines** - Programación asíncrona
- **StateFlow** - Gestión de estado reactivo

## 📁 Estructura del Proyecto

```
app/src/main/java/com/menteencalma/app/
├── 📱 presentation/
│   ├── components/          # Componentes reutilizables
│   │   └── BottomNavigationBar.kt
│   ├── screens/            # Pantallas de la app
│   │   ├── auth/           # Autenticación
│   │   ├── chat/           # Chat terapéutico
│   │   ├── recommendations/ # Recomendaciones
│   │   ├── articles/       # Artículos
│   │   ├── profile/        # Perfil de usuario
│   │   ├── splash/         # Pantalla inicial
│   │   └── subscription/   # Suscripciones
│   └── viewmodels/         # ViewModels
├── 🏢 domain/
│   ├── model/              # Modelos de dominio
│   └── repository/         # Interfaces de repositorio
├── 💾 data/
│   └── repository/         # Implementaciones de repositorio
├── 🔧 di/                  # Inyección de dependencias
├── 🧭 navigation/          # Configuración de navegación
├── 🎨 ui/theme/           # Tema y colores
└── MainActivity.kt         # Actividad principal
```

## 🚀 Configuración del Proyecto

### Prerrequisitos
- **Android Studio** Hedgehog o superior
- **JDK 11** o superior
- **Android SDK** nivel 24+ (Android 7.0)

### Configuración de Firebase
1. Crear proyecto en [Firebase Console](https://console.firebase.google.com/)
2. Añadir aplicación Android con package `com.menteencalma.app`
3. Descargar `google-services.json` y colocarlo en `app/`
4. Habilitar **Authentication** y **Firestore**

### Configuración de Google Play Console
1. Crear aplicación en [Google Play Console](https://play.google.com/console/)
2. Configurar productos de suscripción
3. Obtener clave de licencia para billing

## 🔄 Estados de la Aplicación

### AuthState
```kotlin
sealed class AuthState {
    object Loading          // Verificando autenticación
    object Unauthenticated // Ir a login
    object NeedsProfile    // Crear perfil
    object Authenticated   // App principal
    data class Error(message: String)
}
```

### Flujo de Estados
1. **Loading** - Verificación inicial
2. **Unauthenticated** → Login/Registro
3. **NeedsProfile** → Crear perfil
4. **Authenticated** → Aplicación principal

## 🎯 Características Principales

### ✅ Implementado
- [x] Estructura base con Jetpack Compose
- [x] Navegación con Bottom Navigation
- [x] Arquitectura MVVM con Hilt
- [x] Integración básica con Firebase
- [x] Pantallas principales definidas
- [x] Sistema de autenticación base

### 🚧 En Desarrollo
- [ ] Implementación completa de pantallas
- [ ] Chat con IA
- [ ] Sistema de recomendaciones
- [ ] Gestión de artículos
- [ ] Suscripciones con Google Play Billing
- [ ] Sincronización cross-platform

## 🧪 Testing

### Estructura de Tests
```
app/src/test/          # Unit tests
app/src/androidTest/   # Integration tests
```

### Comandos de Testing
```bash
# Unit tests
./gradlew test

# Integration tests
./gradlew connectedAndroidTest

# UI tests
./gradlew connectedDebugAndroidTest
```

## 📦 Build y Deploy

### Build de Desarrollo
```bash
./gradlew assembleDebug
```

### Build de Producción
```bash
./gradlew assembleRelease
```

### Subir a Play Store
```bash
./gradlew bundleRelease
```

## 🔗 Integración Cross-Platform

La aplicación está diseñada para sincronizar con:
- **App React Native/Expo** existente
- **Aplicación Web** con Firebase
- **Suscripciones unificadas** entre plataformas

### Estructura de Datos Compartida
```kotlin
data class User(
    val id: String,
    val email: String,
    val subscriptionStatus: String,
    val subscriptionPlatform: String?
)
```

## 📞 Soporte

Para soporte técnico o preguntas sobre la implementación, contacta al equipo de desarrollo.

---

**Mente en Calma** - Aplicación Android Nativa con Jetpack Compose
