<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme using compatible Material Design -->
    <style name="Base.Theme.MenteEnCalma" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Primary colors -->
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryDark">@color/primary_color</item>
        <item name="colorAccent">@color/secondary_color</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>

        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/black</item>
        <item name="android:textColorSecondary">@color/secondary_color</item>

        <!-- Status bar -->
        <item name="android:statusBarColor">@color/primary_color</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">false</item>
    </style>

    <style name="Theme.MenteEnCalma" parent="Base.Theme.MenteEnCalma" />

    <!-- Splash Screen Theme - Simplified -->
    <style name="Theme.MenteEnCalma.Splash" parent="Theme.MenteEnCalma">
        <item name="android:windowBackground">@color/primary_color</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>
