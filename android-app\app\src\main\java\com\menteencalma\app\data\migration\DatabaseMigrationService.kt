package com.menteencalma.app.data.migration

import android.util.Log
import com.menteencalma.app.domain.model.*
import com.menteencalma.app.domain.repository.DatabaseRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Servicio para preparar y ejecutar migraciones de base de datos
 * Facilita la transición de Firebase a PostgreSQL/Supabase
 */
@Singleton
class DatabaseMigrationService @Inject constructor(
    private val sourceRepository: DatabaseRepository
) {

    private val _migrationStatus = MutableStateFlow(MigrationStatus())
    val migrationStatus: StateFlow<MigrationStatus> = _migrationStatus.asStateFlow()

    private val json = Json { 
        prettyPrint = true
        ignoreUnknownKeys = true
    }

    companion object {
        private const val TAG = "DatabaseMigration"
    }

    /**
     * Genera el schema SQL para PostgreSQL/Supabase
     */
    fun generateSQLSchema(): String {
        val schema = StringBuilder()
        
        schema.appendLine("-- Mente en Calma Database Schema")
        schema.appendLine("-- Generated for PostgreSQL/Supabase migration")
        schema.appendLine("-- Date: ${java.util.Date()}")
        schema.appendLine()
        
        // Users table
        schema.appendLine("-- Users table")
        schema.appendLine(User.SQL_SCHEMA)
        schema.appendLine()
        
        // Chat messages table
        schema.appendLine("-- Chat messages table")
        schema.appendLine(ChatMessage.SQL_SCHEMA)
        schema.appendLine()
        
        // Articles table
        schema.appendLine("-- Articles table")
        schema.appendLine(Article.SQL_SCHEMA)
        schema.appendLine()
        
        // Additional indexes for performance
        schema.appendLine("-- Additional performance indexes")
        schema.appendLine("CREATE INDEX CONCURRENTLY idx_users_subscription_active ON users (subscription_status, subscription_expires_at) WHERE subscription_status != 'free';")
        schema.appendLine("CREATE INDEX CONCURRENTLY idx_chat_messages_recent ON chat_messages (user_id, timestamp DESC) WHERE timestamp > EXTRACT(EPOCH FROM NOW() - INTERVAL '30 days') * 1000;")
        schema.appendLine("CREATE INDEX CONCURRENTLY idx_articles_published_premium ON articles (published_at DESC, is_premium);")
        schema.appendLine()
        
        // Views for common queries
        schema.appendLine("-- Useful views")
        schema.appendLine("""
            CREATE VIEW active_subscribers AS
            SELECT 
                id,
                email,
                display_name,
                subscription_status,
                subscription_platform,
                subscription_expires_at
            FROM users 
            WHERE subscription_status != 'free' 
            AND (subscription_expires_at IS NULL OR subscription_expires_at > EXTRACT(EPOCH FROM NOW()) * 1000);
        """.trimIndent())
        schema.appendLine()
        
        schema.appendLine("""
            CREATE VIEW user_engagement AS
            SELECT 
                u.id,
                u.display_name,
                u.created_at,
                (u.analytics->>'totalSessions')::int as total_sessions,
                (u.analytics->>'totalMinutes')::int as total_minutes,
                (u.analytics->>'streakDays')::int as streak_days,
                COUNT(cm.id) as total_messages
            FROM users u
            LEFT JOIN chat_messages cm ON u.id = cm.user_id
            GROUP BY u.id, u.display_name, u.created_at, u.analytics;
        """.trimIndent())
        schema.appendLine()
        
        return schema.toString()
    }

    /**
     * Exporta datos de usuarios en formato JSON para migración
     */
    suspend fun exportUsers(limit: Int = 1000): Result<String> {
        return try {
            updateStatus("Exporting users...")
            
            // En una implementación real, harías paginación
            val users = sourceRepository.searchUsers(limit = limit)
            
            if (users.isFailure) {
                return Result.failure(users.exceptionOrNull() ?: Exception("Failed to export users"))
            }
            
            val usersList = users.getOrNull() ?: emptyList()
            val exportData = ExportData(
                users = usersList,
                exportedAt = System.currentTimeMillis(),
                version = "1.0",
                totalRecords = usersList.size
            )
            
            val jsonData = json.encodeToString(exportData)
            updateStatus("Exported ${usersList.size} users successfully")
            
            Result.success(jsonData)
        } catch (e: Exception) {
            Log.e(TAG, "Error exporting users", e)
            updateStatus("Error exporting users: ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * Valida la integridad de los datos antes de la migración
     */
    suspend fun validateDataIntegrity(): Result<ValidationReport> {
        return try {
            updateStatus("Validating data integrity...")
            
            val report = ValidationReport()
            
            // Validar usuarios
            val users = sourceRepository.searchUsers(limit = 100)
            if (users.isSuccess) {
                val usersList = users.getOrNull() ?: emptyList()
                usersList.forEach { user ->
                    val validation = user.validate()
                    if (validation.isFailure) {
                        report.errors.add("User ${user.id}: ${validation.exceptionOrNull()?.message}")
                    }
                }
                report.usersValidated = usersList.size
            }
            
            updateStatus("Data validation completed")
            Result.success(report)
        } catch (e: Exception) {
            Log.e(TAG, "Error validating data", e)
            updateStatus("Error validating data: ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * Genera un script de migración personalizado
     */
    fun generateMigrationScript(targetDatabase: TargetDatabase): String {
        val script = StringBuilder()
        
        when (targetDatabase) {
            TargetDatabase.SUPABASE -> {
                script.appendLine("-- Supabase Migration Script")
                script.appendLine("-- Run this in the Supabase SQL editor")
                script.appendLine()
                script.appendLine(generateSQLSchema())
                script.appendLine()
                script.appendLine("-- Enable Row Level Security")
                script.appendLine("ALTER TABLE users ENABLE ROW LEVEL SECURITY;")
                script.appendLine("ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;")
                script.appendLine("ALTER TABLE articles ENABLE ROW LEVEL SECURITY;")
                script.appendLine()
                script.appendLine("-- RLS Policies")
                script.appendLine("CREATE POLICY \"Users can view own data\" ON users FOR SELECT USING (auth.uid()::text = id);")
                script.appendLine("CREATE POLICY \"Users can update own data\" ON users FOR UPDATE USING (auth.uid()::text = id);")
                script.appendLine("CREATE POLICY \"Users can view own messages\" ON chat_messages FOR SELECT USING (auth.uid()::text = user_id);")
                script.appendLine("CREATE POLICY \"Users can insert own messages\" ON chat_messages FOR INSERT WITH CHECK (auth.uid()::text = user_id);")
                script.appendLine("CREATE POLICY \"Anyone can view published articles\" ON articles FOR SELECT USING (true);")
            }
            
            TargetDatabase.POSTGRESQL -> {
                script.appendLine("-- PostgreSQL Migration Script")
                script.appendLine()
                script.appendLine(generateSQLSchema())
                script.appendLine()
                script.appendLine("-- Create application user")
                script.appendLine("CREATE USER mente_en_calma_app WITH PASSWORD 'your_secure_password';")
                script.appendLine("GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO mente_en_calma_app;")
                script.appendLine("GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO mente_en_calma_app;")
            }
            
            TargetDatabase.MYSQL -> {
                script.appendLine("-- MySQL Migration Script")
                script.appendLine("-- Note: Adjust data types for MySQL compatibility")
                script.appendLine()
                // Convertir tipos de PostgreSQL a MySQL
                val mysqlSchema = generateSQLSchema()
                    .replace("BIGINT", "BIGINT")
                    .replace("TEXT\\[\\]".toRegex(), "JSON") // Arrays como JSON
                    .replace("JSONB", "JSON")
                    .replace("VARCHAR\\((\\d+)\\)".toRegex(), "VARCHAR($1)")
                    .replace("BOOLEAN", "TINYINT(1)")
                    .replace("INDEX", "KEY")
                    .replace("CREATE INDEX CONCURRENTLY", "CREATE INDEX")
                
                script.appendLine(mysqlSchema)
            }
        }
        
        return script.toString()
    }

    /**
     * Estima el tiempo y costo de migración
     */
    suspend fun estimateMigration(): Result<MigrationEstimate> {
        return try {
            updateStatus("Estimating migration...")
            
            // Obtener métricas básicas
            val users = sourceRepository.searchUsers(limit = 10)
            val userCount = users.getOrNull()?.size ?: 0
            
            // Estimaciones basadas en experiencia
            val estimatedUsers = userCount * 100 // Extrapolación
            val estimatedMessages = estimatedUsers * 50 // Promedio de mensajes por usuario
            val estimatedArticles = 100 // Número fijo de artículos
            
            val estimate = MigrationEstimate(
                estimatedUsers = estimatedUsers,
                estimatedMessages = estimatedMessages,
                estimatedArticles = estimatedArticles,
                estimatedDurationHours = calculateMigrationTime(estimatedUsers, estimatedMessages),
                estimatedCostUSD = calculateMigrationCost(estimatedUsers, estimatedMessages),
                recommendedApproach = getRecommendedApproach(estimatedUsers)
            )
            
            updateStatus("Migration estimation completed")
            Result.success(estimate)
        } catch (e: Exception) {
            Log.e(TAG, "Error estimating migration", e)
            updateStatus("Error estimating migration: ${e.message}")
            Result.failure(e)
        }
    }

    private fun calculateMigrationTime(users: Int, messages: Int): Double {
        // Tiempo base + tiempo por registro
        val baseTime = 2.0 // horas
        val timePerUser = 0.001 // horas por usuario
        val timePerMessage = 0.0001 // horas por mensaje
        
        return baseTime + (users * timePerUser) + (messages * timePerMessage)
    }

    private fun calculateMigrationCost(users: Int, messages: Int): Double {
        // Costo de desarrollador + costo de infraestructura
        val developerHourlyRate = 50.0 // USD
        val migrationTime = calculateMigrationTime(users, messages)
        val infrastructureCost = 100.0 // USD (setup, testing, etc.)
        
        return (migrationTime * developerHourlyRate) + infrastructureCost
    }

    private fun getRecommendedApproach(users: Int): String {
        return when {
            users < 1000 -> "Manual migration with data export/import"
            users < 10000 -> "Automated migration with gradual rollout"
            else -> "Phased migration with parallel systems"
        }
    }

    private fun updateStatus(message: String) {
        val currentStatus = _migrationStatus.value
        _migrationStatus.value = currentStatus.copy(
            currentStep = message,
            lastUpdated = System.currentTimeMillis()
        )
        Log.i(TAG, message)
    }
}

/**
 * Estado de la migración
 */
data class MigrationStatus(
    val currentStep: String = "Ready",
    val progress: Float = 0f,
    val isRunning: Boolean = false,
    val lastUpdated: Long = System.currentTimeMillis()
)

/**
 * Datos de exportación
 */
@kotlinx.serialization.Serializable
data class ExportData(
    val users: List<User>,
    val exportedAt: Long,
    val version: String,
    val totalRecords: Int
)

/**
 * Reporte de validación
 */
data class ValidationReport(
    val errors: MutableList<String> = mutableListOf(),
    val warnings: MutableList<String> = mutableListOf(),
    val usersValidated: Int = 0,
    val messagesValidated: Int = 0,
    val articlesValidated: Int = 0
) {
    val isValid: Boolean get() = errors.isEmpty()
}

/**
 * Estimación de migración
 */
data class MigrationEstimate(
    val estimatedUsers: Int,
    val estimatedMessages: Int,
    val estimatedArticles: Int,
    val estimatedDurationHours: Double,
    val estimatedCostUSD: Double,
    val recommendedApproach: String
)

enum class TargetDatabase {
    SUPABASE,
    POSTGRESQL,
    MYSQL
}
