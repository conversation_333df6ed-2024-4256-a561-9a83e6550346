package com.menteencalma.app

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
// import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.rememberNavController
import com.menteencalma.app.navigation.MenteEnCalmaNavigation
import com.menteencalma.app.presentation.components.BottomNavigationBar
// import com.menteencalma.app.presentation.viewmodels.AuthViewModel
import com.menteencalma.app.ui.theme.MenteEnCalmaTheme
// import dagger.hilt.android.AndroidEntryPoint

// @AndroidEntryPoint // Temporalmente deshabilitado
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        val splashScreen = installSplashScreen()
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        setContent {
            MenteEnCalmaTheme {
                MenteEnCalmaApp()
            }
        }
    }
}

@Composable
fun MenteEnCalmaApp() {
    val navController = rememberNavController()
    // val authViewModel: AuthViewModel = hiltViewModel() // Temporalmente deshabilitado

    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Scaffold(
            bottomBar = {
                // Temporalmente siempre mostrar la barra de navegación
                BottomNavigationBar(navController = navController)
            }
        ) { innerPadding ->
            MenteEnCalmaNavigation(
                navController = navController,
                modifier = Modifier.padding(innerPadding)
                // authViewModel = authViewModel // Temporalmente deshabilitado
            )
        }
    }
}
