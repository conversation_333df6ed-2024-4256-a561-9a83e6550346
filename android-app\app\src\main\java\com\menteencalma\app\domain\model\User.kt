package com.menteencalma.app.domain.model

import kotlinx.serialization.Serializable

/**
 * Modelo de usuario optimizado para múltiples backends
 */
@Serializable
data class User(
    val id: String = "",
    val email: String = "",
    val displayName: String = "",
    val age: Int? = null,
    val gender: String? = null, // "male", "female", "other", "prefer_not_to_say"
    val therapistGender: String? = null, // "aurora", "alejandro"
    val interests: List<String> = emptyList(),
    val subscriptionStatus: String = "free", // "free", "premium", "premium_plus"
    val subscriptionPlatform: String? = null, // "google_play", "paypal", "stripe"
    val subscriptionExpiresAt: Long? = null,
    val personalizedGreeting: String? = null, // Generado por Cloud Function
    val profileImageUrl: String? = null,
    val timezone: String? = null,
    val language: String = "es", // ISO 639-1
    val notificationSettings: NotificationSettings = NotificationSettings(),
    val preferences: UserPreferences = UserPreferences(),
    val analytics: UserAnalytics = UserAnalytics(),
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),

    // Campos para migración/compatibilidad
    val version: Int = 1,
    val source: String = "firebase"
) {

    @Serializable
    data class NotificationSettings(
        val pushEnabled: Boolean = true,
        val emailEnabled: Boolean = true,
        val chatReminders: Boolean = true,
        val articleRecommendations: Boolean = true,
        val exerciseReminders: Boolean = true,
        val subscriptionUpdates: Boolean = true,
        val quietHoursStart: String? = null, // "22:00"
        val quietHoursEnd: String? = null    // "08:00"
    )

    @Serializable
    data class UserPreferences(
        val theme: String = "system", // "light", "dark", "system"
        val chatStyle: String = "conversational", // "conversational", "formal", "casual"
        val exerciseFrequency: String = "daily", // "daily", "weekly", "custom"
        val reminderTime: String? = null, // "09:00"
        val preferredSessionLength: Int = 15, // minutos
        val autoPlayAudio: Boolean = true,
        val dataUsageMode: String = "normal" // "low", "normal", "high"
    )

    @Serializable
    data class UserAnalytics(
        val totalSessions: Int = 0,
        val totalMinutes: Int = 0,
        val streakDays: Int = 0,
        val longestStreak: Int = 0,
        val lastActiveAt: Long? = null,
        val favoriteExercises: List<String> = emptyList(),
        val completedArticles: List<String> = emptyList(),
        val moodHistory: List<MoodEntry> = emptyList()
    )

    @Serializable
    data class MoodEntry(
        val date: Long,
        val mood: Int, // 1-10 scale
        val notes: String? = null
    )

    companion object {
        const val COLLECTION_NAME = "users"
        const val CURRENT_VERSION = 1

        // Constantes para validación
        const val MIN_AGE = 13
        const val MAX_AGE = 120
        const val MAX_DISPLAY_NAME_LENGTH = 100
        const val MAX_INTERESTS = 10

        // Valores válidos
        val VALID_GENDERS = setOf("male", "female", "other", "prefer_not_to_say")
        val VALID_THERAPISTS = setOf("aurora", "alejandro")
        val VALID_SUBSCRIPTION_STATUS = setOf("free", "premium", "premium_plus")
        val VALID_PLATFORMS = setOf("google_play", "paypal", "stripe", "apple_pay")

        // Índices recomendados para Firebase/Firestore
        val FIRESTORE_INDEXES = listOf(
            "email",
            "subscriptionStatus",
            "therapistGender",
            "createdAt",
            "subscriptionStatus,subscriptionExpiresAt",
            "therapistGender,subscriptionStatus"
        )

        // Schema SQL para migración futura
        const val SQL_SCHEMA = """
            CREATE TABLE users (
                id VARCHAR(255) PRIMARY KEY,
                email VARCHAR(255) UNIQUE NOT NULL,
                display_name VARCHAR(100) NOT NULL,
                age INTEGER CHECK (age >= 13 AND age <= 120),
                gender VARCHAR(50) CHECK (gender IN ('male', 'female', 'other', 'prefer_not_to_say')),
                therapist_gender VARCHAR(50) CHECK (therapist_gender IN ('aurora', 'alejandro')),
                interests TEXT[], -- PostgreSQL array
                subscription_status VARCHAR(50) DEFAULT 'free' CHECK (subscription_status IN ('free', 'premium', 'premium_plus')),
                subscription_platform VARCHAR(50) CHECK (subscription_platform IN ('google_play', 'paypal', 'stripe', 'apple_pay')),
                subscription_expires_at BIGINT,
                personalized_greeting TEXT,
                profile_image_url VARCHAR(1000),
                timezone VARCHAR(50),
                language VARCHAR(10) DEFAULT 'es',
                notification_settings JSONB,
                preferences JSONB,
                analytics JSONB,
                created_at BIGINT NOT NULL,
                updated_at BIGINT NOT NULL,
                version INTEGER DEFAULT 1,
                source VARCHAR(50) DEFAULT 'firebase',

                INDEX idx_email (email),
                INDEX idx_subscription (subscription_status),
                INDEX idx_therapist (therapist_gender),
                INDEX idx_created (created_at),
                INDEX idx_subscription_expires (subscription_status, subscription_expires_at)
            );
        """
    }

    /**
     * Convierte a formato para Firebase
     */
    fun toFirebaseMap(): Map<String, Any?> = mapOf(
        "id" to id,
        "email" to email,
        "displayName" to displayName,
        "age" to age,
        "gender" to gender,
        "therapistGender" to therapistGender,
        "interests" to interests,
        "subscriptionStatus" to subscriptionStatus,
        "subscriptionPlatform" to subscriptionPlatform,
        "subscriptionExpiresAt" to subscriptionExpiresAt,
        "personalizedGreeting" to personalizedGreeting,
        "profileImageUrl" to profileImageUrl,
        "timezone" to timezone,
        "language" to language,
        "notificationSettings" to mapOf(
            "pushEnabled" to notificationSettings.pushEnabled,
            "emailEnabled" to notificationSettings.emailEnabled,
            "chatReminders" to notificationSettings.chatReminders,
            "articleRecommendations" to notificationSettings.articleRecommendations,
            "exerciseReminders" to notificationSettings.exerciseReminders,
            "subscriptionUpdates" to notificationSettings.subscriptionUpdates,
            "quietHoursStart" to notificationSettings.quietHoursStart,
            "quietHoursEnd" to notificationSettings.quietHoursEnd
        ),
        "preferences" to mapOf(
            "theme" to preferences.theme,
            "chatStyle" to preferences.chatStyle,
            "exerciseFrequency" to preferences.exerciseFrequency,
            "reminderTime" to preferences.reminderTime,
            "preferredSessionLength" to preferences.preferredSessionLength,
            "autoPlayAudio" to preferences.autoPlayAudio,
            "dataUsageMode" to preferences.dataUsageMode
        ),
        "analytics" to mapOf(
            "totalSessions" to analytics.totalSessions,
            "totalMinutes" to analytics.totalMinutes,
            "streakDays" to analytics.streakDays,
            "longestStreak" to analytics.longestStreak,
            "lastActiveAt" to analytics.lastActiveAt,
            "favoriteExercises" to analytics.favoriteExercises,
            "completedArticles" to analytics.completedArticles,
            "moodHistory" to analytics.moodHistory.map { mood ->
                mapOf(
                    "date" to mood.date,
                    "mood" to mood.mood,
                    "notes" to mood.notes
                )
            }
        ),
        "createdAt" to createdAt,
        "updatedAt" to updatedAt,
        "version" to version,
        "source" to source
    )

    /**
     * Valida el usuario antes de guardarlo
     */
    fun validate(): Result<Unit> {
        return when {
            id.isBlank() -> Result.failure(IllegalArgumentException("User ID cannot be blank"))
            email.isBlank() -> Result.failure(IllegalArgumentException("Email cannot be blank"))
            !android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches() ->
                Result.failure(IllegalArgumentException("Invalid email format"))
            displayName.isBlank() -> Result.failure(IllegalArgumentException("Display name cannot be blank"))
            displayName.length > MAX_DISPLAY_NAME_LENGTH ->
                Result.failure(IllegalArgumentException("Display name too long (max $MAX_DISPLAY_NAME_LENGTH chars)"))
            age != null && (age < MIN_AGE || age > MAX_AGE) ->
                Result.failure(IllegalArgumentException("Age must be between $MIN_AGE and $MAX_AGE"))
            gender != null && gender !in VALID_GENDERS ->
                Result.failure(IllegalArgumentException("Invalid gender: $gender"))
            therapistGender != null && therapistGender !in VALID_THERAPISTS ->
                Result.failure(IllegalArgumentException("Invalid therapist: $therapistGender"))
            subscriptionStatus !in VALID_SUBSCRIPTION_STATUS ->
                Result.failure(IllegalArgumentException("Invalid subscription status: $subscriptionStatus"))
            subscriptionPlatform != null && subscriptionPlatform !in VALID_PLATFORMS ->
                Result.failure(IllegalArgumentException("Invalid platform: $subscriptionPlatform"))
            interests.size > MAX_INTERESTS ->
                Result.failure(IllegalArgumentException("Too many interests (max $MAX_INTERESTS)"))
            createdAt <= 0 -> Result.failure(IllegalArgumentException("Invalid created date"))
            updatedAt <= 0 -> Result.failure(IllegalArgumentException("Invalid updated date"))
            else -> Result.success(Unit)
        }
    }

    /**
     * Verifica si el usuario tiene una suscripción activa
     */
    fun hasActiveSubscription(): Boolean {
        return when {
            subscriptionStatus == "free" -> false
            subscriptionExpiresAt == null -> true // Suscripción sin expiración
            subscriptionExpiresAt > System.currentTimeMillis() -> true
            else -> false
        }
    }

    /**
     * Obtiene el nombre del terapeuta seleccionado
     */
    fun getTherapistName(): String? {
        return when (therapistGender) {
            "aurora" -> "Psicóloga Aurora"
            "alejandro" -> "Psicólogo Alejandro"
            else -> null
        }
    }
}

/**
 * Modelo para la suscripción del usuario
 */
data class Subscription(
    val status: String = "free",
    val plan: String? = null,
    val platform: String? = null,
    val expiresAt: Long? = null,
    val purchaseToken: String? = null,
    val isActive: Boolean = false
) {
    companion object {
        const val STATUS_FREE = "free"
        const val STATUS_ACTIVE = "active"
        const val STATUS_EXPIRED = "expired"
        const val STATUS_CANCELLED = "cancelled"
        
        const val PLATFORM_GOOGLE_PLAY = "google_play"
        const val PLATFORM_PAYPAL = "paypal"
        const val PLATFORM_STRIPE = "stripe"
        
        const val PLAN_MONTHLY = "monthly"
        const val PLAN_YEARLY = "yearly"
    }
}
