package com.menteencalma.app.domain.model

/**
 * Modelo de datos del usuario
 */
data class User(
    val id: String = "",
    val email: String = "",
    val name: String = "",
    val age: Int = 0,
    val interests: List<String> = emptyList(),
    val subscriptionStatus: String = "free", // "free", "premium", "premium_plus"
    val subscriptionPlatform: String? = null, // "google_play", "paypal", "stripe"
    val subscriptionExpiresAt: Long? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * Modelo para la suscripción del usuario
 */
data class Subscription(
    val status: String = "free",
    val plan: String? = null,
    val platform: String? = null,
    val expiresAt: Long? = null,
    val purchaseToken: String? = null,
    val isActive: Boolean = false
) {
    companion object {
        const val STATUS_FREE = "free"
        const val STATUS_ACTIVE = "active"
        const val STATUS_EXPIRED = "expired"
        const val STATUS_CANCELLED = "cancelled"
        
        const val PLATFORM_GOOGLE_PLAY = "google_play"
        const val PLATFORM_PAYPAL = "paypal"
        const val PLATFORM_STRIPE = "stripe"
        
        const val PLAN_MONTHLY = "monthly"
        const val PLAN_YEARLY = "yearly"
    }
}
