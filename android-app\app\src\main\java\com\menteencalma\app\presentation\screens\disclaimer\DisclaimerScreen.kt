package com.menteencalma.app.presentation.screens.disclaimer

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DisclaimerScreen(
    onNavigateBack: () -> Unit
) {
    val scrollState = rememberScrollState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Descargo de Responsabilidad") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Volver")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(scrollState)
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // Warning Header
            DisclaimerHeader()

            // Main Disclaimer Content
            DisclaimerContent()

            // AI Limitations Section
            AILimitationsSection()

            // Professional Help Section
            ProfessionalHelpSection()

            // Emergency Section
            EmergencySection()

            // Data and Privacy
            DataPrivacySection()

            // Contact Information
            ContactSection()
        }
    }
}

@Composable
private fun DisclaimerHeader() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                Icons.Default.Warning,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.onErrorContainer
            )

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "IMPORTANTE: LEE ANTES DE USAR",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onErrorContainer,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Esta aplicación utiliza inteligencia artificial y no reemplaza la atención médica profesional",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onErrorContainer,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun DisclaimerContent() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Descargo de Responsabilidad",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            val disclaimerText = """
                Mente en Calma es una aplicación de bienestar mental que utiliza inteligencia artificial para proporcionar apoyo emocional, recomendaciones de bienestar y contenido educativo. Esta aplicación NO constituye asesoramiento médico, psicológico o terapéutico profesional.

                Al usar esta aplicación, usted reconoce y acepta que:

                • La información y las respuestas proporcionadas por la IA son de naturaleza general y educativa
                • No se debe confiar únicamente en esta aplicación para tomar decisiones relacionadas con la salud mental
                • Los consejos y recomendaciones no reemplazan la consulta con profesionales de la salud mental calificados
                • La aplicación no puede diagnosticar, tratar o curar ninguna condición de salud mental
                • En caso de crisis o emergencia, debe buscar ayuda profesional inmediata
            """.trimIndent()

            Text(
                text = disclaimerText,
                style = MaterialTheme.typography.bodyMedium,
                lineHeight = MaterialTheme.typography.bodyMedium.lineHeight
            )
        }
    }
}

@Composable
private fun AILimitationsSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    Icons.Default.Psychology,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "Limitaciones de la Inteligencia Artificial",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            val limitations = listOf(
                "La IA puede no comprender completamente el contexto emocional complejo",
                "Las respuestas se basan en patrones de datos y no en experiencia clínica real",
                "No puede detectar señales no verbales o matices emocionales sutiles",
                "Puede proporcionar información incorrecta o inapropiada en situaciones específicas",
                "No tiene la capacidad de formar una relación terapéutica real",
                "No puede adaptar el tratamiento basado en la evaluación clínica individual"
            )

            limitations.forEach { limitation ->
                Row(
                    modifier = Modifier.padding(vertical = 4.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    Text(
                        text = "• ",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = limitation,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

@Composable
private fun ProfessionalHelpSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    Icons.Default.LocalHospital,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "Cuándo Buscar Ayuda Profesional",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "Debe buscar ayuda de un profesional de la salud mental si experimenta:",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(8.dp))

            val professionalHelpSigns = listOf(
                "Pensamientos de autolesión o suicidio",
                "Síntomas persistentes de depresión o ansiedad",
                "Dificultades significativas en el funcionamiento diario",
                "Abuso de sustancias",
                "Trauma o experiencias traumáticas",
                "Problemas de relación graves",
                "Cambios drásticos en el comportamiento o personalidad",
                "Síntomas que interfieren con el trabajo, escuela o relaciones"
            )

            professionalHelpSigns.forEach { sign ->
                Row(
                    modifier = Modifier.padding(vertical = 2.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    Text(
                        text = "• ",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Text(
                        text = sign,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

@Composable
private fun EmergencySection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFFEBEE)
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    Icons.Default.Emergency,
                    contentDescription = null,
                    tint = Color(0xFFD32F2F)
                )
                Text(
                    text = "EN CASO DE EMERGENCIA",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFFD32F2F)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "Si usted o alguien que conoce está en peligro inmediato o tiene pensamientos suicidas:",
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFFD32F2F),
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(8.dp))

            val emergencyActions = listOf(
                "🚨 Llame al 911 inmediatamente",
                "📞 Contacte la línea nacional de prevención del suicidio: 988",
                "💬 Envíe un mensaje de texto con 'HELLO' al 741741",
                "🏥 Vaya a la sala de emergencias más cercana",
                "👥 No se quede solo, busque a alguien de confianza"
            )

            emergencyActions.forEach { action ->
                Text(
                    text = action,
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFFD32F2F),
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
        }
    }
}

@Composable
private fun DataPrivacySection() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    Icons.Default.Security,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "Privacidad y Datos",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            val privacyText = """
                • Sus conversaciones y datos personales se manejan con estricta confidencialidad
                • Los datos se cifran y almacenan de forma segura
                • No compartimos información personal con terceros sin su consentimiento
                • Puede solicitar la eliminación de sus datos en cualquier momento
                • Consulte nuestra Política de Privacidad para más detalles
            """.trimIndent()

            Text(
                text = privacyText,
                style = MaterialTheme.typography.bodyMedium,
                lineHeight = MaterialTheme.typography.bodyMedium.lineHeight
            )
        }
    }
}

@Composable
private fun ContactSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    Icons.Default.ContactSupport,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSecondaryContainer
                )
                Text(
                    text = "Contacto y Soporte",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "Si tiene preguntas sobre este descargo de responsabilidad o sobre el uso de la aplicación, puede contactarnos:",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSecondaryContainer
            )

            Spacer(modifier = Modifier.height(8.dp))

            val contactInfo = listOf(
                "📧 Email: <EMAIL>",
                "🌐 Web: www.menteencalma.com",
                "📱 Soporte en la app: Configuración > Ayuda"
            )

            contactInfo.forEach { contact ->
                Text(
                    text = contact,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSecondaryContainer,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "Última actualización: ${java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale.getDefault()).format(java.util.Date())}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSecondaryContainer,
                fontWeight = FontWeight.Medium
            )
        }
    }
}
