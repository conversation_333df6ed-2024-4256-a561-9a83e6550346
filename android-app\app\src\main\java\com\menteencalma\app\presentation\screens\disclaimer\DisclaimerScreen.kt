package com.menteencalma.app.presentation.screens.disclaimer

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DisclaimerScreen(
    onNavigateBack: () -> Unit
) {
    val scrollState = rememberScrollState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Descargo de Responsabilidad") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Volver")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(scrollState)
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // Warning Header
            DisclaimerHeader()

            // Main Disclaimer Content
            DisclaimerContent()

            // AI as Support Tool
            AIToolSection()

            // Not Professional Help
            NotProfessionalHelpSection()

            // AI Limitations Section
            AILimitationsSection()

            // User Responsibility
            UserResponsibilitySection()

            // Emergency Section
            EmergencySection()

            // Contact Information
            ContactSection()
        }
    }
}

@Composable
private fun DisclaimerHeader() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                Icons.Default.Warning,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.onErrorContainer
            )

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "IMPORTANTE: LEE ANTES DE USAR",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onErrorContainer,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Esta aplicación utiliza inteligencia artificial y no reemplaza la atención médica profesional",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onErrorContainer,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun DisclaimerContent() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Descargo de Responsabilidad Importante",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            Text(
                text = "Por favor, lee esta información cuidadosamente sobre el uso de Mente en calma y sus funciones de IA.",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }
    }
}

@Composable
private fun AIToolSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    Icons.Default.Psychology,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "La IA como Herramienta de Apoyo",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            val aiToolText = """
                Mente en calma utiliza Inteligencia Artificial (IA) avanzada, incluyendo Gemini AI, para proporcionar apoyo psicológico, recomendaciones personalizadas e información sobre temas de salud mental. Nuestra IA está diseñada para ofrecer orientación basada en principios de la Terapia Cognitivo-Conductual (TCC), mindfulness y psicología positiva.

                El chatbot de IA y otras funciones impulsadas por IA están destinadas a ser herramientas de apoyo. Pueden ayudarte a explorar tus pensamientos y sentimientos, aprender estrategias de afrontamiento y obtener conocimientos sobre el bienestar mental.
            """.trimIndent()

            Text(
                text = aiToolText,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer,
                lineHeight = MaterialTheme.typography.bodyMedium.lineHeight
            )
        }
    }
}

@Composable
private fun NotProfessionalHelpSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onErrorContainer
                )
                Text(
                    text = "No Reemplaza la Ayuda Profesional",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            val notProfessionalText = """
                Mente en calma y sus funciones de IA no son un sustituto del consejo, diagnóstico o tratamiento médico profesional. Busca siempre el consejo de tu médico u otro proveedor de salud calificado con cualquier pregunta que puedas tener respecto a una condición médica o preocupaciones de salud mental. Nunca ignores el consejo médico profesional ni demores en buscarlo debido a algo que hayas leído o con lo que hayas interactuado en esta aplicación.

                Si estás experimentando una crisis de salud mental, o si estás en peligro, por favor contacta a los servicios de emergencia (ej., llama al 911 o a tu número de emergencia local) o a una línea de crisis inmediatamente.
            """.trimIndent()

            Text(
                text = notProfessionalText,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onErrorContainer,
                lineHeight = MaterialTheme.typography.bodyMedium.lineHeight
            )
        }
    }
}

@Composable
private fun AILimitationsSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    Icons.Default.Psychology,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "Limitaciones de la IA",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            val limitationsText = """
                Aunque nuestra IA se esfuerza por proporcionar información útil y relevante, tiene limitaciones. Las respuestas de la IA se generan basándose en patrones en los datos y conocimiento preprogramado. Una IA no puede comprender completamente la complejidad de las emociones humanas o las circunstancias vitales individuales de la misma manera que un profesional humano. Puede haber casos en los que las respuestas de la IA no se adapten perfectamente a tu situación única.

                El "razonamiento" de la IA es un proceso computacional. Aunque su objetivo es tomar decisiones informadas sobre cuándo y cómo incorporar información, no posee conciencia ni intuición humanas.
            """.trimIndent()

            Text(
                text = limitationsText,
                style = MaterialTheme.typography.bodyMedium,
                lineHeight = MaterialTheme.typography.bodyMedium.lineHeight
            )
        }
    }
}

@Composable
private fun UserResponsibilitySection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    Icons.Default.Person,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSecondaryContainer
                )
                Text(
                    text = "Tu Responsabilidad",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            val responsibilityText = """
                Al usar Mente en calma, reconoces y aceptas que eres responsable de tus propias acciones y decisiones. La información y el apoyo proporcionados por esta aplicación son solo para fines informativos y educativos generales.

                Te animamos a usar Mente en calma como una herramienta complementaria junto con la atención profesional si es necesario, y a ejercer siempre tu propio juicio al considerar cualquier información o recomendación proporcionada.
            """.trimIndent()

            Text(
                text = responsibilityText,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSecondaryContainer,
                lineHeight = MaterialTheme.typography.bodyMedium.lineHeight
            )
        }
    }
}

@Composable
private fun EmergencySection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFFEBEE)
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    Icons.Default.Emergency,
                    contentDescription = null,
                    tint = Color(0xFFD32F2F)
                )
                Text(
                    text = "EN CASO DE EMERGENCIA",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFFD32F2F)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "Si usted o alguien que conoce está en peligro inmediato o tiene pensamientos suicidas:",
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFFD32F2F),
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(8.dp))

            val emergencyActions = listOf(
                "🚨 Llame al 911 inmediatamente",
                "📞 Contacte la línea nacional de prevención del suicidio: 988",
                "💬 Envíe un mensaje de texto con 'HELLO' al 741741",
                "🏥 Vaya a la sala de emergencias más cercana",
                "👥 No se quede solo, busque a alguien de confianza"
            )

            emergencyActions.forEach { action ->
                Text(
                    text = action,
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFFD32F2F),
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
        }
    }
}



@Composable
private fun ContactSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                Icons.Default.Info,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(32.dp)
            )

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "Mente en Calma",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Aplicación de bienestar mental con IA",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "Última actualización: ${java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale.getDefault()).format(java.util.Date())}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center
            )
        }
    }
}
