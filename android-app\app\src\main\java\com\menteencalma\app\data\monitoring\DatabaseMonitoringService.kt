package com.menteencalma.app.data.monitoring

import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Servicio para monitorear costos y performance de la base de datos
 * Útil para detectar cuándo migrar de Firebase a otras soluciones
 */
@Singleton
class DatabaseMonitoringService @Inject constructor() {

    private val _metrics = MutableStateFlow(DatabaseMetrics())
    val metrics: StateFlow<DatabaseMetrics> = _metrics.asStateFlow()

    private val _alerts = MutableStateFlow<List<DatabaseAlert>>(emptyList())
    val alerts: StateFlow<List<DatabaseAlert>> = _alerts.asStateFlow()

    companion object {
        private const val TAG = "DatabaseMonitoring"
        
        // Umbrales para alertas
        private const val HIGH_READ_THRESHOLD = 1000 // reads por minuto
        private const val HIGH_WRITE_THRESHOLD = 500 // writes por minuto
        private const val HIGH_LATENCY_THRESHOLD = 2000L // ms
        private const val ESTIMATED_COST_THRESHOLD = 50.0 // USD por mes
    }

    /**
     * Registra una operación de lectura
     */
    fun recordRead(collection: String, latency: Long, documentsRead: Int = 1) {
        val currentMetrics = _metrics.value
        val updatedMetrics = currentMetrics.copy(
            totalReads = currentMetrics.totalReads + documentsRead,
            avgReadLatency = calculateNewAverage(
                currentMetrics.avgReadLatency,
                latency,
                currentMetrics.totalReads
            ),
            readsByCollection = currentMetrics.readsByCollection.toMutableMap().apply {
                this[collection] = (this[collection] ?: 0) + documentsRead
            }
        )
        
        _metrics.value = updatedMetrics
        checkThresholds(updatedMetrics)
        
        Log.d(TAG, "Read operation: collection=$collection, latency=${latency}ms, docs=$documentsRead")
    }

    /**
     * Registra una operación de escritura
     */
    fun recordWrite(collection: String, latency: Long, documentsWritten: Int = 1) {
        val currentMetrics = _metrics.value
        val updatedMetrics = currentMetrics.copy(
            totalWrites = currentMetrics.totalWrites + documentsWritten,
            avgWriteLatency = calculateNewAverage(
                currentMetrics.avgWriteLatency,
                latency,
                currentMetrics.totalWrites
            ),
            writesByCollection = currentMetrics.writesByCollection.toMutableMap().apply {
                this[collection] = (this[collection] ?: 0) + documentsWritten
            }
        )
        
        _metrics.value = updatedMetrics
        checkThresholds(updatedMetrics)
        
        Log.d(TAG, "Write operation: collection=$collection, latency=${latency}ms, docs=$documentsWritten")
    }

    /**
     * Registra una operación fallida
     */
    fun recordError(operation: String, error: String, collection: String? = null) {
        val currentMetrics = _metrics.value
        val updatedMetrics = currentMetrics.copy(
            totalErrors = currentMetrics.totalErrors + 1,
            errorsByType = currentMetrics.errorsByType.toMutableMap().apply {
                this[error] = (this[error] ?: 0) + 1
            }
        )
        
        _metrics.value = updatedMetrics
        
        // Crear alerta de error
        val alert = DatabaseAlert(
            type = AlertType.ERROR,
            message = "Database error in $operation: $error",
            timestamp = System.currentTimeMillis(),
            metadata = mapOf(
                "operation" to operation,
                "error" to error,
                "collection" to (collection ?: "unknown")
            )
        )
        
        addAlert(alert)
        
        Log.e(TAG, "Database error: operation=$operation, error=$error, collection=$collection")
    }

    /**
     * Calcula el costo estimado mensual basado en las operaciones actuales
     */
    fun getEstimatedMonthlyCost(): Double {
        val metrics = _metrics.value
        
        // Precios de Firestore (aproximados)
        val readCostPer100k = 0.36 // USD
        val writeCostPer100k = 1.08 // USD
        val deleteCostPer100k = 0.12 // USD
        
        // Proyectar a un mes (30 días)
        val dailyReads = metrics.totalReads.toDouble()
        val dailyWrites = metrics.totalWrites.toDouble()
        
        val monthlyReads = dailyReads * 30
        val monthlyWrites = dailyWrites * 30
        
        val readCost = (monthlyReads / 100000) * readCostPer100k
        val writeCost = (monthlyWrites / 100000) * writeCostPer100k
        
        return readCost + writeCost
    }

    /**
     * Obtiene recomendaciones de optimización
     */
    fun getOptimizationRecommendations(): List<OptimizationRecommendation> {
        val metrics = _metrics.value
        val recommendations = mutableListOf<OptimizationRecommendation>()
        
        // Verificar latencia alta
        if (metrics.avgReadLatency > HIGH_LATENCY_THRESHOLD) {
            recommendations.add(
                OptimizationRecommendation(
                    type = RecommendationType.PERFORMANCE,
                    title = "Alta latencia en lecturas",
                    description = "La latencia promedio de lectura es ${metrics.avgReadLatency}ms. Considera usar caché local o índices compuestos.",
                    priority = Priority.HIGH
                )
            )
        }
        
        // Verificar muchas lecturas en una colección
        val topReadCollection = metrics.readsByCollection.maxByOrNull { it.value }
        if (topReadCollection != null && topReadCollection.value > 1000) {
            recommendations.add(
                OptimizationRecommendation(
                    type = RecommendationType.COST,
                    title = "Muchas lecturas en ${topReadCollection.key}",
                    description = "La colección ${topReadCollection.key} tiene ${topReadCollection.value} lecturas. Considera implementar caché o paginación.",
                    priority = Priority.MEDIUM
                )
            )
        }
        
        // Verificar costo estimado alto
        val estimatedCost = getEstimatedMonthlyCost()
        if (estimatedCost > ESTIMATED_COST_THRESHOLD) {
            recommendations.add(
                OptimizationRecommendation(
                    type = RecommendationType.MIGRATION,
                    title = "Costo mensual estimado alto",
                    description = "El costo estimado es $${String.format("%.2f", estimatedCost)}/mes. Considera migrar a PostgreSQL/Supabase.",
                    priority = Priority.HIGH
                )
            )
        }
        
        // Verificar muchos errores
        if (metrics.totalErrors > 10) {
            recommendations.add(
                OptimizationRecommendation(
                    type = RecommendationType.RELIABILITY,
                    title = "Muchos errores de base de datos",
                    description = "Se han registrado ${metrics.totalErrors} errores. Revisa la conectividad y las reglas de seguridad.",
                    priority = Priority.HIGH
                )
            )
        }
        
        return recommendations
    }

    /**
     * Resetea las métricas (útil para testing o nuevos períodos)
     */
    fun resetMetrics() {
        _metrics.value = DatabaseMetrics()
        _alerts.value = emptyList()
        Log.i(TAG, "Database metrics reset")
    }

    private fun calculateNewAverage(currentAvg: Long, newValue: Long, totalCount: Int): Long {
        return if (totalCount == 0) newValue
        else ((currentAvg * (totalCount - 1)) + newValue) / totalCount
    }

    private fun checkThresholds(metrics: DatabaseMetrics) {
        val currentTime = System.currentTimeMillis()
        val alerts = mutableListOf<DatabaseAlert>()
        
        // Verificar umbral de lecturas
        if (metrics.totalReads > HIGH_READ_THRESHOLD) {
            alerts.add(
                DatabaseAlert(
                    type = AlertType.HIGH_USAGE,
                    message = "High read volume: ${metrics.totalReads} reads",
                    timestamp = currentTime
                )
            )
        }
        
        // Verificar umbral de escrituras
        if (metrics.totalWrites > HIGH_WRITE_THRESHOLD) {
            alerts.add(
                DatabaseAlert(
                    type = AlertType.HIGH_USAGE,
                    message = "High write volume: ${metrics.totalWrites} writes",
                    timestamp = currentTime
                )
            )
        }
        
        // Verificar latencia alta
        if (metrics.avgReadLatency > HIGH_LATENCY_THRESHOLD) {
            alerts.add(
                DatabaseAlert(
                    type = AlertType.PERFORMANCE,
                    message = "High read latency: ${metrics.avgReadLatency}ms",
                    timestamp = currentTime
                )
            )
        }
        
        alerts.forEach { addAlert(it) }
    }

    private fun addAlert(alert: DatabaseAlert) {
        val currentAlerts = _alerts.value.toMutableList()
        currentAlerts.add(alert)
        
        // Mantener solo las últimas 50 alertas
        if (currentAlerts.size > 50) {
            currentAlerts.removeAt(0)
        }
        
        _alerts.value = currentAlerts
    }
}

/**
 * Métricas de la base de datos
 */
data class DatabaseMetrics(
    val totalReads: Int = 0,
    val totalWrites: Int = 0,
    val totalErrors: Int = 0,
    val avgReadLatency: Long = 0,
    val avgWriteLatency: Long = 0,
    val readsByCollection: Map<String, Int> = emptyMap(),
    val writesByCollection: Map<String, Int> = emptyMap(),
    val errorsByType: Map<String, Int> = emptyMap(),
    val sessionStartTime: Long = System.currentTimeMillis()
)

/**
 * Alerta de la base de datos
 */
data class DatabaseAlert(
    val type: AlertType,
    val message: String,
    val timestamp: Long,
    val metadata: Map<String, String> = emptyMap()
)

enum class AlertType {
    HIGH_USAGE,
    PERFORMANCE,
    ERROR,
    COST
}

/**
 * Recomendación de optimización
 */
data class OptimizationRecommendation(
    val type: RecommendationType,
    val title: String,
    val description: String,
    val priority: Priority
)

enum class RecommendationType {
    PERFORMANCE,
    COST,
    MIGRATION,
    RELIABILITY
}

enum class Priority {
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL
}
