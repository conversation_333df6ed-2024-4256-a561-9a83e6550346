package com.menteencalma.app.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.menteencalma.app.presentation.screens.articles.ArticleDetailScreen
import com.menteencalma.app.presentation.screens.articles.ArticlesScreen
import com.menteencalma.app.presentation.screens.auth.CreateProfileScreen
import com.menteencalma.app.presentation.screens.auth.LoginScreen
import com.menteencalma.app.presentation.screens.chat.ChatScreen
import com.menteencalma.app.presentation.screens.profile.ProfileScreen
import com.menteencalma.app.presentation.screens.recommendations.RecommendationsScreen
import com.menteencalma.app.presentation.screens.splash.SplashScreen
import com.menteencalma.app.presentation.screens.subscription.SubscribeScreen
import com.menteencalma.app.presentation.viewmodels.AuthViewModel

@Composable
fun MenteEnCalmaNavigation(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    authViewModel: AuthViewModel = hiltViewModel()
) {
    val authState by authViewModel.authState.collectAsState()
    
    NavHost(
        navController = navController,
        startDestination = Screen.Splash.route,
        modifier = modifier
    ) {
        // Pantalla de Splash/Loading
        composable(Screen.Splash.route) {
            SplashScreen(
                onNavigateToLogin = {
                    navController.navigate(Screen.Login.route) {
                        popUpTo(Screen.Splash.route) { inclusive = true }
                    }
                },
                onNavigateToCreateProfile = {
                    navController.navigate(Screen.CreateProfile.route) {
                        popUpTo(Screen.Splash.route) { inclusive = true }
                    }
                },
                onNavigateToMain = {
                    navController.navigate(Screen.Chat.route) {
                        popUpTo(Screen.Splash.route) { inclusive = true }
                    }
                }
            )
        }
        
        // Pantallas de Autenticación
        composable(Screen.Login.route) {
            LoginScreen(
                onNavigateToCreateProfile = {
                    navController.navigate(Screen.CreateProfile.route)
                },
                onLoginSuccess = {
                    navController.navigate(Screen.Chat.route) {
                        popUpTo(Screen.Login.route) { inclusive = true }
                    }
                }
            )
        }
        
        composable(Screen.CreateProfile.route) {
            CreateProfileScreen(
                onProfileCreated = {
                    navController.navigate(Screen.Chat.route) {
                        popUpTo(Screen.CreateProfile.route) { inclusive = true }
                    }
                },
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }
        
        // Pantallas Principales (con Bottom Navigation)
        composable(Screen.Chat.route) {
            ChatScreen(
                onNavigateToSubscribe = {
                    navController.navigate(Screen.Subscribe.route)
                }
            )
        }
        
        composable(Screen.Recommendations.route) {
            RecommendationsScreen(
                onNavigateToSubscribe = {
                    navController.navigate(Screen.Subscribe.route)
                }
            )
        }
        
        composable(Screen.Articles.route) {
            ArticlesScreen(
                onNavigateToArticleDetail = { articleId ->
                    navController.navigate(Screen.ArticleDetail.createRoute(articleId))
                },
                onNavigateToSubscribe = {
                    navController.navigate(Screen.Subscribe.route)
                }
            )
        }
        
        composable(Screen.Profile.route) {
            ProfileScreen(
                onNavigateToSubscribe = {
                    navController.navigate(Screen.Subscribe.route)
                },
                onNavigateToSettings = {
                    navController.navigate(Screen.Settings.route)
                },
                onLogout = {
                    navController.navigate(Screen.Login.route) {
                        popUpTo(0) { inclusive = true }
                    }
                }
            )
        }
        
        // Pantallas Adicionales
        composable(Screen.Subscribe.route) {
            SubscribeScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onSubscriptionSuccess = {
                    navController.popBackStack()
                }
            )
        }
        
        composable(Screen.ArticleDetail.route) { backStackEntry ->
            val articleId = backStackEntry.arguments?.getString("articleId") ?: ""
            ArticleDetailScreen(
                articleId = articleId,
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }
    }
}
