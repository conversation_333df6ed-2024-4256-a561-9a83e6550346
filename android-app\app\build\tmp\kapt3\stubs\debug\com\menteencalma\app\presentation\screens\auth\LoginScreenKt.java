package com.menteencalma.app.presentation.screens.auth;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000>\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a.\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u0007\u001aF\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0018\u0010\n\u001a\u0014\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u000b2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0003\u001a\b\u0010\u000f\u001a\u00020\u0001H\u0003\u001aL\u0010\u0010\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u001e\u0010\u0011\u001a\u001a\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u00122\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0003\u001a$\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\u00152\u0012\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00010\u0017H\u0003\u00a8\u0006\u0018"}, d2 = {"LoginScreen", "", "onNavigateToCreateProfile", "Lkotlin/Function0;", "onLoginSuccess", "loginViewModel", "Lcom/menteencalma/app/presentation/viewmodels/LoginViewModel;", "LoginTabContent", "uiState", "Lcom/menteencalma/app/presentation/viewmodels/LoginUiState;", "onSignIn", "Lkotlin/Function2;", "", "onSignInWithGoogle", "onClearError", "LogoSection", "RegisterTabContent", "onSignUp", "Lkotlin/Function3;", "TabSection", "selectedTab", "", "onTabSelected", "Lkotlin/Function1;", "app_debug"})
public final class LoginScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void LoginScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToCreateProfile, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onLoginSuccess, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.presentation.viewmodels.LoginViewModel loginViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void LogoSection() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TabSection(int selectedTab, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onTabSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void LoginTabContent(com.menteencalma.app.presentation.viewmodels.LoginUiState uiState, kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSignIn, kotlin.jvm.functions.Function0<kotlin.Unit> onSignInWithGoogle, kotlin.jvm.functions.Function0<kotlin.Unit> onClearError) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void RegisterTabContent(com.menteencalma.app.presentation.viewmodels.LoginUiState uiState, kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSignUp, kotlin.jvm.functions.Function0<kotlin.Unit> onSignInWithGoogle, kotlin.jvm.functions.Function0<kotlin.Unit> onClearError) {
    }
}