package com.menteencalma.app.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b&\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0013\u0010\u0005\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0013\u0010\u0007\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0013\u0010\t\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\"\u0013\u0010\u000b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\f\u0010\u0003\"\u0013\u0010\r\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u000e\u0010\u0003\"\u0013\u0010\u000f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0010\u0010\u0003\"\u0013\u0010\u0011\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0012\u0010\u0003\"\u0013\u0010\u0013\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0014\u0010\u0003\"\u0013\u0010\u0015\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0016\u0010\u0003\"\u0013\u0010\u0017\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0018\u0010\u0003\"\u0013\u0010\u0019\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001a\u0010\u0003\"\u0013\u0010\u001b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001c\u0010\u0003\"\u0013\u0010\u001d\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001e\u0010\u0003\"\u0013\u0010\u001f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b \u0010\u0003\"\u0013\u0010!\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\"\u0010\u0003\"\u0013\u0010#\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b$\u0010\u0003\"\u0013\u0010%\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b&\u0010\u0003\u00a8\u0006\'"}, d2 = {"MenteEnCalmaBackgroundDark", "Landroidx/compose/ui/graphics/Color;", "getMenteEnCalmaBackgroundDark", "()J", "J", "MenteEnCalmaBackgroundLight", "getMenteEnCalmaBackgroundLight", "MenteEnCalmaError", "getMenteEnCalmaError", "MenteEnCalmaInfo", "getMenteEnCalmaInfo", "MenteEnCalmaOnBackgroundDark", "getMenteEnCalmaOnBackgroundDark", "MenteEnCalmaOnBackgroundLight", "getMenteEnCalmaOnBackgroundLight", "MenteEnCalmaOnPrimary", "getMenteEnCalmaOnPrimary", "MenteEnCalmaOnSecondary", "getMenteEnCalmaOnSecondary", "MenteEnCalmaOnSurfaceDark", "getMenteEnCalmaOnSurfaceDark", "MenteEnCalmaOnSurfaceLight", "getMenteEnCalmaOnSurfaceLight", "MenteEnCalmaOnTertiary", "getMenteEnCalmaOnTertiary", "MenteEnCalmaPrimary", "getMenteEnCalmaPrimary", "MenteEnCalmaSecondary", "getMenteEnCalmaSecondary", "MenteEnCalmaSuccess", "getMenteEnCalmaSuccess", "MenteEnCalmaSurfaceDark", "getMenteEnCalmaSurfaceDark", "MenteEnCalmaSurfaceLight", "getMenteEnCalmaSurfaceLight", "MenteEnCalmaTertiary", "getMenteEnCalmaTertiary", "MenteEnCalmaWarning", "getMenteEnCalmaWarning", "app_debug"})
public final class ColorKt {
    private static final long MenteEnCalmaPrimary = 0L;
    private static final long MenteEnCalmaSecondary = 0L;
    private static final long MenteEnCalmaTertiary = 0L;
    private static final long MenteEnCalmaBackgroundLight = 0L;
    private static final long MenteEnCalmaBackgroundDark = 0L;
    private static final long MenteEnCalmaSurfaceLight = 0L;
    private static final long MenteEnCalmaSurfaceDark = 0L;
    private static final long MenteEnCalmaOnPrimary = 0L;
    private static final long MenteEnCalmaOnSecondary = 0L;
    private static final long MenteEnCalmaOnTertiary = 0L;
    private static final long MenteEnCalmaOnBackgroundLight = 0L;
    private static final long MenteEnCalmaOnBackgroundDark = 0L;
    private static final long MenteEnCalmaOnSurfaceLight = 0L;
    private static final long MenteEnCalmaOnSurfaceDark = 0L;
    private static final long MenteEnCalmaSuccess = 0L;
    private static final long MenteEnCalmaWarning = 0L;
    private static final long MenteEnCalmaError = 0L;
    private static final long MenteEnCalmaInfo = 0L;
    
    public static final long getMenteEnCalmaPrimary() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaSecondary() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaTertiary() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaBackgroundLight() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaBackgroundDark() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaSurfaceLight() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaSurfaceDark() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaOnPrimary() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaOnSecondary() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaOnTertiary() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaOnBackgroundLight() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaOnBackgroundDark() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaOnSurfaceLight() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaOnSurfaceDark() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaSuccess() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaWarning() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaError() {
        return 0L;
    }
    
    public static final long getMenteEnCalmaInfo() {
        return 0L;
    }
}