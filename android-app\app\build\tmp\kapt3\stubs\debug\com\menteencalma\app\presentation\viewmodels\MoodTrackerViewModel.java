package com.menteencalma.app.presentation.viewmodels;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0010\u0007\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\u0012\u001a\u00020\u00132\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00160\u0015H\u0002J\u0006\u0010\u0017\u001a\u00020\u0018J\u0006\u0010\u0019\u001a\u00020\u0018J\u000e\u0010\u001a\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001cJ\u0018\u0010\u001d\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u001f\u0012\u0004\u0012\u00020 0\u001e0\u0015J\u0014\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00160\u00152\u0006\u0010\"\u001a\u00020\u001cJ\b\u0010#\u001a\u00020\u0018H\u0002J\b\u0010$\u001a\u00020\u0018H\u0002J\u001c\u0010%\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00150&2\u0006\u0010\'\u001a\u00020\u001cH\u0002J\b\u0010(\u001a\u00020\u0018H\u0014J\u0006\u0010)\u001a\u00020\u0018J\u000e\u0010*\u001a\u00020\u00182\u0006\u0010+\u001a\u00020\u001cJ\u000e\u0010,\u001a\u00020\u00182\u0006\u0010-\u001a\u00020\u001cJ\u000e\u0010.\u001a\u00020\u00182\u0006\u0010/\u001a\u00020\u001cJ\u000e\u00100\u001a\u00020\u00182\u0006\u00101\u001a\u000202J\u000e\u00103\u001a\u00020\u00182\u0006\u00104\u001a\u000205R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\u00a8\u00066"}, d2 = {"Lcom/menteencalma/app/presentation/viewmodels/MoodTrackerViewModel;", "Landroidx/lifecycle/ViewModel;", "databaseRepository", "Lcom/menteencalma/app/domain/repository/DatabaseRepository;", "authRepository", "Lcom/menteencalma/app/domain/repository/AuthRepository;", "firestore", "Lcom/google/firebase/firestore/FirebaseFirestore;", "(Lcom/menteencalma/app/domain/repository/DatabaseRepository;Lcom/menteencalma/app/domain/repository/AuthRepository;Lcom/google/firebase/firestore/FirebaseFirestore;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/menteencalma/app/presentation/viewmodels/MoodTrackerUiState;", "moodEntriesListener", "Lcom/google/firebase/firestore/ListenerRegistration;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "calculateAnalytics", "Lcom/menteencalma/app/presentation/viewmodels/MoodAnalytics;", "entries", "", "Lcom/menteencalma/app/domain/model/MoodEntry;", "clearError", "", "clearSaveSuccess", "deleteMoodEntry", "entryId", "", "getChartData", "Lkotlin/Pair;", "", "", "getEntriesForDate", "date", "loadCurrentUser", "observeMoodEntries", "observeMoodEntriesFlow", "Lkotlinx/coroutines/flow/Flow;", "userId", "onCleared", "saveMoodEntry", "toggleActivity", "activity", "toggleTag", "tag", "updateDescription", "description", "updateIntensity", "intensity", "", "updateSelectedMood", "mood", "Lcom/menteencalma/app/domain/model/MoodEntry$MoodType;", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class MoodTrackerViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.repository.DatabaseRepository databaseRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.repository.AuthRepository authRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.firebase.firestore.FirebaseFirestore firestore = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.menteencalma.app.presentation.viewmodels.MoodTrackerUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.viewmodels.MoodTrackerUiState> uiState = null;
    @org.jetbrains.annotations.Nullable()
    private com.google.firebase.firestore.ListenerRegistration moodEntriesListener;
    
    @javax.inject.Inject()
    public MoodTrackerViewModel(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.DatabaseRepository databaseRepository, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.AuthRepository authRepository, @org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.viewmodels.MoodTrackerUiState> getUiState() {
        return null;
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
    
    private final void loadCurrentUser() {
    }
    
    private final void observeMoodEntries() {
    }
    
    private final kotlinx.coroutines.flow.Flow<java.util.List<com.menteencalma.app.domain.model.MoodEntry>> observeMoodEntriesFlow(java.lang.String userId) {
        return null;
    }
    
    public final void updateSelectedMood(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.model.MoodEntry.MoodType mood) {
    }
    
    public final void updateIntensity(int intensity) {
    }
    
    public final void updateDescription(@org.jetbrains.annotations.NotNull()
    java.lang.String description) {
    }
    
    public final void toggleTag(@org.jetbrains.annotations.NotNull()
    java.lang.String tag) {
    }
    
    public final void toggleActivity(@org.jetbrains.annotations.NotNull()
    java.lang.String activity) {
    }
    
    public final void saveMoodEntry() {
    }
    
    public final void deleteMoodEntry(@org.jetbrains.annotations.NotNull()
    java.lang.String entryId) {
    }
    
    public final void clearError() {
    }
    
    public final void clearSaveSuccess() {
    }
    
    private final com.menteencalma.app.presentation.viewmodels.MoodAnalytics calculateAnalytics(java.util.List<com.menteencalma.app.domain.model.MoodEntry> entries) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<kotlin.Pair<java.lang.Long, java.lang.Float>> getChartData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.domain.model.MoodEntry> getEntriesForDate(@org.jetbrains.annotations.NotNull()
    java.lang.String date) {
        return null;
    }
}