{"logs": [{"outputFile": "com.menteencalma.app-mergeDebugResources-3:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ec6bd2faa3e0a998fc0a19c7dd0c22ed\\transformed\\browser-1.4.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,269,385", "endColumns": "113,99,115,100", "endOffsets": "164,264,380,481"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "3267,3666,3766,3882", "endColumns": "113,99,115,100", "endOffsets": "3376,3761,3877,3978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\b7c5e14f30f524dc22cc8925fd453a32\\transformed\\core-1.12.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "2,3,4,5,6,7,8,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,407,513,614,722,11023", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "198,301,402,508,609,717,845,11119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\bc87254a1d5a4bb9d2cd1a5740dba329\\transformed\\play-services-base-18.1.0\\res\\values-kn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,466,595,706,845,970,1074,1169,1315,1424,1585,1716,1857,2010,2075,2134", "endColumns": "106,165,128,110,138,124,103,94,145,108,160,130,140,152,64,58,80", "endOffsets": "299,465,594,705,844,969,1073,1168,1314,1423,1584,1715,1856,2009,2074,2133,2214"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1031,1142,1312,1445,1560,1703,1832,1940,2185,2335,2448,2613,2748,2893,3050,3119,3182", "endColumns": "110,169,132,114,142,128,107,98,149,112,164,134,144,156,68,62,84", "endOffsets": "1137,1307,1440,1555,1698,1827,1935,2034,2330,2443,2608,2743,2888,3045,3114,3177,3262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d60095cfcd5f6da5b72a1052224dd592\\transformed\\ui-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,995,1066,1148,1234,1313,1390,1459", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,990,1061,1143,1229,1308,1385,1454,1572"}, "to": {"startLines": "9,10,30,31,32,36,37,94,95,96,97,98,99,100,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "850,947,3381,3477,3577,3983,4067,10458,10549,10634,10705,10776,10858,10944,11124,11201,11270", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "942,1026,3472,3572,3661,4062,4155,10544,10629,10700,10771,10853,10939,11018,11196,11265,11383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ca611de3937d8b7fad81038213315e33\\transformed\\foundation-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "105,106", "startColumns": "4,4", "startOffsets": "11388,11476", "endColumns": "87,94", "endOffsets": "11471,11566"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\0a2f82cac04b16c6074a060829451b01\\transformed\\play-services-basement-18.2.0\\res\\values-kn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2039", "endColumns": "145", "endOffsets": "2180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\98bcb59e84dea2106bcefc907e055731\\transformed\\material3-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,425,548,647,745,860,1017,1147,1299,1385,1491,1587,1689,1805,1938,2049,2188,2323,2456,2634,2758,2876,2997,3124,3221,3318,3440,3578,3684,3793,3899,4038,4183,4293,4402,4478,4578,4678,4765,4854,4965,5045,5129,5229,5337,5437,5538,5625,5738,5840,5945,6066,6146,6256", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "174,298,420,543,642,740,855,1012,1142,1294,1380,1486,1582,1684,1800,1933,2044,2183,2318,2451,2629,2753,2871,2992,3119,3216,3313,3435,3573,3679,3788,3894,4033,4178,4288,4397,4473,4573,4673,4760,4849,4960,5040,5124,5224,5332,5432,5533,5620,5733,5835,5940,6061,6141,6251,6348"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4160,4284,4408,4530,4653,4752,4850,4965,5122,5252,5404,5490,5596,5692,5794,5910,6043,6154,6293,6428,6561,6739,6863,6981,7102,7229,7326,7423,7545,7683,7789,7898,8004,8143,8288,8398,8507,8583,8683,8783,8870,8959,9070,9150,9234,9334,9442,9542,9643,9730,9843,9945,10050,10171,10251,10361", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "4279,4403,4525,4648,4747,4845,4960,5117,5247,5399,5485,5591,5687,5789,5905,6038,6149,6288,6423,6556,6734,6858,6976,7097,7224,7321,7418,7540,7678,7784,7893,7999,8138,8283,8393,8502,8578,8678,8778,8865,8954,9065,9145,9229,9329,9437,9537,9638,9725,9838,9940,10045,10166,10246,10356,10453"}}]}]}