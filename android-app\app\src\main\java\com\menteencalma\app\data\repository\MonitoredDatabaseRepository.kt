package com.menteencalma.app.data.repository

import com.menteencalma.app.data.monitoring.DatabaseMonitoringService
import com.menteencalma.app.domain.model.*
import com.menteencalma.app.domain.repository.DatabaseRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.system.measureTimeMillis

/**
 * Wrapper que añade monitoreo a cualquier implementación de DatabaseRepository
 * Permite cambiar la implementación subyacente sin perder el monitoreo
 */
@Singleton
class MonitoredDatabaseRepository @Inject constructor(
    private val actualRepository: DatabaseRepository,
    private val monitoringService: DatabaseMonitoringService
) : DatabaseRepository {

    // ==================== USER OPERATIONS ====================

    override suspend fun getUser(userId: String): Result<User?> {
        var result: Result<User?> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.getUser(userId)
            } catch (e: Exception) {
                monitoringService.recordError("getUser", e.message ?: "Unknown error", User.COLLECTION_NAME)
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            monitoringService.recordRead(User.COLLECTION_NAME, latency, 1)
        }
        
        return result
    }

    override suspend fun createUser(user: User): Result<Unit> {
        var result: Result<Unit> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.createUser(user)
            } catch (e: Exception) {
                monitoringService.recordError("createUser", e.message ?: "Unknown error", User.COLLECTION_NAME)
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            monitoringService.recordWrite(User.COLLECTION_NAME, latency, 1)
        }
        
        return result
    }

    override suspend fun updateUser(user: User): Result<Unit> {
        var result: Result<Unit> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.updateUser(user)
            } catch (e: Exception) {
                monitoringService.recordError("updateUser", e.message ?: "Unknown error", User.COLLECTION_NAME)
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            monitoringService.recordWrite(User.COLLECTION_NAME, latency, 1)
        }
        
        return result
    }

    override fun observeUser(userId: String): Flow<User?> {
        // Para flows, registramos la operación inicial
        monitoringService.recordRead(User.COLLECTION_NAME, 0, 1)
        return actualRepository.observeUser(userId)
    }

    override suspend fun searchUsers(
        ageRange: IntRange?,
        gender: String?,
        therapistPreference: String?,
        subscriptionStatus: String?,
        limit: Int
    ): Result<List<User>> {
        var result: Result<List<User>> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.searchUsers(ageRange, gender, therapistPreference, subscriptionStatus, limit)
            } catch (e: Exception) {
                monitoringService.recordError("searchUsers", e.message ?: "Unknown error", User.COLLECTION_NAME)
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            val documentsRead = result.getOrNull()?.size ?: 0
            monitoringService.recordRead(User.COLLECTION_NAME, latency, documentsRead)
        }
        
        return result
    }

    // ==================== CHAT OPERATIONS ====================

    override suspend fun getChatMessages(
        userId: String,
        limit: Int,
        lastMessageId: String?
    ): Result<List<ChatMessage>> {
        var result: Result<List<ChatMessage>> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.getChatMessages(userId, limit, lastMessageId)
            } catch (e: Exception) {
                monitoringService.recordError("getChatMessages", e.message ?: "Unknown error", ChatMessage.COLLECTION_NAME)
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            val documentsRead = result.getOrNull()?.size ?: 0
            monitoringService.recordRead(ChatMessage.COLLECTION_NAME, latency, documentsRead)
        }
        
        return result
    }

    override suspend fun sendChatMessage(message: ChatMessage): Result<Unit> {
        var result: Result<Unit> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.sendChatMessage(message)
            } catch (e: Exception) {
                monitoringService.recordError("sendChatMessage", e.message ?: "Unknown error", ChatMessage.COLLECTION_NAME)
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            monitoringService.recordWrite(ChatMessage.COLLECTION_NAME, latency, 1)
        }
        
        return result
    }

    override fun observeChatMessages(userId: String): Flow<List<ChatMessage>> {
        // Para flows, registramos la operación inicial
        monitoringService.recordRead(ChatMessage.COLLECTION_NAME, 0, 1)
        return actualRepository.observeChatMessages(userId)
    }

    override suspend fun markMessagesAsRead(userId: String, messageIds: List<String>): Result<Unit> {
        var result: Result<Unit> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.markMessagesAsRead(userId, messageIds)
            } catch (e: Exception) {
                monitoringService.recordError("markMessagesAsRead", e.message ?: "Unknown error", ChatMessage.COLLECTION_NAME)
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            monitoringService.recordWrite(ChatMessage.COLLECTION_NAME, latency, messageIds.size)
        }
        
        return result
    }

    // ==================== SUBSCRIPTION OPERATIONS ====================

    override suspend fun getSubscription(userId: String): Result<Subscription?> {
        var result: Result<Subscription?> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.getSubscription(userId)
            } catch (e: Exception) {
                monitoringService.recordError("getSubscription", e.message ?: "Unknown error", "subscriptions")
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            monitoringService.recordRead("subscriptions", latency, 1)
        }
        
        return result
    }

    override suspend fun updateSubscription(subscription: Subscription): Result<Unit> {
        var result: Result<Unit> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.updateSubscription(subscription)
            } catch (e: Exception) {
                monitoringService.recordError("updateSubscription", e.message ?: "Unknown error", "subscriptions")
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            monitoringService.recordWrite("subscriptions", latency, 1)
        }
        
        return result
    }

    override suspend fun getSubscriptionStats(startDate: Long, endDate: Long): Result<Map<String, Any>> {
        var result: Result<Map<String, Any>> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.getSubscriptionStats(startDate, endDate)
            } catch (e: Exception) {
                monitoringService.recordError("getSubscriptionStats", e.message ?: "Unknown error", "analytics")
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            // Las consultas de analytics pueden leer muchos documentos
            monitoringService.recordRead("analytics", latency, 100) // Estimación
        }
        
        return result
    }

    // ==================== CONTENT OPERATIONS ====================

    override suspend fun getArticles(
        category: String?,
        isPremium: Boolean?,
        limit: Int,
        offset: Int
    ): Result<List<Article>> {
        var result: Result<List<Article>> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.getArticles(category, isPremium, limit, offset)
            } catch (e: Exception) {
                monitoringService.recordError("getArticles", e.message ?: "Unknown error", Article.COLLECTION_NAME)
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            val documentsRead = result.getOrNull()?.size ?: 0
            monitoringService.recordRead(Article.COLLECTION_NAME, latency, documentsRead)
        }
        
        return result
    }

    override suspend fun getArticle(articleId: String): Result<Article?> {
        var result: Result<Article?> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.getArticle(articleId)
            } catch (e: Exception) {
                monitoringService.recordError("getArticle", e.message ?: "Unknown error", Article.COLLECTION_NAME)
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            monitoringService.recordRead(Article.COLLECTION_NAME, latency, 1)
        }
        
        return result
    }

    override suspend fun recordArticleRead(userId: String, articleId: String): Result<Unit> {
        var result: Result<Unit> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.recordArticleRead(userId, articleId)
            } catch (e: Exception) {
                monitoringService.recordError("recordArticleRead", e.message ?: "Unknown error", "analytics")
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            // Esta operación actualiza tanto el artículo como el usuario
            monitoringService.recordWrite("analytics", latency, 2)
        }
        
        return result
    }

    // ==================== ANALYTICS OPERATIONS ====================

    override suspend fun logEvent(eventName: String, parameters: Map<String, Any>): Result<Unit> {
        var result: Result<Unit> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.logEvent(eventName, parameters)
            } catch (e: Exception) {
                monitoringService.recordError("logEvent", e.message ?: "Unknown error", "events")
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            monitoringService.recordWrite("events", latency, 1)
        }
        
        return result
    }

    override suspend fun getUsageMetrics(
        userId: String,
        startDate: Long,
        endDate: Long
    ): Result<Map<String, Any>> {
        var result: Result<Map<String, Any>> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.getUsageMetrics(userId, startDate, endDate)
            } catch (e: Exception) {
                monitoringService.recordError("getUsageMetrics", e.message ?: "Unknown error", "analytics")
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            monitoringService.recordRead("analytics", latency, 1)
        }
        
        return result
    }

    // ==================== BATCH OPERATIONS ====================

    override suspend fun executeTransaction(operations: List<DatabaseRepository.DatabaseOperation>): Result<Unit> {
        var result: Result<Unit> = Result.failure(Exception("Not executed"))
        
        val latency = measureTimeMillis {
            result = try {
                actualRepository.executeTransaction(operations)
            } catch (e: Exception) {
                monitoringService.recordError("executeTransaction", e.message ?: "Unknown error", "transactions")
                Result.failure(e)
            }
        }
        
        if (result.isSuccess) {
            // Contar operaciones de escritura en la transacción
            val writeCount = operations.size
            monitoringService.recordWrite("transactions", latency, writeCount)
        }
        
        return result
    }
}
