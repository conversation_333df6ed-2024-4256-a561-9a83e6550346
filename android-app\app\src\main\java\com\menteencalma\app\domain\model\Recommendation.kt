package com.menteencalma.app.domain.model

import kotlinx.serialization.Serializable

/**
 * Modelo para recomendaciones personalizadas
 */
@Serializable
data class Recommendation(
    val id: String = "",
    val userId: String = "",
    val title: String = "",
    val content: String = "",
    val isPersonalized: Boolean = false,
    val category: String = "", // mindfulness, anxiety, stress, etc.
    val difficulty: String = "beginner", // beginner, intermediate, advanced
    val estimatedDuration: Int = 0, // en minutos
    val tags: List<String> = emptyList(),
    val createdAt: Long = System.currentTimeMillis(),
    val therapistId: String = "", // aurora, alejandro
    
    // Metadata para analytics
    val metadata: RecommendationMetadata? = null
) {
    
    @Serializable
    data class RecommendationMetadata(
        val userMood: String? = null,
        val userGoals: List<String> = emptyList(),
        val previousRecommendations: List<String> = emptyList(),
        val generationModel: String = "gpt-4",
        val confidence: Float = 0.0f,
        val customData: Map<String, String> = emptyMap()
    )
    
    companion object {
        const val COLLECTION_NAME = "recommendations"
        
        // Categorías predefinidas
        object Categories {
            const val MINDFULNESS = "mindfulness"
            const val ANXIETY = "anxiety"
            const val STRESS = "stress"
            const val DEPRESSION = "depression"
            const val SLEEP = "sleep"
            const val RELATIONSHIPS = "relationships"
            const val SELF_CARE = "self_care"
            const val MOTIVATION = "motivation"
        }
        
        // Dificultades
        object Difficulty {
            const val BEGINNER = "beginner"
            const val INTERMEDIATE = "intermediate"
            const val ADVANCED = "advanced"
        }
    }
    
    /**
     * Valida la recomendación
     */
    fun validate(): Result<Unit> {
        return when {
            id.isBlank() -> Result.failure(IllegalArgumentException("Recommendation ID cannot be blank"))
            userId.isBlank() -> Result.failure(IllegalArgumentException("User ID cannot be blank"))
            title.isBlank() -> Result.failure(IllegalArgumentException("Title cannot be blank"))
            content.isBlank() -> Result.failure(IllegalArgumentException("Content cannot be blank"))
            estimatedDuration < 0 -> Result.failure(IllegalArgumentException("Duration cannot be negative"))
            therapistId.isBlank() -> Result.failure(IllegalArgumentException("Therapist ID cannot be blank"))
            else -> Result.success(Unit)
        }
    }
}
