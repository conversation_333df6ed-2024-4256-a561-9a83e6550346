# Mente en Calma - App Android

Una aplicación de meditación y bienestar mental desarrollada con React Native y Expo, con integración completa de Firebase y sistema de suscripciones cross-platform.

## 🚀 Características

- **Autenticación completa** con Firebase Auth
- **Sistema de suscripciones** integrado con Google Play Store
- **Sincronización cross-platform** entre app móvil y web
- **Base de datos en tiempo real** con Firestore
- **Interfaz moderna** y fácil de usar
- **Gestión de estado** con Context API

## 📱 Funcionalidades

### Autenticación
- Registro e inicio de sesión con email/contraseña
- Integración con Google Sign-In (próximamente)
- Gestión de perfiles de usuario

### Suscripciones
- Planes mensuales y anuales
- Integración con Google Play Billing
- Sincronización con suscripciones web (PayPal/Stripe)
- Restauración de compras

### Contenido
- Dashboard personalizado
- Estado de suscripción en tiempo real
- Acceso a contenido premium
- Interfaz adaptativa según el plan

## 🛠️ Configuración

### 1. Configurar Firebase

1. Ve a [Firebase Console](https://console.firebase.google.com/)
2. Crea un nuevo proyecto o usa uno existente
3. Habilita Authentication y Firestore
4. Descarga el archivo `google-services.json` para Android
5. Actualiza `src/config/firebase.js` con tu configuración:

```javascript
const firebaseConfig = {
  apiKey: "tu-api-key",
  authDomain: "tu-proyecto.firebaseapp.com",
  projectId: "tu-proyecto-id",
  storageBucket: "tu-proyecto.appspot.com",
  messagingSenderId: "123456789",
  appId: "tu-app-id"
};
```

### 2. Configurar Google Play Console

1. Ve a [Google Play Console](https://play.google.com/console/)
2. Crea una nueva aplicación
3. Configura los productos de suscripción:
   - `mente_calma_monthly` - Plan mensual
   - `mente_calma_yearly` - Plan anual
4. Actualiza los SKUs en `src/contexts/SubscriptionContext.js`

### 3. Configurar EAS (Expo Application Services)

```bash
npm install -g @expo/cli
expo login
eas build:configure
```

Actualiza `app.json` con tu project ID:
```json
{
  "expo": {
    "extra": {
      "eas": {
        "projectId": "tu-project-id-aqui"
      }
    }
  }
}
```

## 🚀 Instalación y Desarrollo

### Prerrequisitos
- Node.js 16+
- Expo CLI
- Android Studio (para desarrollo Android)

### Instalación
```bash
# Clonar el repositorio
git clone [tu-repositorio]
cd mente-en-calma-app

# Instalar dependencias
npm install

# Iniciar el servidor de desarrollo
npm start
```

### Ejecutar en Android
```bash
npm run android
```

### Ejecutar en Web (para pruebas)
```bash
npm run web
```

## 📁 Estructura del Proyecto

```
src/
├── config/
│   └── firebase.js          # Configuración de Firebase
├── contexts/
│   ├── AuthContext.js       # Context de autenticación
│   └── SubscriptionContext.js # Context de suscripciones
├── navigation/
│   └── AppNavigator.js      # Navegación principal
└── screens/
    ├── LoginScreen.js       # Pantalla de login
    ├── RegisterScreen.js    # Pantalla de registro
    ├── HomeScreen.js        # Pantalla principal
    └── SubscriptionScreen.js # Pantalla de suscripciones
```

## 🔄 Sincronización Cross-Platform

La app está diseñada para sincronizar suscripciones entre:
- **App Android** (Google Play Store)
- **App Web** (PayPal/Stripe)

### Base de Datos de Usuarios (Firestore)
```javascript
{
  uid: "user-id",
  email: "<EMAIL>",
  subscription: {
    status: "active" | "inactive" | "free",
    plan: "monthly" | "yearly" | null,
    platform: "google_play" | "paypal" | "stripe",
    expiresAt: timestamp,
    purchaseToken: "token-from-platform"
  }
}
```

## 🧪 Testing

```bash
# Ejecutar tests (cuando estén configurados)
npm test

# Verificar la app en modo desarrollo
expo start --dev-client
```

## 📦 Build y Deploy

### Build de desarrollo
```bash
eas build --platform android --profile development
```

### Build de producción
```bash
eas build --platform android --profile production
```

### Submit a Google Play Store
```bash
eas submit --platform android
```

## 🔧 Configuración Adicional

### Variables de Entorno
Crea un archivo `.env` en la raíz del proyecto:
```
FIREBASE_API_KEY=tu-api-key
FIREBASE_PROJECT_ID=tu-project-id
GOOGLE_PLAY_LICENSE_KEY=tu-license-key
```

### Permisos Android
Los permisos necesarios ya están configurados en `app.json`:
- `INTERNET`
- `ACCESS_NETWORK_STATE`

## 🤝 Integración con App Web

Para sincronizar con la app web existente:

1. **Usar la misma configuración de Firebase**
2. **Mantener la estructura de datos consistente**
3. **Implementar webhooks para sincronización de suscripciones**
4. **Usar el mismo sistema de autenticación**

## 📞 Soporte

Para soporte técnico o preguntas sobre la implementación, contacta al equipo de desarrollo.

## 📄 Licencia

Este proyecto es privado y confidencial.
