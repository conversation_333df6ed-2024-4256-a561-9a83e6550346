package com.menteencalma.app.ui.theme

import androidx.compose.ui.graphics.Color

// Colores principales de Mente en Calma
val MenteEnCalmaPrimary = Color(0xFF4A90E2)
val MenteEnCalmaSecondary = Color(0xFF7BB3F0)
val MenteEnCalmaTertiary = Color(0xFF9AC5F4)

// Colores de fondo
val MenteEnCalmaBackgroundLight = Color(0xFFF8F9FA)
val MenteEnCalmaBackgroundDark = Color(0xFF121212)

// Colores de superficie
val MenteEnCalmaSurfaceLight = Color(0xFFFFFFFF)
val MenteEnCalmaSurfaceDark = Color(0xFF1E1E1E)

// Colores de texto
val MenteEnCalmaOnPrimary = Color(0xFFFFFFFF)
val MenteEnCalmaOnSecondary = Color(0xFFFFFFFF)
val MenteEnCalmaOnTertiary = Color(0xFF000000)
val MenteEnCalmaOnBackgroundLight = Color(0xFF1C1B1F)
val MenteEnCalmaOnBackgroundDark = Color(0xFFE6E1E5)
val MenteEnCalmaOnSurfaceLight = Color(0xFF1C1B1F)
val MenteEnCalmaOnSurfaceDark = Color(0xFFE6E1E5)

// Colores adicionales para la aplicación
val MenteEnCalmaSuccess = Color(0xFF4CAF50)
val MenteEnCalmaWarning = Color(0xFFFF9800)
val MenteEnCalmaError = Color(0xFFF44336)
val MenteEnCalmaInfo = Color(0xFF2196F3)
