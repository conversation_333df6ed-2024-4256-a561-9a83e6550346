package com.menteencalma.app.presentation.screens.admin;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000R\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0003\u001a\u0010\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u0006H\u0003\u001a \u0010\u0007\u001a\u00020\u00012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001a*\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u0013H\u0003\u001a\u0010\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u0006H\u0003\u001a\u0010\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u0017H\u0003\u001a\u0018\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bH\u0003\u001a\u0010\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u001a\u001a\u00020\u001bH\u0003\u001a\u0010\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u001eH\u0003\u00a8\u0006\u001f"}, d2 = {"AlertItem", "", "alert", "Lcom/menteencalma/app/data/monitoring/DatabaseAlert;", "AlertsTab", "uiState", "Lcom/menteencalma/app/presentation/viewmodels/DatabaseMonitoringUiState;", "DatabaseMonitoringScreen", "onNavigateBack", "Lkotlin/Function0;", "viewModel", "Lcom/menteencalma/app/presentation/viewmodels/DatabaseMonitoringViewModel;", "MetricItem", "label", "", "value", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "isError", "", "MetricsTab", "MigrationEstimateCard", "estimate", "Lcom/menteencalma/app/data/migration/MigrationEstimate;", "MigrationTab", "RecommendationItem", "recommendation", "Lcom/menteencalma/app/data/monitoring/OptimizationRecommendation;", "ValidationReportCard", "report", "Lcom/menteencalma/app/data/migration/ValidationReport;", "app_debug"})
public final class DatabaseMonitoringScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void DatabaseMonitoringScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MetricsTab(com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState uiState) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AlertsTab(com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState uiState) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MigrationTab(com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState uiState, com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MetricItem(java.lang.String label, java.lang.String value, androidx.compose.ui.graphics.vector.ImageVector icon, boolean isError) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void RecommendationItem(com.menteencalma.app.data.monitoring.OptimizationRecommendation recommendation) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AlertItem(com.menteencalma.app.data.monitoring.DatabaseAlert alert) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MigrationEstimateCard(com.menteencalma.app.data.migration.MigrationEstimate estimate) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ValidationReportCard(com.menteencalma.app.data.migration.ValidationReport report) {
    }
}