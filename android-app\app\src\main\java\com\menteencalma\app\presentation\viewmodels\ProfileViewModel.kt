package com.menteencalma.app.presentation.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.menteencalma.app.domain.model.User
import com.menteencalma.app.domain.repository.DatabaseRepository
import com.menteencalma.app.domain.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class ProfileUiState(
    val currentUser: User? = null,
    val isLoading: Boolean = true,
    val isUpdating: Boolean = false,
    val errorMessage: String? = null,
    val updateSuccess: Boolean = false,
    
    // Form fields
    val displayName: String = "",
    val age: String = "",
    val therapistGender: String = "",
    
    // Subscription info
    val subscriptionStatus: String = "free",
    val subscriptionPlan: String? = null,
    val subscriptionPlatform: String? = null,
    val subscriptionExpiresAt: Long? = null,
    val isSubscriptionActive: Boolean = false
)

@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val databaseRepository: DatabaseRepository,
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ProfileUiState())
    val uiState: StateFlow<ProfileUiState> = _uiState.asStateFlow()

    init {
        loadUserProfile()
    }

    private fun loadUserProfile() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
                
                val firebaseUser = authRepository.getCurrentUser()
                if (firebaseUser != null) {
                    val userResult = databaseRepository.getUser(firebaseUser.uid)
                    
                    if (userResult.isSuccess) {
                        val user = userResult.getOrNull()
                        if (user != null) {
                            _uiState.value = _uiState.value.copy(
                                currentUser = user,
                                displayName = user.displayName,
                                age = user.age?.toString() ?: "",
                                therapistGender = user.therapistGender ?: "aurora",
                                subscriptionStatus = user.subscriptionStatus,
                                subscriptionPlan = user.subscriptionPlan,
                                subscriptionPlatform = user.subscriptionPlatform,
                                subscriptionExpiresAt = user.subscriptionExpiresAt,
                                isSubscriptionActive = user.hasActiveSubscription(),
                                isLoading = false
                            )
                        } else {
                            _uiState.value = _uiState.value.copy(
                                errorMessage = "No se pudo cargar el perfil del usuario",
                                isLoading = false
                            )
                        }
                    } else {
                        _uiState.value = _uiState.value.copy(
                            errorMessage = "Error al cargar el perfil: ${userResult.exceptionOrNull()?.message}",
                            isLoading = false
                        )
                    }
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Usuario no autenticado",
                        isLoading = false
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error inesperado: ${e.message}",
                    isLoading = false
                )
            }
        }
    }

    fun updateDisplayName(name: String) {
        _uiState.value = _uiState.value.copy(displayName = name, updateSuccess = false)
    }

    fun updateAge(age: String) {
        // Validar que solo contenga números
        if (age.isEmpty() || age.all { it.isDigit() }) {
            _uiState.value = _uiState.value.copy(age = age, updateSuccess = false)
        }
    }

    fun updateTherapistGender(therapist: String) {
        _uiState.value = _uiState.value.copy(therapistGender = therapist, updateSuccess = false)
    }

    fun updateProfile() {
        val currentState = _uiState.value
        val currentUser = currentState.currentUser ?: return

        // Validaciones
        if (currentState.displayName.isBlank()) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "El nombre no puede estar vacío"
            )
            return
        }

        if (currentState.age.isNotEmpty()) {
            val ageInt = currentState.age.toIntOrNull()
            if (ageInt == null || ageInt < 13 || ageInt > 120) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "La edad debe ser un número entre 13 y 120"
                )
                return
            }
        }

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isUpdating = true,
                    errorMessage = null,
                    updateSuccess = false
                )

                val updatedUser = currentUser.copy(
                    displayName = currentState.displayName.trim(),
                    age = if (currentState.age.isNotEmpty()) currentState.age.toInt() else null,
                    therapistGender = currentState.therapistGender,
                    updatedAt = System.currentTimeMillis()
                )

                val result = databaseRepository.updateUser(updatedUser)
                
                if (result.isSuccess) {
                    _uiState.value = _uiState.value.copy(
                        currentUser = updatedUser,
                        isUpdating = false,
                        updateSuccess = true,
                        errorMessage = null
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Error al actualizar el perfil: ${result.exceptionOrNull()?.message}",
                        isUpdating = false
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error inesperado: ${e.message}",
                    isUpdating = false
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun clearUpdateSuccess() {
        _uiState.value = _uiState.value.copy(updateSuccess = false)
    }

    fun refreshProfile() {
        loadUserProfile()
    }

    fun signOut() {
        viewModelScope.launch {
            try {
                authRepository.signOut()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error al cerrar sesión: ${e.message}"
                )
            }
        }
    }

    fun getTherapistDisplayName(therapistId: String): String {
        return when (therapistId) {
            "aurora" -> "Psicóloga Aurora"
            "alejandro" -> "Psicólogo Alejandro"
            else -> "Terapeuta IA"
        }
    }

    fun getTherapistDescription(therapistId: String): String {
        return when (therapistId) {
            "aurora" -> "Especialista en terapia cognitivo-conductual y mindfulness"
            "alejandro" -> "Experto en psicología positiva y gestión emocional"
            else -> "Asistente de inteligencia artificial especializado en bienestar mental"
        }
    }

    fun getSubscriptionDisplayInfo(): SubscriptionDisplayInfo {
        val currentState = _uiState.value
        
        return when {
            currentState.isSubscriptionActive -> {
                val planName = when (currentState.subscriptionPlan) {
                    "premium_monthly" -> "Premium Mensual"
                    "premium_annual" -> "Premium Anual"
                    "premium_plus" -> "Premium Plus"
                    else -> "Premium"
                }
                
                val expirationText = currentState.subscriptionExpiresAt?.let { expiresAt ->
                    val date = java.util.Date(expiresAt)
                    val formatter = java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale.getDefault())
                    "Expira el ${formatter.format(date)}"
                } ?: "Sin fecha de expiración"
                
                SubscriptionDisplayInfo(
                    isActive = true,
                    title = "Plan $planName Activo",
                    description = expirationText,
                    buttonText = "Gestionar Suscripción",
                    platform = currentState.subscriptionPlatform
                )
            }
            else -> {
                SubscriptionDisplayInfo(
                    isActive = false,
                    title = "Estás en la Versión Gratuita",
                    description = "Desbloquea todas las funciones con Premium",
                    buttonText = "Suscribirse Ahora",
                    platform = null
                )
            }
        }
    }
}

data class SubscriptionDisplayInfo(
    val isActive: Boolean,
    val title: String,
    val description: String,
    val buttonText: String,
    val platform: String?
)
