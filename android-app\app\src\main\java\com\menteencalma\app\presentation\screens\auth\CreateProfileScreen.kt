package com.menteencalma.app.presentation.screens.auth

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.menteencalma.app.R
import com.menteencalma.app.presentation.viewmodels.CreateProfileViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateProfileScreen(
    onProfileCreated: () -> Unit,
    onNavigateBack: () -> Unit,
    createProfileViewModel: CreateProfileViewModel = hiltViewModel()
) {
    val uiState by createProfileViewModel.uiState.collectAsState()

    // Observar cuando el perfil se crea exitosamente
    LaunchedEffect(uiState.isProfileCreated) {
        if (uiState.isProfileCreated) {
            onProfileCreated()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Crear Perfil") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Volver")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.05f),
                            MaterialTheme.colorScheme.background
                        )
                    )
                )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Header
                ProfileHeader()

                Spacer(modifier = Modifier.height(32.dp))

                // Formulario
                ProfileForm(
                    uiState = uiState,
                    onDisplayNameChange = createProfileViewModel::updateDisplayName,
                    onAgeChange = createProfileViewModel::updateAge,
                    onGenderChange = createProfileViewModel::updateGender,
                    onTherapistGenderChange = createProfileViewModel::updateTherapistGender,
                    onCreateProfile = createProfileViewModel::createProfile,
                    onClearError = createProfileViewModel::clearError
                )
            }
        }
    }
}

@Composable
private fun ProfileHeader() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Icono de perfil
        Box(
            modifier = Modifier
                .size(80.dp)
                .clip(RoundedCornerShape(20.dp))
                .background(MaterialTheme.colorScheme.primaryContainer),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = null,
                modifier = Modifier.size(40.dp),
                tint = MaterialTheme.colorScheme.onPrimaryContainer
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "Completa tu perfil",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )

        Text(
            text = "Ayúdanos a personalizar tu experiencia",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun ProfileForm(
    uiState: com.menteencalma.app.presentation.viewmodels.CreateProfileUiState,
    onDisplayNameChange: (String) -> Unit,
    onAgeChange: (String) -> Unit,
    onGenderChange: (String) -> Unit,
    onTherapistGenderChange: (String) -> Unit,
    onCreateProfile: () -> Unit,
    onClearError: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        // Campo de nombre (obligatorio)
        OutlinedTextField(
            value = uiState.displayName,
            onValueChange = {
                onDisplayNameChange(it)
                if (uiState.errorMessage != null) onClearError()
            },
            label = { Text("Nombre *") },
            placeholder = { Text("¿Cómo te llamas?") },
            singleLine = true,
            modifier = Modifier.fillMaxWidth(),
            isError = uiState.errorMessage != null && uiState.displayName.isBlank()
        )

        // Campo de edad (opcional)
        OutlinedTextField(
            value = uiState.age,
            onValueChange = {
                // Solo permitir números
                if (it.all { char -> char.isDigit() } && it.length <= 3) {
                    onAgeChange(it)
                    if (uiState.errorMessage != null) onClearError()
                }
            },
            label = { Text("Edad (opcional)") },
            placeholder = { Text("Ej: 25") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            singleLine = true,
            modifier = Modifier.fillMaxWidth(),
            isError = uiState.errorMessage != null && uiState.age.isNotBlank()
        )

        // Selección de género (opcional)
        GenderSelection(
            selectedGender = uiState.gender,
            onGenderSelected = onGenderChange
        )

        // Selección de terapeuta (obligatorio)
        TherapistSelection(
            selectedTherapist = uiState.therapistGender,
            onTherapistSelected = onTherapistGenderChange,
            isError = uiState.errorMessage != null && uiState.therapistGender.isBlank()
        )

        // Mensaje de error
        if (uiState.errorMessage != null) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Text(
                    text = uiState.errorMessage,
                    modifier = Modifier.padding(16.dp),
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Botón de guardar
        Button(
            onClick = onCreateProfile,
            modifier = Modifier.fillMaxWidth(),
            enabled = !uiState.isLoading && uiState.displayName.isNotBlank() && uiState.therapistGender.isNotBlank(),
            shape = RoundedCornerShape(12.dp)
        ) {
            if (uiState.isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    color = MaterialTheme.colorScheme.onPrimary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("Creando perfil...")
            } else {
                Text("Guardar y Continuar")
            }
        }
    }
}

@Composable
private fun GenderSelection(
    selectedGender: String,
    onGenderSelected: (String) -> Unit
) {
    Column {
        Text(
            text = "Género (opcional)",
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )

        Spacer(modifier = Modifier.height(8.dp))

        val genderOptions = listOf(
            "male" to "Masculino",
            "female" to "Femenino",
            "other" to "Otro",
            "prefer_not_to_say" to "Prefiero no decir"
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            genderOptions.forEach { (value, label) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(8.dp))
                        .selectable(
                            selected = selectedGender == value,
                            onClick = { onGenderSelected(value) }
                        )
                        .padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedGender == value,
                        onClick = { onGenderSelected(value) }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = label,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

@Composable
private fun TherapistSelection(
    selectedTherapist: String,
    onTherapistSelected: (String) -> Unit,
    isError: Boolean = false
) {
    Column {
        Text(
            text = "Elige tu terapeuta *",
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium,
            color = if (isError) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.onSurface
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Psicóloga Aurora
        TherapistCard(
            name = "Psicóloga Aurora",
            description = "Especialista en terapia cognitivo-conductual y mindfulness",
            value = com.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.THERAPIST_AURORA,
            isSelected = selectedTherapist == com.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.THERAPIST_AURORA,
            onSelected = onTherapistSelected,
            isError = isError
        )

        Spacer(modifier = Modifier.height(12.dp))

        // Psicólogo Alejandro
        TherapistCard(
            name = "Psicólogo Alejandro",
            description = "Experto en psicología positiva y gestión emocional",
            value = com.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.THERAPIST_ALEJANDRO,
            isSelected = selectedTherapist == com.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.THERAPIST_ALEJANDRO,
            onSelected = onTherapistSelected,
            isError = isError
        )
    }
}

@Composable
private fun TherapistCard(
    name: String,
    description: String,
    value: String,
    isSelected: Boolean,
    onSelected: (String) -> Unit,
    isError: Boolean = false
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .selectable(
                selected = isSelected,
                onClick = { onSelected(value) }
            ),
        colors = CardDefaults.cardColors(
            containerColor = when {
                isSelected -> MaterialTheme.colorScheme.primaryContainer
                isError -> MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
                else -> MaterialTheme.colorScheme.surfaceVariant
            }
        ),
        border = if (isSelected) {
            androidx.compose.foundation.BorderStroke(
                2.dp,
                MaterialTheme.colorScheme.primary
            )
        } else null
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = isSelected,
                onClick = { onSelected(value) }
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = name,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = if (isSelected) MaterialTheme.colorScheme.onPrimaryContainer else MaterialTheme.colorScheme.onSurfaceVariant
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = if (isSelected) MaterialTheme.colorScheme.onPrimaryContainer else MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}
