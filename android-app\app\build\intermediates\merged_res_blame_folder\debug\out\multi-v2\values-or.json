{"logs": [{"outputFile": "com.menteencalma.app-mergeDebugResources-3:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\98bcb59e84dea2106bcefc907e055731\\transformed\\material3-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,407,526,620,720,837,980,1106,1257,1342,1447,1543,1638,1754,1884,1994,2137,2275,2406,2598,2724,2853,2988,3118,3215,3311,3428,3550,3655,3760,3863,4005,4155,4262,4371,4446,4550,4652,4746,4837,4942,5022,5107,5208,5314,5407,5508,5595,5703,5802,5905,6029,6109,6212", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "172,289,402,521,615,715,832,975,1101,1252,1337,1442,1538,1633,1749,1879,1989,2132,2270,2401,2593,2719,2848,2983,3113,3210,3306,3423,3545,3650,3755,3858,4000,4150,4257,4366,4441,4545,4647,4741,4832,4937,5017,5102,5203,5309,5402,5503,5590,5698,5797,5900,6024,6104,6207,6301"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4194,4316,4433,4546,4665,4759,4859,4976,5119,5245,5396,5481,5586,5682,5777,5893,6023,6133,6276,6414,6545,6737,6863,6992,7127,7257,7354,7450,7567,7689,7794,7899,8002,8144,8294,8401,8510,8585,8689,8791,8885,8976,9081,9161,9246,9347,9453,9546,9647,9734,9842,9941,10044,10168,10248,10351", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "4311,4428,4541,4660,4754,4854,4971,5114,5240,5391,5476,5581,5677,5772,5888,6018,6128,6271,6409,6540,6732,6858,6987,7122,7252,7349,7445,7562,7684,7789,7894,7997,8139,8289,8396,8505,8580,8684,8786,8880,8971,9076,9156,9241,9342,9448,9541,9642,9729,9837,9936,10039,10163,10243,10346,10440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\bc87254a1d5a4bb9d2cd1a5740dba329\\transformed\\play-services-base-18.1.0\\res\\values-or\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1024,1135,1296,1428,1545,1700,1835,1949,2199,2366,2479,2640,2773,2923,3080,3145,3217", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "1130,1291,1423,1540,1695,1830,1944,2054,2361,2474,2635,2768,2918,3075,3140,3212,3299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ec6bd2faa3e0a998fc0a19c7dd0c22ed\\transformed\\browser-1.4.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,270,383", "endColumns": "109,104,112,108", "endOffsets": "160,265,378,487"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "3304,3692,3797,3910", "endColumns": "109,104,112,108", "endOffsets": "3409,3792,3905,4014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ca611de3937d8b7fad81038213315e33\\transformed\\foundation-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "105,106", "startColumns": "4,4", "startOffsets": "11360,11445", "endColumns": "84,87", "endOffsets": "11440,11528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\0a2f82cac04b16c6074a060829451b01\\transformed\\play-services-basement-18.2.0\\res\\values-or\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2059", "endColumns": "139", "endOffsets": "2194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\b7c5e14f30f524dc22cc8925fd453a32\\transformed\\core-1.12.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,208,310,413,518,619,721,10990", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "203,305,408,513,614,716,835,11086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d60095cfcd5f6da5b72a1052224dd592\\transformed\\ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,987,1057,1135,1217,1287,1370,1437", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,982,1052,1130,1212,1282,1365,1432,1551"}, "to": {"startLines": "9,10,30,31,32,36,37,94,95,96,97,98,99,100,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,937,3414,3506,3606,4019,4096,10445,10533,10620,10690,10760,10838,10920,11091,11174,11241", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "932,1019,3501,3601,3687,4091,4189,10528,10615,10685,10755,10833,10915,10985,11169,11236,11355"}}]}]}