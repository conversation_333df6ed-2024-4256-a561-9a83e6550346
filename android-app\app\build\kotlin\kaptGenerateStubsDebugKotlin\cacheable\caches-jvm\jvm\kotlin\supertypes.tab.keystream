!com.menteencalma.app.MainActivity,com.menteencalma.app.MenteEnCalmaApplication:com.menteencalma.app.data.migration.ExportData.$serializer2com.menteencalma.app.data.migration.TargetDatabase.com.menteencalma.app.data.monitoring.AlertType7com.menteencalma.app.data.monitoring.RecommendationType-com.menteencalma.app.data.monitoring.Priority7com.menteencalma.app.data.repository.AuthRepositoryImpl?<EMAIL>(com.menteencalma.app.di.FirebaseDatabase)com.menteencalma.app.di.MonitoredDatabaseEcom.menteencalma.app.domain.model.Article.ArticleMetadata.$serializer5com.menteencalma.app.domain.model.Article.$serializer3com.menteencalma.app.domain.model.AuthState.Loading;com.menteencalma.app.domain.model.AuthState.Unauthenticated8com.menteencalma.app.domain.model.AuthState.NeedsProfile9com.menteencalma.app.domain.model.AuthState.Authenticated1com.menteencalma.app.domain.model.AuthState.Error9com.menteencalma.app.domain.model.ChatMessage.MessageTypeIcom.menteencalma.app.domain.model.ChatMessage.MessageMetadata.$serializer9com.menteencalma.app.domain.model.ChatMessage.$serializerNcom.menteencalma.app.domain.model.GeneratedArticle.ArticleMetadata.$serializer>com.menteencalma.app.domain.model.GeneratedArticle.$serializerDcom.menteencalma.app.domain.model.MoodEntry.MoodMetadata.$serializer4com.menteencalma.app.domain.model.MoodEntry.MoodType7com.menteencalma.app.domain.model.MoodEntry.$serializerScom.menteencalma.app.domain.model.Recommendation.RecommendationMetadata.$serializer<com.menteencalma.app.domain.model.Recommendation.$serializerGcom.menteencalma.app.domain.model.User.NotificationSettings.$serializerBcom.menteencalma.app.domain.model.User.UserPreferences.$<EMAIL>.$serializer<com.menteencalma.app.domain.model.User.MoodEntry.$serializer2com.menteencalma.app.domain.model.User.$serializerVcom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation.CreateUserVcom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation.UpdateUserYcom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation.CreateMessage^com.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation.UpdateSubscription-com.menteencalma.app.navigation.Screen.Splash,com.menteencalma.app.navigation.Screen.Login4com.menteencalma.app.navigation.Screen.CreateProfile+com.menteencalma.app.navigation.Screen.Chat6com.menteencalma.app.navigation.Screen.Recommendations/com.menteencalma.app.navigation.Screen.Articles2com.menteencalma.app.navigation.Screen.MoodTracker.com.menteencalma.app.navigation.Screen.Doctors.com.menteencalma.app.navigation.Screen.Profile0com.menteencalma.app.navigation.Screen.Subscribe1com.menteencalma.app.navigation.Screen.Disclaimer/com.menteencalma.app.navigation.Screen.Settings4com.menteencalma.app.navigation.Screen.ArticleDetailDcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel<com.menteencalma.app.presentation.screens.chat.ChatViewModelBcom.menteencalma.app.presentation.screens.profile.ProfileViewModelRcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel:com.menteencalma.app.presentation.viewmodels.AuthViewModelCcom.menteencalma.app.presentation.viewmodels.CreateProfileViewModelHcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel;com.menteencalma.app.presentation.viewmodels.LoginViewModelAcom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel=com.menteencalma.app.presentation.viewmodels.ProfileViewModel?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel                                                                                                                                                                                                                                                                                      