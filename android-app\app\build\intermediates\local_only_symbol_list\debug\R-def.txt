R_DEF: Internal format may change without notice
local
color background_color
color black
color error_color
color md_theme_dark_error
color md_theme_dark_errorContainer
color md_theme_dark_inverseOnSurface
color md_theme_dark_inversePrimary
color md_theme_dark_inverseSurface
color md_theme_dark_onError
color md_theme_dark_onErrorContainer
color md_theme_dark_onPrimary
color md_theme_dark_onPrimaryContainer
color md_theme_dark_onSecondary
color md_theme_dark_onSecondaryContainer
color md_theme_dark_onSurface
color md_theme_dark_onSurfaceVariant
color md_theme_dark_onTertiary
color md_theme_dark_onTertiaryContainer
color md_theme_dark_outline
color md_theme_dark_outlineVariant
color md_theme_dark_primary
color md_theme_dark_primaryContainer
color md_theme_dark_secondary
color md_theme_dark_secondaryContainer
color md_theme_dark_surface
color md_theme_dark_surfaceVariant
color md_theme_dark_tertiary
color md_theme_dark_tertiaryContainer
color md_theme_light_error
color md_theme_light_errorContainer
color md_theme_light_inverseOnSurface
color md_theme_light_inversePrimary
color md_theme_light_inverseSurface
color md_theme_light_onError
color md_theme_light_onErrorContainer
color md_theme_light_onPrimary
color md_theme_light_onPrimaryContainer
color md_theme_light_onSecondary
color md_theme_light_onSecondaryContainer
color md_theme_light_onSurface
color md_theme_light_onSurfaceVariant
color md_theme_light_onTertiary
color md_theme_light_onTertiaryContainer
color md_theme_light_outline
color md_theme_light_outlineVariant
color md_theme_light_primary
color md_theme_light_primaryContainer
color md_theme_light_secondary
color md_theme_light_secondaryContainer
color md_theme_light_surface
color md_theme_light_surfaceVariant
color md_theme_light_tertiary
color md_theme_light_tertiaryContainer
color primary_color
color secondary_color
color surface_color
color transparent
color white
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_launcher_legacy
drawable ic_launcher_temp
mipmap ic_launcher
mipmap ic_launcher_round
string age_label
string age_placeholder
string app_name
string app_subtitle
string articles_description
string articles_title
string back
string cancel
string chat_description
string chat_title
string complete_profile
string confirm_password_label
string continue_with_google
string create_profile_title
string creating_profile
string default_web_client_id
string email_label
string error
string gcm_defaultSenderId
string gender_female
string gender_label
string gender_male
string gender_other
string gender_prefer_not_to_say
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string loading
string login_title
string logo_description
string menu_description
string name_label
string name_placeholder
string nav_articles
string nav_chat
string nav_doctors
string nav_mood_tracker
string nav_profile
string nav_recommendations
string ok
string password_label
string personalize_experience
string profile_description
string profile_title
string project_id
string recommendations_description
string recommendations_title
string retry
string save_and_continue
string settings_description
string sign_in_button
string sign_in_tab
string sign_up_button
string sign_up_tab
string subscribe_title
string therapist_alejandro
string therapist_alejandro_description
string therapist_aurora
string therapist_aurora_description
string therapist_label
style Theme.MenteEnCalma
style Theme.MenteEnCalma.Splash
xml backup_rules
xml data_extraction_rules
