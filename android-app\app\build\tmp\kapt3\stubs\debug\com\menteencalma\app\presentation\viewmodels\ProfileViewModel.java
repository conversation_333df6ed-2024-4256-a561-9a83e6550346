package com.menteencalma.app.presentation.viewmodels;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u000e\u001a\u00020\u000fJ\u0006\u0010\u0010\u001a\u00020\u000fJ\u0006\u0010\u0011\u001a\u00020\u0012J\u000e\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014J\u000e\u0010\u0016\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014J\b\u0010\u0017\u001a\u00020\u000fH\u0002J\u0006\u0010\u0018\u001a\u00020\u000fJ\u0006\u0010\u0019\u001a\u00020\u000fJ\u000e\u0010\u001a\u001a\u00020\u000f2\u0006\u0010\u001b\u001a\u00020\u0014J\u000e\u0010\u001c\u001a\u00020\u000f2\u0006\u0010\u001d\u001a\u00020\u0014J\u0006\u0010\u001e\u001a\u00020\u000fJ\u000e\u0010\u001f\u001a\u00020\u000f2\u0006\u0010 \u001a\u00020\u0014R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006!"}, d2 = {"Lcom/menteencalma/app/presentation/viewmodels/ProfileViewModel;", "Landroidx/lifecycle/ViewModel;", "databaseRepository", "Lcom/menteencalma/app/domain/repository/DatabaseRepository;", "authRepository", "Lcom/menteencalma/app/domain/repository/AuthRepository;", "(Lcom/menteencalma/app/domain/repository/DatabaseRepository;Lcom/menteencalma/app/domain/repository/AuthRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/menteencalma/app/presentation/viewmodels/ProfileUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "clearError", "", "clearUpdateSuccess", "getSubscriptionDisplayInfo", "Lcom/menteencalma/app/presentation/viewmodels/SubscriptionDisplayInfo;", "getTherapistDescription", "", "therapistId", "getTherapistDisplayName", "loadUserProfile", "refreshProfile", "signOut", "updateAge", "age", "updateDisplayName", "name", "updateProfile", "updateTherapistGender", "therapist", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class ProfileViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.repository.DatabaseRepository databaseRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.repository.AuthRepository authRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.menteencalma.app.presentation.viewmodels.ProfileUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.viewmodels.ProfileUiState> uiState = null;
    
    @javax.inject.Inject()
    public ProfileViewModel(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.DatabaseRepository databaseRepository, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.AuthRepository authRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.viewmodels.ProfileUiState> getUiState() {
        return null;
    }
    
    private final void loadUserProfile() {
    }
    
    public final void updateDisplayName(@org.jetbrains.annotations.NotNull()
    java.lang.String name) {
    }
    
    public final void updateAge(@org.jetbrains.annotations.NotNull()
    java.lang.String age) {
    }
    
    public final void updateTherapistGender(@org.jetbrains.annotations.NotNull()
    java.lang.String therapist) {
    }
    
    public final void updateProfile() {
    }
    
    public final void clearError() {
    }
    
    public final void clearUpdateSuccess() {
    }
    
    public final void refreshProfile() {
    }
    
    public final void signOut() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTherapistDisplayName(@org.jetbrains.annotations.NotNull()
    java.lang.String therapistId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTherapistDescription(@org.jetbrains.annotations.NotNull()
    java.lang.String therapistId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.presentation.viewmodels.SubscriptionDisplayInfo getSubscriptionDisplayInfo() {
        return null;
    }
}