<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme for dark mode using compatible Material Design -->
    <style name="Base.Theme.MenteEnCalma" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Primary colors for dark mode -->
        <item name="colorPrimary">@color/md_theme_dark_primary</item>
        <item name="colorPrimaryDark">@color/md_theme_dark_primaryContainer</item>
        <item name="colorAccent">@color/md_theme_dark_secondary</item>

        <!-- Background colors for dark mode -->
        <item name="android:colorBackground">@color/md_theme_dark_surface</item>
        <item name="colorSurface">@color/md_theme_dark_surface</item>

        <!-- Text colors for dark mode -->
        <item name="android:textColorPrimary">@color/md_theme_dark_onSurface</item>
        <item name="android:textColorSecondary">@color/md_theme_dark_onSurfaceVariant</item>

        <!-- Status bar for dark mode -->
        <item name="android:statusBarColor">@color/md_theme_dark_surface</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">false</item>
    </style>
</resources>
