package com.menteencalma.app.presentation.screens.chat;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0019\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B_\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\b\u0012\b\b\u0002\u0010\n\u001a\u00020\b\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\b\b\u0002\u0010\r\u001a\u00020\f\u0012\b\b\u0002\u0010\u000e\u001a\u00020\f\u00a2\u0006\u0002\u0010\u000fJ\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001d\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\bH\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\t\u0010 \u001a\u00020\fH\u00c6\u0003J\t\u0010!\u001a\u00020\fH\u00c6\u0003Jc\u0010\"\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\b2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\b\b\u0002\u0010\r\u001a\u00020\f2\b\b\u0002\u0010\u000e\u001a\u00020\fH\u00c6\u0001J\u0013\u0010#\u001a\u00020\b2\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020&H\u00d6\u0001J\t\u0010\'\u001a\u00020\fH\u00d6\u0001R\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\n\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u0015R\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0015R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u000e\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0013R\u0011\u0010\r\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0013\u00a8\u0006("}, d2 = {"Lcom/menteencalma/app/presentation/screens/chat/ChatUiState;", "", "messages", "", "Lcom/menteencalma/app/domain/model/ChatMessage;", "currentUser", "Lcom/menteencalma/app/domain/model/User;", "isLoading", "", "isSendingMessage", "hasReachedLimit", "errorMessage", "", "therapistName", "therapistAvatar", "(Ljava/util/List;Lcom/menteencalma/app/domain/model/User;ZZZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getCurrentUser", "()Lcom/menteencalma/app/domain/model/User;", "getErrorMessage", "()Ljava/lang/String;", "getHasReachedLimit", "()Z", "getMessages", "()Ljava/util/List;", "getTherapistAvatar", "getTherapistName", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class ChatUiState {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.menteencalma.app.domain.model.ChatMessage> messages = null;
    @org.jetbrains.annotations.Nullable()
    private final com.menteencalma.app.domain.model.User currentUser = null;
    private final boolean isLoading = false;
    private final boolean isSendingMessage = false;
    private final boolean hasReachedLimit = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String therapistName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String therapistAvatar = null;
    
    public ChatUiState(@org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.domain.model.ChatMessage> messages, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.User currentUser, boolean isLoading, boolean isSendingMessage, boolean hasReachedLimit, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, @org.jetbrains.annotations.NotNull()
    java.lang.String therapistName, @org.jetbrains.annotations.NotNull()
    java.lang.String therapistAvatar) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.domain.model.ChatMessage> getMessages() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.User getCurrentUser() {
        return null;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isSendingMessage() {
        return false;
    }
    
    public final boolean getHasReachedLimit() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTherapistName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTherapistAvatar() {
        return null;
    }
    
    public ChatUiState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.domain.model.ChatMessage> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.User component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.presentation.screens.chat.ChatUiState copy(@org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.domain.model.ChatMessage> messages, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.User currentUser, boolean isLoading, boolean isSendingMessage, boolean hasReachedLimit, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, @org.jetbrains.annotations.NotNull()
    java.lang.String therapistName, @org.jetbrains.annotations.NotNull()
    java.lang.String therapistAvatar) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}