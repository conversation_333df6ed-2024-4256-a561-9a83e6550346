package com.menteencalma.app.presentation.screens.subscription;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000L\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a\u001e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a\u0010\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\bH\u0003\u001a\u0016\u0010\t\u001a\u00020\u00012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\b0\u000bH\u0003\u001a\u001a\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u0010H\u0003\u001a\u0012\u0010\u0011\u001a\u00020\u00012\b\b\u0002\u0010\u000f\u001a\u00020\u0010H\u0003\u001aN\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000e2\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u000f\u001a\u00020\u0010H\u0003\u001ab\u0010\u0019\u001a\u00020\u00012\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00140\u000b2\b\u0010\u001b\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u0016\u001a\u00020\u000e2\u0012\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010\u001d2\u0012\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010\u001d2\u0006\u0010\u001f\u001a\u00020\u000e2\b\u0010 \u001a\u0004\u0018\u00010\u0003H\u0003\u001a.\u0010!\u001a\u00020\u00012\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010$\u001a\u00020%H\u0007\u001a\b\u0010&\u001a\u00020\u0001H\u0003\u001a\u0010\u0010\'\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0003\u001a\b\u0010(\u001a\u00020\u0001H\u0003\u00a8\u0006)"}, d2 = {"ErrorCard", "", "message", "", "onDismiss", "Lkotlin/Function0;", "FeatureRow", "feature", "Lcom/menteencalma/app/presentation/viewmodels/SubscriptionFeature;", "FeaturesComparisonTable", "features", "", "FreePlanCard", "isCurrentPlan", "", "modifier", "Landroidx/compose/ui/Modifier;", "LoadingState", "PlanCard", "plan", "Lcom/menteencalma/app/presentation/viewmodels/SubscriptionPlan;", "isSelected", "isPurchasing", "onSelect", "onPurchase", "PlansSection", "plans", "selectedPlan", "onSelectPlan", "Lkotlin/Function1;", "onPurchasePlan", "isUserSubscribed", "currentPlan", "SubscribeScreen", "onNavigateBack", "onSubscriptionSuccess", "subscribeViewModel", "Lcom/menteencalma/app/presentation/viewmodels/SubscribeViewModel;", "SubscriptionHeader", "SuccessCard", "TermsAndPrivacy", "app_debug"})
public final class SubscribeScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SubscribeScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSubscriptionSuccess, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.presentation.viewmodels.SubscribeViewModel subscribeViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void LoadingState(androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void SubscriptionHeader() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void FeaturesComparisonTable(java.util.List<com.menteencalma.app.presentation.viewmodels.SubscriptionFeature> features) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void FeatureRow(com.menteencalma.app.presentation.viewmodels.SubscriptionFeature feature) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void PlansSection(java.util.List<com.menteencalma.app.presentation.viewmodels.SubscriptionPlan> plans, com.menteencalma.app.presentation.viewmodels.SubscriptionPlan selectedPlan, boolean isPurchasing, kotlin.jvm.functions.Function1<? super com.menteencalma.app.presentation.viewmodels.SubscriptionPlan, kotlin.Unit> onSelectPlan, kotlin.jvm.functions.Function1<? super com.menteencalma.app.presentation.viewmodels.SubscriptionPlan, kotlin.Unit> onPurchasePlan, boolean isUserSubscribed, java.lang.String currentPlan) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void FreePlanCard(boolean isCurrentPlan, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void PlanCard(com.menteencalma.app.presentation.viewmodels.SubscriptionPlan plan, boolean isSelected, boolean isPurchasing, boolean isCurrentPlan, kotlin.jvm.functions.Function0<kotlin.Unit> onSelect, kotlin.jvm.functions.Function0<kotlin.Unit> onPurchase, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TermsAndPrivacy() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ErrorCard(java.lang.String message, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void SuccessCard(java.lang.String message) {
    }
}