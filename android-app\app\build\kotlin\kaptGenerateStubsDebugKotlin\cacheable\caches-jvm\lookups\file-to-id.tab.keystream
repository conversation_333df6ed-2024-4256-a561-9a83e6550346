6app/src/main/java/com/menteencalma/app/MainActivity.ktAapp/src/main/java/com/menteencalma/app/MenteEnCalmaApplication.ktQapp/src/main/java/com/menteencalma/app/data/migration/DatabaseMigrationService.ktSapp/src/main/java/com/menteencalma/app/data/monitoring/DatabaseMonitoringService.ktLapp/src/main/java/com/menteencalma/app/data/repository/AuthRepositoryImpl.ktTapp/src/main/java/com/menteencalma/app/data/repository/FirebaseDatabaseRepository.ktUapp/src/main/java/com/menteencalma/app/data/repository/MonitoredDatabaseRepository.ktLapp/src/main/java/com/menteencalma/app/data/repository/UserRepositoryImpl.ktLapp/src/main/java/com/menteencalma/app/data/service/CloudFunctionsService.ktPapp/src/main/java/com/menteencalma/app/data/service/MockCloudFunctionsService.kt6app/src/main/java/com/menteencalma/app/di/AppModule.kt>app/src/main/java/com/menteencalma/app/domain/model/Article.kt@app/src/main/java/com/menteencalma/app/domain/model/AuthState.ktBapp/src/main/java/com/menteencalma/app/domain/model/ChatMessage.ktGapp/src/main/java/com/menteencalma/app/domain/model/GeneratedArticle.kt@app/src/main/java/com/menteencalma/app/domain/model/MoodEntry.ktEapp/src/main/java/com/menteencalma/app/domain/model/Recommendation.kt;app/src/main/java/com/menteencalma/app/domain/model/User.ktJapp/src/main/java/com/menteencalma/app/domain/repository/AuthRepository.ktNapp/src/main/java/com/menteencalma/app/domain/repository/DatabaseRepository.ktJapp/src/main/java/com/menteencalma/app/domain/repository/UserRepository.ktKapp/src/main/java/com/menteencalma/app/navigation/MenteEnCalmaNavigation.kt;app/src/main/java/com/menteencalma/app/navigation/Screen.ktUapp/src/main/java/com/menteencalma/app/presentation/components/BottomNavigationBar.kt]app/src/main/java/com/menteencalma/app/presentation/screens/admin/DatabaseMonitoringScreen.kt[app/src/main/java/com/menteencalma/app/presentation/screens/articles/ArticleDetailScreen.ktVapp/src/main/java/com/menteencalma/app/presentation/screens/articles/ArticlesScreen.ktYapp/src/main/java/com/menteencalma/app/presentation/screens/articles/ArticlesViewModel.ktWapp/src/main/java/com/menteencalma/app/presentation/screens/auth/CreateProfileScreen.ktOapp/src/main/java/com/menteencalma/app/presentation/screens/auth/LoginScreen.ktNapp/src/main/java/com/menteencalma/app/presentation/screens/chat/ChatScreen.ktQapp/src/main/java/com/menteencalma/app/presentation/screens/chat/ChatViewModel.ktZapp/src/main/java/com/menteencalma/app/presentation/screens/disclaimer/DisclaimerScreen.ktTapp/src/main/java/com/menteencalma/app/presentation/screens/doctors/DoctorsScreen.ktUapp/src/main/java/com/menteencalma/app/presentation/screens/mood/MoodTrackerScreen.ktTapp/src/main/java/com/menteencalma/app/presentation/screens/profile/ProfileScreen.ktWapp/src/main/java/com/menteencalma/app/presentation/screens/profile/ProfileViewModel.ktdapp/src/main/java/com/menteencalma/app/presentation/screens/recommendations/RecommendationsScreen.ktgapp/src/main/java/com/menteencalma/app/presentation/screens/recommendations/RecommendationsViewModel.ktRapp/src/main/java/com/menteencalma/app/presentation/screens/splash/SplashScreen.kt[app/src/main/java/com/menteencalma/app/presentation/screens/subscription/SubscribeScreen.ktOapp/src/main/java/com/menteencalma/app/presentation/viewmodels/AuthViewModel.ktXapp/src/main/java/com/menteencalma/app/presentation/viewmodels/CreateProfileViewModel.kt]app/src/main/java/com/menteencalma/app/presentation/viewmodels/DatabaseMonitoringViewModel.ktPapp/src/main/java/com/menteencalma/app/presentation/viewmodels/LoginViewModel.ktVapp/src/main/java/com/menteencalma/app/presentation/viewmodels/MoodTrackerViewModel.ktRapp/src/main/java/com/menteencalma/app/presentation/viewmodels/ProfileViewModel.ktTapp/src/main/java/com/menteencalma/app/presentation/viewmodels/SubscribeViewModel.kt8app/src/main/java/com/menteencalma/app/ui/theme/Color.kt8app/src/main/java/com/menteencalma/app/ui/theme/Theme.kt7app/src/main/java/com/menteencalma/app/ui/theme/Type.kt                                                                                  