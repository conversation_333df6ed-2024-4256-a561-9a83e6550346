package com.menteencalma.app.presentation.viewmodels;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b)\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u00a9\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0005\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u000e\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0013\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0015\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0015\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0015\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u0015\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\u0002\u0010\u001aJ\t\u0010-\u001a\u00020\u0003H\u00c6\u0003J\t\u0010.\u001a\u00020\u0015H\u00c6\u0003J\t\u0010/\u001a\u00020\u0015H\u00c6\u0003J\t\u00100\u001a\u00020\u0015H\u00c6\u0003J\t\u00101\u001a\u00020\u0015H\u00c6\u0003J\u000b\u00102\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\u000f\u00103\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000f\u00104\u001a\b\u0012\u0004\u0012\u00020\b0\u0005H\u00c6\u0003J\t\u00105\u001a\u00020\nH\u00c6\u0003J\u000b\u00106\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\u000b\u00107\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\u000b\u00108\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\u000b\u00109\u001a\u0004\u0018\u00010\u0011H\u00c6\u0003J\u000b\u0010:\u001a\u0004\u0018\u00010\u0013H\u00c6\u0003J\u00ad\u0001\u0010;\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u00052\b\b\u0002\u0010\t\u001a\u00020\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u000e2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00132\b\b\u0002\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u00152\b\b\u0002\u0010\u0017\u001a\u00020\u00152\b\b\u0002\u0010\u0018\u001a\u00020\u00152\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u000eH\u00c6\u0001J\u0013\u0010<\u001a\u00020\u00152\b\u0010=\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010>\u001a\u00020?H\u00d6\u0001J\t\u0010@\u001a\u00020\u000eH\u00d6\u0001R\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0013\u0010\u0019\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0013\u0010\u000f\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001eR\u0011\u0010\u0017\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\"R\u0011\u0010\u0016\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\"R\u0011\u0010\u0014\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\"R\u0011\u0010\u0018\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\"R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0013\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001eR\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u001cR\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010*R\u0013\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010,\u00a8\u0006A"}, d2 = {"Lcom/menteencalma/app/presentation/viewmodels/DatabaseMonitoringUiState;", "", "metrics", "Lcom/menteencalma/app/data/monitoring/DatabaseMetrics;", "alerts", "", "Lcom/menteencalma/app/data/monitoring/DatabaseAlert;", "recommendations", "Lcom/menteencalma/app/data/monitoring/OptimizationRecommendation;", "estimatedMonthlyCost", "", "migrationEstimate", "Lcom/menteencalma/app/data/migration/MigrationEstimate;", "migrationScript", "", "exportedData", "validationReport", "Lcom/menteencalma/app/data/migration/ValidationReport;", "selectedTargetDatabase", "Lcom/menteencalma/app/data/migration/TargetDatabase;", "isLoading", "", "isGeneratingEstimate", "isExporting", "isValidating", "errorMessage", "(Lcom/menteencalma/app/data/monitoring/DatabaseMetrics;Ljava/util/List;Ljava/util/List;DLcom/menteencalma/app/data/migration/MigrationEstimate;Ljava/lang/String;Ljava/lang/String;Lcom/menteencalma/app/data/migration/ValidationReport;Lcom/menteencalma/app/data/migration/TargetDatabase;ZZZZLjava/lang/String;)V", "getAlerts", "()Ljava/util/List;", "getErrorMessage", "()Ljava/lang/String;", "getEstimatedMonthlyCost", "()D", "getExportedData", "()Z", "getMetrics", "()Lcom/menteencalma/app/data/monitoring/DatabaseMetrics;", "getMigrationEstimate", "()Lcom/menteencalma/app/data/migration/MigrationEstimate;", "getMigrationScript", "getRecommendations", "getSelectedTargetDatabase", "()Lcom/menteencalma/app/data/migration/TargetDatabase;", "getValidationReport", "()Lcom/menteencalma/app/data/migration/ValidationReport;", "component1", "component10", "component11", "component12", "component13", "component14", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class DatabaseMonitoringUiState {
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.data.monitoring.DatabaseMetrics metrics = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.menteencalma.app.data.monitoring.DatabaseAlert> alerts = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.menteencalma.app.data.monitoring.OptimizationRecommendation> recommendations = null;
    private final double estimatedMonthlyCost = 0.0;
    @org.jetbrains.annotations.Nullable()
    private final com.menteencalma.app.data.migration.MigrationEstimate migrationEstimate = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String migrationScript = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String exportedData = null;
    @org.jetbrains.annotations.Nullable()
    private final com.menteencalma.app.data.migration.ValidationReport validationReport = null;
    @org.jetbrains.annotations.Nullable()
    private final com.menteencalma.app.data.migration.TargetDatabase selectedTargetDatabase = null;
    private final boolean isLoading = false;
    private final boolean isGeneratingEstimate = false;
    private final boolean isExporting = false;
    private final boolean isValidating = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    
    public DatabaseMonitoringUiState(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.data.monitoring.DatabaseMetrics metrics, @org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.data.monitoring.DatabaseAlert> alerts, @org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.data.monitoring.OptimizationRecommendation> recommendations, double estimatedMonthlyCost, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.data.migration.MigrationEstimate migrationEstimate, @org.jetbrains.annotations.Nullable()
    java.lang.String migrationScript, @org.jetbrains.annotations.Nullable()
    java.lang.String exportedData, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.data.migration.ValidationReport validationReport, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.data.migration.TargetDatabase selectedTargetDatabase, boolean isLoading, boolean isGeneratingEstimate, boolean isExporting, boolean isValidating, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.data.monitoring.DatabaseMetrics getMetrics() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.data.monitoring.DatabaseAlert> getAlerts() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.data.monitoring.OptimizationRecommendation> getRecommendations() {
        return null;
    }
    
    public final double getEstimatedMonthlyCost() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.data.migration.MigrationEstimate getMigrationEstimate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMigrationScript() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getExportedData() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.data.migration.ValidationReport getValidationReport() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.data.migration.TargetDatabase getSelectedTargetDatabase() {
        return null;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isGeneratingEstimate() {
        return false;
    }
    
    public final boolean isExporting() {
        return false;
    }
    
    public final boolean isValidating() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    public DatabaseMonitoringUiState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.data.monitoring.DatabaseMetrics component1() {
        return null;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean component13() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.data.monitoring.DatabaseAlert> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.data.monitoring.OptimizationRecommendation> component3() {
        return null;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.data.migration.MigrationEstimate component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.data.migration.ValidationReport component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.data.migration.TargetDatabase component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState copy(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.data.monitoring.DatabaseMetrics metrics, @org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.data.monitoring.DatabaseAlert> alerts, @org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.data.monitoring.OptimizationRecommendation> recommendations, double estimatedMonthlyCost, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.data.migration.MigrationEstimate migrationEstimate, @org.jetbrains.annotations.Nullable()
    java.lang.String migrationScript, @org.jetbrains.annotations.Nullable()
    java.lang.String exportedData, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.data.migration.ValidationReport validationReport, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.data.migration.TargetDatabase selectedTargetDatabase, boolean isLoading, boolean isGeneratingEstimate, boolean isExporting, boolean isValidating, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}