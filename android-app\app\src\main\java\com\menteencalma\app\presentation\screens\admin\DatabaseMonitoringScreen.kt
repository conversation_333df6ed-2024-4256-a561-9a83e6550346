package com.menteencalma.app.presentation.screens.admin

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.menteencalma.app.data.migration.TargetDatabase
import com.menteencalma.app.data.monitoring.AlertType
import com.menteencalma.app.data.monitoring.Priority
import com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DatabaseMonitoringScreen(
    onNavigateBack: () -> Unit,
    viewModel: DatabaseMonitoringViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    var selectedTab by remember { mutableIntStateOf(0) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Database Monitoring") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.resetMetrics() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "Reset Metrics")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Tabs
            TabRow(selectedTabIndex = selectedTab) {
                Tab(
                    selected = selectedTab == 0,
                    onClick = { selectedTab = 0 },
                    text = { Text("Metrics") }
                )
                Tab(
                    selected = selectedTab == 1,
                    onClick = { selectedTab = 1 },
                    text = { Text("Alerts") }
                )
                Tab(
                    selected = selectedTab == 2,
                    onClick = { selectedTab = 2 },
                    text = { Text("Migration") }
                )
            }

            // Content
            when (selectedTab) {
                0 -> MetricsTab(uiState)
                1 -> AlertsTab(uiState)
                2 -> MigrationTab(uiState, viewModel)
            }
        }
    }
}

@Composable
private fun MetricsTab(uiState: com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Cost Overview
        item {
            Card {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Estimated Monthly Cost",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Text(
                            text = "$${String.format("%.2f", uiState.estimatedMonthlyCost)}",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold,
                            color = if (uiState.estimatedMonthlyCost > 50) 
                                MaterialTheme.colorScheme.error 
                            else MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
        }

        // Operations Overview
        item {
            Card {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Database Operations",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        MetricItem(
                            label = "Reads",
                            value = uiState.metrics.totalReads.toString(),
                            icon = Icons.Default.Visibility
                        )
                        MetricItem(
                            label = "Writes",
                            value = uiState.metrics.totalWrites.toString(),
                            icon = Icons.Default.Edit
                        )
                        MetricItem(
                            label = "Errors",
                            value = uiState.metrics.totalErrors.toString(),
                            icon = Icons.Default.Error,
                            isError = uiState.metrics.totalErrors > 0
                        )
                    }
                }
            }
        }

        // Performance Metrics
        item {
            Card {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Performance",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        MetricItem(
                            label = "Avg Read Latency",
                            value = "${uiState.metrics.avgReadLatency}ms",
                            icon = Icons.Default.Speed
                        )
                        MetricItem(
                            label = "Avg Write Latency",
                            value = "${uiState.metrics.avgWriteLatency}ms",
                            icon = Icons.Default.Speed
                        )
                    }
                }
            }
        }

        // Collection Breakdown
        item {
            Card {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Reads by Collection",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                    
                    uiState.metrics.readsByCollection.forEach { (collection, count) ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(text = collection)
                            Text(text = count.toString(), fontWeight = FontWeight.Bold)
                        }
                    }
                }
            }
        }

        // Recommendations
        item {
            Card {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Optimization Recommendations",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                    
                    if (uiState.recommendations.isEmpty()) {
                        Text(
                            text = "No recommendations at this time",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    } else {
                        uiState.recommendations.forEach { recommendation ->
                            RecommendationItem(recommendation = recommendation)
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun AlertsTab(uiState: com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        if (uiState.alerts.isEmpty()) {
            item {
                Card {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                Icons.Default.CheckCircle,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(48.dp)
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text("No alerts")
                        }
                    }
                }
            }
        } else {
            items(uiState.alerts.reversed()) { alert ->
                AlertItem(alert = alert)
            }
        }
    }
}

@Composable
private fun MigrationTab(
    uiState: com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState,
    viewModel: DatabaseMonitoringViewModel
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Migration Actions
        item {
            Card {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Migration Tools",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = { viewModel.generateMigrationEstimate() },
                            enabled = !uiState.isGeneratingEstimate,
                            modifier = Modifier.weight(1f)
                        ) {
                            if (uiState.isGeneratingEstimate) {
                                CircularProgressIndicator(modifier = Modifier.size(16.dp))
                            } else {
                                Text("Estimate")
                            }
                        }
                        
                        Button(
                            onClick = { viewModel.validateDataIntegrity() },
                            enabled = !uiState.isValidating,
                            modifier = Modifier.weight(1f)
                        ) {
                            if (uiState.isValidating) {
                                CircularProgressIndicator(modifier = Modifier.size(16.dp))
                            } else {
                                Text("Validate")
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        OutlinedButton(
                            onClick = { viewModel.exportData() },
                            enabled = !uiState.isExporting,
                            modifier = Modifier.weight(1f)
                        ) {
                            if (uiState.isExporting) {
                                CircularProgressIndicator(modifier = Modifier.size(16.dp))
                            } else {
                                Text("Export Data")
                            }
                        }
                        
                        OutlinedButton(
                            onClick = { viewModel.clearMigrationData() },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("Clear")
                        }
                    }
                }
            }
        }

        // Target Database Selection
        item {
            Card {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Generate Migration Script",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = { viewModel.generateMigrationScript(TargetDatabase.SUPABASE) },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("Supabase")
                        }
                        
                        Button(
                            onClick = { viewModel.generateMigrationScript(TargetDatabase.POSTGRESQL) },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("PostgreSQL")
                        }
                        
                        Button(
                            onClick = { viewModel.generateMigrationScript(TargetDatabase.MYSQL) },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("MySQL")
                        }
                    }
                }
            }
        }

        // Migration Estimate
        uiState.migrationEstimate?.let { estimate ->
            item {
                MigrationEstimateCard(estimate = estimate)
            }
        }

        // Validation Report
        uiState.validationReport?.let { report ->
            item {
                ValidationReportCard(report = report)
            }
        }

        // Error Message
        uiState.errorMessage?.let { error ->
            item {
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Default.Error,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = error,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Spacer(modifier = Modifier.weight(1f))
                        IconButton(onClick = { viewModel.clearError() }) {
                            Icon(
                                Icons.Default.Close,
                                contentDescription = "Close",
                                tint = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun MetricItem(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    isError: Boolean = false
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            icon,
            contentDescription = null,
            tint = if (isError) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.primary
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = if (isError) MaterialTheme.colorScheme.error else Color.Unspecified
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun RecommendationItem(
    recommendation: com.menteencalma.app.data.monitoring.OptimizationRecommendation
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (recommendation.priority) {
                Priority.HIGH, Priority.CRITICAL -> MaterialTheme.colorScheme.errorContainer
                Priority.MEDIUM -> MaterialTheme.colorScheme.tertiaryContainer
                Priority.LOW -> MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = recommendation.title,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = recommendation.priority.name,
                    style = MaterialTheme.typography.labelSmall,
                    color = when (recommendation.priority) {
                        Priority.HIGH, Priority.CRITICAL -> MaterialTheme.colorScheme.onErrorContainer
                        Priority.MEDIUM -> MaterialTheme.colorScheme.onTertiaryContainer
                        Priority.LOW -> MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = recommendation.description,
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

@Composable
private fun AlertItem(
    alert: com.menteencalma.app.data.monitoring.DatabaseAlert
) {
    Card {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                when (alert.type) {
                    AlertType.ERROR -> Icons.Default.Error
                    AlertType.HIGH_USAGE -> Icons.Default.Warning
                    AlertType.PERFORMANCE -> Icons.Default.Speed
                    AlertType.COST -> Icons.Default.AttachMoney
                },
                contentDescription = null,
                tint = when (alert.type) {
                    AlertType.ERROR -> MaterialTheme.colorScheme.error
                    AlertType.HIGH_USAGE -> MaterialTheme.colorScheme.tertiary
                    AlertType.PERFORMANCE -> MaterialTheme.colorScheme.secondary
                    AlertType.COST -> MaterialTheme.colorScheme.primary
                }
            )
            Spacer(modifier = Modifier.width(12.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = alert.message,
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault())
                        .format(java.util.Date(alert.timestamp)),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun MigrationEstimateCard(
    estimate: com.menteencalma.app.data.migration.MigrationEstimate
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Migration Estimate",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Text("Users: ${estimate.estimatedUsers}")
            Text("Messages: ${estimate.estimatedMessages}")
            Text("Articles: ${estimate.estimatedArticles}")
            Text("Duration: ${String.format("%.1f", estimate.estimatedDurationHours)} hours")
            Text("Cost: $${String.format("%.2f", estimate.estimatedCostUSD)}")
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Recommended Approach:",
                style = MaterialTheme.typography.titleSmall
            )
            Text(
                text = estimate.recommendedApproach,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

@Composable
private fun ValidationReportCard(
    report: com.menteencalma.app.data.migration.ValidationReport
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Validation Report",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("Status:")
                Text(
                    text = if (report.isValid) "Valid" else "Issues Found",
                    color = if (report.isValid) 
                        MaterialTheme.colorScheme.primary 
                    else MaterialTheme.colorScheme.error,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Text("Users validated: ${report.usersValidated}")
            Text("Errors: ${report.errors.size}")
            Text("Warnings: ${report.warnings.size}")
            
            if (report.errors.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Errors:",
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.error
                )
                report.errors.take(3).forEach { error ->
                    Text(
                        text = "• $error",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
                if (report.errors.size > 3) {
                    Text(
                        text = "... and ${report.errors.size - 3} more",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }
        }
    }
}
