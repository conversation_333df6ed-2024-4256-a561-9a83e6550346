rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() && request.auth.token.email_verified == true;
    }
    
    function isValidUserData() {
      return request.resource.data.keys().hasAll(['email', 'displayName', 'therapistGender']) &&
             request.resource.data.email == request.auth.token.email &&
             request.resource.data.displayName is string &&
             request.resource.data.displayName.size() > 0 &&
             request.resource.data.therapistGender in ['aurora', 'alejandro'] &&
             // Validate usage limits structure
             request.resource.data.keys().hasAll(['dailyLimits', 'monthlyLimits', 'usageToday', 'usageThisMonth']);
    }
    
    function isValidChatMessage() {
      return request.resource.data.keys().hasAll(['message', 'timestamp', 'sender']) &&
             request.resource.data.message is string &&
             request.resource.data.message.size() > 0 &&
             request.resource.data.sender in ['user', 'ai'] &&
             request.resource.data.timestamp is timestamp;
    }
    
    function isValidMoodEntry() {
      return request.resource.data.keys().hasAll(['mood', 'intensity', 'timestamp', 'date']) &&
             request.resource.data.mood in ['VERY_SAD', 'SAD', 'NEUTRAL', 'HAPPY', 'VERY_HAPPY'] &&
             request.resource.data.intensity is int &&
             request.resource.data.intensity >= 1 &&
             request.resource.data.intensity <= 10 &&
             request.resource.data.timestamp is timestamp &&
             request.resource.data.date is string;
    }
    
    function isValidArticle() {
      return request.resource.data.keys().hasAll(['title', 'content', 'category', 'wordCount']) &&
             request.resource.data.title is string &&
             request.resource.data.title.size() > 0 &&
             request.resource.data.content is string &&
             request.resource.data.content.size() > 0 &&
             request.resource.data.category is string &&
             request.resource.data.wordCount is int &&
             request.resource.data.wordCount <= 120; // Maximum 120 words for articles
    }

    function isValidRecommendation() {
      return request.resource.data.keys().hasAll(['title', 'description', 'category']) &&
             request.resource.data.title is string &&
             request.resource.data.title.size() > 0 &&
             request.resource.data.description is string &&
             request.resource.data.description.size() > 0 &&
             request.resource.data.category is string &&
             // Validate word count if present
             (!request.resource.data.keys().hasAny(['wordCount']) ||
              (request.resource.data.wordCount is int && request.resource.data.wordCount <= 80)); // Maximum 80 words for recommendations
    }
    
    // Users collection - main user profiles
    match /users/{userId} {
      allow read: if isOwner(userId);
      allow create: if isOwner(userId) && isValidUser() && isValidUserData();
      allow update: if isOwner(userId) && isValidUser();
      allow delete: if false; // Users cannot delete their own profiles
      
      // Chat messages subcollection
      match /chatMessages/{messageId} {
        allow read, write: if isOwner(userId) && isValidUser();
        allow create: if isOwner(userId) && isValidUser() && isValidChatMessage();
        allow update: if false; // Messages are immutable
        allow delete: if isOwner(userId) && isValidUser();
      }
      
      // Mood entries subcollection
      match /moodEntries/{entryId} {
        allow read, write: if isOwner(userId) && isValidUser();
        allow create: if isOwner(userId) && isValidUser() && isValidMoodEntry();
        allow update: if isOwner(userId) && isValidUser() && isValidMoodEntry();
        allow delete: if isOwner(userId) && isValidUser();
      }
      
      // Saved articles subcollection
      match /savedArticles/{articleId} {
        allow read, write: if isOwner(userId) && isValidUser();
        allow create: if isOwner(userId) && isValidUser() && isValidArticle();
        allow update: if false; // Articles are immutable once created
        allow delete: if isOwner(userId) && isValidUser();
      }
      
      // Recommendations subcollection (if stored)
      match /recommendations/{recommendationId} {
        allow read, write: if isOwner(userId) && isValidUser();
        allow create: if isOwner(userId) && isValidUser() && isValidRecommendation();
        allow update: if false; // Recommendations are immutable
        allow delete: if isOwner(userId) && isValidUser();
      }
      
      // Subscription data subcollection
      match /subscriptionData/{subscriptionId} {
        allow read: if isOwner(userId) && isValidUser();
        allow write: if false; // Only Cloud Functions can write subscription data
      }
    }
    
    // App configuration (read-only for clients)
    match /config/{configId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only admins can write config
    }
    
    // Public content (if any)
    match /publicContent/{contentId} {
      allow read: if true; // Public read access
      allow write: if false; // Only admins can write
    }
    
    // Analytics and usage data (write-only for clients)
    match /analytics/{analyticsId} {
      allow read: if false; // Only admins can read analytics
      allow write: if isAuthenticated(); // Users can write their own analytics
    }
    
    // Feedback and support tickets
    match /feedback/{feedbackId} {
      allow read: if isAuthenticated() && 
                     resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && 
                       request.resource.data.userId == request.auth.uid &&
                       request.resource.data.keys().hasAll(['message', 'timestamp', 'userId']) &&
                       request.resource.data.message is string &&
                       request.resource.data.message.size() > 0;
      allow update, delete: if false; // Feedback is immutable
    }
    
    // Emergency contacts and resources (read-only)
    match /emergencyResources/{resourceId} {
      allow read: if true; // Public access for emergency information
      allow write: if false; // Only admins can manage emergency resources
    }
    
    // Therapist information (read-only)
    match /therapists/{therapistId} {
      allow read: if isAuthenticated(); // Authenticated users can read therapist info
      allow write: if false; // Only admins can manage therapist data
    }
    
    // Subscription plans and pricing (read-only)
    match /subscriptionPlans/{planId} {
      allow read: if true; // Public access to pricing information
      allow write: if false; // Only admins can manage subscription plans
    }
    
    // Default deny rule for any other documents
    match /{document=**} {
      allow read, write: if false;
    }
  }
}

// Storage rules (if using Firebase Storage)
// service firebase.storage {
//   match /b/{bucket}/o {
//     // User profile images
//     match /users/{userId}/profile/{imageId} {
//       allow read: if true; // Profile images can be public
//       allow write: if request.auth != null && 
//                       request.auth.uid == userId &&
//                       request.resource.size < 5 * 1024 * 1024 && // 5MB limit
//                       request.resource.contentType.matches('image/.*');
//     }
//     
//     // Default deny
//     match /{allPaths=**} {
//       allow read, write: if false;
//     }
//   }
// }
