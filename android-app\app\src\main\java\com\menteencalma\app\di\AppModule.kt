package com.menteencalma.app.di

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.functions.FirebaseFunctions
import com.menteencalma.app.data.repository.AuthRepositoryImpl
import com.menteencalma.app.data.repository.UserRepositoryImpl
import com.menteencalma.app.data.repository.FirebaseDatabaseRepository
import com.menteencalma.app.data.repository.MonitoredDatabaseRepository
import com.menteencalma.app.data.monitoring.DatabaseMonitoringService
import com.menteencalma.app.data.migration.DatabaseMigrationService
import com.menteencalma.app.data.service.CloudFunctionsService
import com.menteencalma.app.data.service.MockCloudFunctionsService
import com.menteencalma.app.domain.repository.AuthRepository
import com.menteencalma.app.domain.repository.UserRepository
import com.menteencalma.app.domain.repository.DatabaseRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton
import javax.inject.Qualifier

/**
 * Qualifiers para distinguir entre implementaciones
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class FirebaseDatabase

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class MonitoredDatabase

@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    @Provides
    @Singleton
    fun provideFirebaseAuth(): FirebaseAuth = FirebaseAuth.getInstance()

    @Provides
    @Singleton
    fun provideFirebaseFirestore(): FirebaseFirestore = FirebaseFirestore.getInstance()

    @Provides
    @Singleton
    fun provideFirebaseFunctions(): FirebaseFunctions = FirebaseFunctions.getInstance()

    @Provides
    @Singleton
    fun provideAuthRepository(
        firebaseAuth: FirebaseAuth
    ): AuthRepository = AuthRepositoryImpl(firebaseAuth)

    @Provides
    @Singleton
    fun provideUserRepository(
        firestore: FirebaseFirestore
    ): UserRepository = UserRepositoryImpl(firestore)

    // ==================== NEW DATABASE ARCHITECTURE ====================

    @Provides
    @Singleton
    fun provideDatabaseMonitoringService(): DatabaseMonitoringService = DatabaseMonitoringService()

    @Provides
    @Singleton
    @FirebaseDatabase
    fun provideFirebaseDatabaseRepository(
        firestore: FirebaseFirestore
    ): DatabaseRepository = FirebaseDatabaseRepository(firestore)

    @Provides
    @Singleton
    @MonitoredDatabase
    fun provideMonitoredDatabaseRepository(
        @FirebaseDatabase actualRepository: DatabaseRepository,
        monitoringService: DatabaseMonitoringService
    ): DatabaseRepository = MonitoredDatabaseRepository(actualRepository, monitoringService)

    @Provides
    @Singleton
    fun provideDatabaseRepository(
        @MonitoredDatabase repository: DatabaseRepository
    ): DatabaseRepository = repository

    @Provides
    @Singleton
    fun provideDatabaseMigrationService(
        databaseRepository: DatabaseRepository
    ): DatabaseMigrationService = DatabaseMigrationService(databaseRepository)

    @Provides
    @Singleton
    fun provideMockCloudFunctionsService(): MockCloudFunctionsService = MockCloudFunctionsService()

    @Provides
    @Singleton
    fun provideCloudFunctionsService(
        functions: FirebaseFunctions,
        mockService: MockCloudFunctionsService
    ): CloudFunctionsService = CloudFunctionsService(functions, mockService)
}
