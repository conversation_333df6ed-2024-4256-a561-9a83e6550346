package com.menteencalma.app.data.repository;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000~\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0010\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J$\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\n\u0010\u000bJ*\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0010\u0010\u0011J&\u0010\u0012\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00130\u00062\u0006\u0010\u0014\u001a\u00020\u0015H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0016\u0010\u0017JF\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u000e0\u00062\b\u0010\u0019\u001a\u0004\u0018\u00010\u00152\b\u0010\u001a\u001a\u0004\u0018\u00010\u001b2\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u001dH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001f\u0010 J<\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\"0\u000e0\u00062\u0006\u0010#\u001a\u00020\u00152\u0006\u0010\u001c\u001a\u00020\u001d2\b\u0010$\u001a\u0004\u0018\u00010\u0015H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b%\u0010&J&\u0010\'\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010(0\u00062\u0006\u0010#\u001a\u00020\u0015H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b)\u0010\u0017J8\u0010*\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020,0+0\u00062\u0006\u0010-\u001a\u00020.2\u0006\u0010/\u001a\u00020.H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b0\u00101J@\u00102\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020,0+0\u00062\u0006\u0010#\u001a\u00020\u00152\u0006\u0010-\u001a\u00020.2\u0006\u0010/\u001a\u00020.H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b3\u00104J&\u00105\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u00062\u0006\u0010#\u001a\u00020\u0015H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b6\u0010\u0017J8\u00107\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u00108\u001a\u00020\u00152\u0012\u00109\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020,0+H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b:\u0010;J2\u0010<\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010#\u001a\u00020\u00152\f\u0010=\u001a\b\u0012\u0004\u0012\u00020\u00150\u000eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b>\u0010?J\u001c\u0010@\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\"0\u000e0A2\u0006\u0010#\u001a\u00020\u0015H\u0016J\u0018\u0010B\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0A2\u0006\u0010#\u001a\u00020\u0015H\u0016J,\u0010C\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010#\u001a\u00020\u00152\u0006\u0010\u0014\u001a\u00020\u0015H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bD\u0010EJR\u0010F\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u000e0\u00062\b\u0010G\u001a\u0004\u0018\u00010H2\b\u0010I\u001a\u0004\u0018\u00010\u00152\b\u0010J\u001a\u0004\u0018\u00010\u00152\b\u0010K\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u001c\u001a\u00020\u001dH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bL\u0010MJ$\u0010N\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010O\u001a\u00020\"H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bP\u0010QJ$\u0010R\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010S\u001a\u00020(H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bT\u0010UJ$\u0010V\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bW\u0010\u000bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006X"}, d2 = {"Lcom/menteencalma/app/data/repository/FirebaseDatabaseRepository;", "Lcom/menteencalma/app/domain/repository/DatabaseRepository;", "firestore", "Lcom/google/firebase/firestore/FirebaseFirestore;", "(Lcom/google/firebase/firestore/FirebaseFirestore;)V", "createUser", "Lkotlin/Result;", "", "user", "Lcom/menteencalma/app/domain/model/User;", "createUser-gIAlu-s", "(Lcom/menteencalma/app/domain/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "executeTransaction", "operations", "", "Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation;", "executeTransaction-gIAlu-s", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getArticle", "Lcom/menteencalma/app/domain/model/Article;", "articleId", "", "getArticle-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getArticles", "category", "isPremium", "", "limit", "", "offset", "getArticles-yxL6bBk", "(Ljava/lang/String;Ljava/lang/Boolean;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getChatMessages", "Lcom/menteencalma/app/domain/model/ChatMessage;", "userId", "lastMessageId", "getChatMessages-BWLJW6A", "(Ljava/lang/String;ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSubscription", "Lcom/menteencalma/app/domain/model/Subscription;", "getSubscription-gIAlu-s", "getSubscriptionStats", "", "", "startDate", "", "endDate", "getSubscriptionStats-0E7RQCE", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUsageMetrics", "getUsageMetrics-BWLJW6A", "(Ljava/lang/String;JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUser", "getUser-gIAlu-s", "logEvent", "eventName", "parameters", "logEvent-0E7RQCE", "(Ljava/lang/String;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markMessagesAsRead", "messageIds", "markMessagesAsRead-0E7RQCE", "(Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "observeChatMessages", "Lkotlinx/coroutines/flow/Flow;", "observeUser", "recordArticleRead", "recordArticleRead-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchUsers", "ageRange", "Lkotlin/ranges/IntRange;", "gender", "therapistPreference", "subscriptionStatus", "searchUsers-hUnOzRk", "(Lkotlin/ranges/IntRange;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "sendChatMessage", "message", "sendChatMessage-gIAlu-s", "(Lcom/menteencalma/app/domain/model/ChatMessage;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSubscription", "subscription", "updateSubscription-gIAlu-s", "(Lcom/menteencalma/app/domain/model/Subscription;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUser", "updateUser-gIAlu-s", "app_debug"})
public final class FirebaseDatabaseRepository implements com.menteencalma.app.domain.repository.DatabaseRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.google.firebase.firestore.FirebaseFirestore firestore = null;
    
    @javax.inject.Inject()
    public FirebaseDatabaseRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<com.menteencalma.app.domain.model.User> observeUser(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.List<com.menteencalma.app.domain.model.ChatMessage>> observeChatMessages(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
        return null;
    }
}