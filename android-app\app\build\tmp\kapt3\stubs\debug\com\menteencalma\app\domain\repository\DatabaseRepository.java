package com.menteencalma.app.domain.repository;

/**
 * Abstracción de la base de datos para facilitar migraciones futuras
 * Implementaciones: FirebaseRepository, SupabaseRepository, PostgreSQLRepository
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000r\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0000\n\u0002\u0010\t\n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0011\bf\u0018\u00002\u00020\u0001:\u0001TJ$\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0007\u0010\bJ*\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\r\u0010\u000eJ&\u0010\u000f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00100\u00032\u0006\u0010\u0011\u001a\u00020\u0012H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0013\u0010\u0014JN\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u000b0\u00032\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00122\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00182\b\b\u0002\u0010\u0019\u001a\u00020\u001a2\b\b\u0002\u0010\u001b\u001a\u00020\u001aH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001c\u0010\u001dJ@\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u000b0\u00032\u0006\u0010 \u001a\u00020\u00122\b\b\u0002\u0010\u0019\u001a\u00020\u001a2\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u0012H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\"\u0010#J&\u0010$\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010%0\u00032\u0006\u0010 \u001a\u00020\u0012H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b&\u0010\u0014J8\u0010\'\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010(0\u00032\u0006\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020*H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b,\u0010-J@\u0010.\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010(0\u00032\u0006\u0010 \u001a\u00020\u00122\u0006\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020*H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b/\u00100J&\u00101\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00060\u00032\u0006\u0010 \u001a\u00020\u0012H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b2\u0010\u0014J8\u00103\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u00104\u001a\u00020\u00122\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00010(H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b6\u00107J2\u00108\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010 \u001a\u00020\u00122\f\u00109\u001a\b\u0012\u0004\u0012\u00020\u00120\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b:\u0010;J\u001c\u0010<\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u000b0=2\u0006\u0010 \u001a\u00020\u0012H&J\u0018\u0010>\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00060=2\u0006\u0010 \u001a\u00020\u0012H&J,\u0010?\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010 \u001a\u00020\u00122\u0006\u0010\u0011\u001a\u00020\u0012H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b@\u0010AJ\\\u0010B\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u000b0\u00032\n\b\u0002\u0010C\u001a\u0004\u0018\u00010D2\n\b\u0002\u0010E\u001a\u0004\u0018\u00010\u00122\n\b\u0002\u0010F\u001a\u0004\u0018\u00010\u00122\n\b\u0002\u0010G\u001a\u0004\u0018\u00010\u00122\b\b\u0002\u0010\u0019\u001a\u00020\u001aH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bH\u0010IJ$\u0010J\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010K\u001a\u00020\u001fH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bL\u0010MJ$\u0010N\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010O\u001a\u00020%H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bP\u0010QJ$\u0010R\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bS\u0010\b\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006U"}, d2 = {"Lcom/menteencalma/app/domain/repository/DatabaseRepository;", "", "createUser", "Lkotlin/Result;", "", "user", "Lcom/menteencalma/app/domain/model/User;", "createUser-gIAlu-s", "(Lcom/menteencalma/app/domain/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "executeTransaction", "operations", "", "Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation;", "executeTransaction-gIAlu-s", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getArticle", "Lcom/menteencalma/app/domain/model/Article;", "articleId", "", "getArticle-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getArticles", "category", "isPremium", "", "limit", "", "offset", "getArticles-yxL6bBk", "(Ljava/lang/String;Ljava/lang/Boolean;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getChatMessages", "Lcom/menteencalma/app/domain/model/ChatMessage;", "userId", "lastMessageId", "getChatMessages-BWLJW6A", "(Ljava/lang/String;ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSubscription", "Lcom/menteencalma/app/domain/model/Subscription;", "getSubscription-gIAlu-s", "getSubscriptionStats", "", "startDate", "", "endDate", "getSubscriptionStats-0E7RQCE", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUsageMetrics", "getUsageMetrics-BWLJW6A", "(Ljava/lang/String;JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUser", "getUser-gIAlu-s", "logEvent", "eventName", "parameters", "logEvent-0E7RQCE", "(Ljava/lang/String;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markMessagesAsRead", "messageIds", "markMessagesAsRead-0E7RQCE", "(Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "observeChatMessages", "Lkotlinx/coroutines/flow/Flow;", "observeUser", "recordArticleRead", "recordArticleRead-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchUsers", "ageRange", "Lkotlin/ranges/IntRange;", "gender", "therapistPreference", "subscriptionStatus", "searchUsers-hUnOzRk", "(Lkotlin/ranges/IntRange;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "sendChatMessage", "message", "sendChatMessage-gIAlu-s", "(Lcom/menteencalma/app/domain/model/ChatMessage;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSubscription", "subscription", "updateSubscription-gIAlu-s", "(Lcom/menteencalma/app/domain/model/Subscription;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUser", "updateUser-gIAlu-s", "DatabaseOperation", "app_debug"})
public abstract interface DatabaseRepository {
    
    /**
     * Observa cambios en un usuario en tiempo real
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.menteencalma.app.domain.model.User> observeUser(@org.jetbrains.annotations.NotNull()
    java.lang.String userId);
    
    /**
     * Observa mensajes de chat en tiempo real
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.menteencalma.app.domain.model.ChatMessage>> observeChatMessages(@org.jetbrains.annotations.NotNull()
    java.lang.String userId);
    
    /**
     * Operación de base de datos para transacciones
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\u0004\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0004\u0007\b\t\n\u00a8\u0006\u000b"}, d2 = {"Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation;", "", "()V", "CreateMessage", "CreateUser", "UpdateSubscription", "UpdateUser", "Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation$CreateMessage;", "Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation$CreateUser;", "Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation$UpdateSubscription;", "Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation$UpdateUser;", "app_debug"})
    public static abstract class DatabaseOperation {
        
        private DatabaseOperation() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation$CreateMessage;", "Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation;", "message", "Lcom/menteencalma/app/domain/model/ChatMessage;", "(Lcom/menteencalma/app/domain/model/ChatMessage;)V", "getMessage", "()Lcom/menteencalma/app/domain/model/ChatMessage;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
        public static final class CreateMessage extends com.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation {
            @org.jetbrains.annotations.NotNull()
            private final com.menteencalma.app.domain.model.ChatMessage message = null;
            
            public CreateMessage(@org.jetbrains.annotations.NotNull()
            com.menteencalma.app.domain.model.ChatMessage message) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.menteencalma.app.domain.model.ChatMessage getMessage() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.menteencalma.app.domain.model.ChatMessage component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation.CreateMessage copy(@org.jetbrains.annotations.NotNull()
            com.menteencalma.app.domain.model.ChatMessage message) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation$CreateUser;", "Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation;", "user", "Lcom/menteencalma/app/domain/model/User;", "(Lcom/menteencalma/app/domain/model/User;)V", "getUser", "()Lcom/menteencalma/app/domain/model/User;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
        public static final class CreateUser extends com.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation {
            @org.jetbrains.annotations.NotNull()
            private final com.menteencalma.app.domain.model.User user = null;
            
            public CreateUser(@org.jetbrains.annotations.NotNull()
            com.menteencalma.app.domain.model.User user) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.menteencalma.app.domain.model.User getUser() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.menteencalma.app.domain.model.User component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation.CreateUser copy(@org.jetbrains.annotations.NotNull()
            com.menteencalma.app.domain.model.User user) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation$UpdateSubscription;", "Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation;", "subscription", "Lcom/menteencalma/app/domain/model/Subscription;", "(Lcom/menteencalma/app/domain/model/Subscription;)V", "getSubscription", "()Lcom/menteencalma/app/domain/model/Subscription;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
        public static final class UpdateSubscription extends com.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation {
            @org.jetbrains.annotations.NotNull()
            private final com.menteencalma.app.domain.model.Subscription subscription = null;
            
            public UpdateSubscription(@org.jetbrains.annotations.NotNull()
            com.menteencalma.app.domain.model.Subscription subscription) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.menteencalma.app.domain.model.Subscription getSubscription() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.menteencalma.app.domain.model.Subscription component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation.UpdateSubscription copy(@org.jetbrains.annotations.NotNull()
            com.menteencalma.app.domain.model.Subscription subscription) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation$UpdateUser;", "Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation;", "user", "Lcom/menteencalma/app/domain/model/User;", "(Lcom/menteencalma/app/domain/model/User;)V", "getUser", "()Lcom/menteencalma/app/domain/model/User;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
        public static final class UpdateUser extends com.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation {
            @org.jetbrains.annotations.NotNull()
            private final com.menteencalma.app.domain.model.User user = null;
            
            public UpdateUser(@org.jetbrains.annotations.NotNull()
            com.menteencalma.app.domain.model.User user) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.menteencalma.app.domain.model.User getUser() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.menteencalma.app.domain.model.User component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation.UpdateUser copy(@org.jetbrains.annotations.NotNull()
            com.menteencalma.app.domain.model.User user) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
    }
    
    /**
     * Abstracción de la base de datos para facilitar migraciones futuras
     * Implementaciones: FirebaseRepository, SupabaseRepository, PostgreSQLRepository
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}