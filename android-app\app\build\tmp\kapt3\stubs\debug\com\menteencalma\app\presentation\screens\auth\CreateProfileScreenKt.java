package com.menteencalma.app.presentation.screens.auth;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0006\u001a.\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u0007\u001a$\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u000bH\u0003\u001a|\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000e2\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u000b2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u000b2\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u000b2\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u000b2\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0003\u001a\b\u0010\u0015\u001a\u00020\u0001H\u0003\u001aF\u0010\u0016\u001a\u00020\u00012\u0006\u0010\u0017\u001a\u00020\t2\u0006\u0010\u0018\u001a\u00020\t2\u0006\u0010\u0019\u001a\u00020\t2\u0006\u0010\u001a\u001a\u00020\u001b2\u0012\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\u001d\u001a\u00020\u001bH\u0003\u001a.\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u001f\u001a\u00020\t2\u0012\u0010 \u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\u001d\u001a\u00020\u001bH\u0003\u00a8\u0006!"}, d2 = {"CreateProfileScreen", "", "onProfileCreated", "Lkotlin/Function0;", "onNavigateBack", "createProfileViewModel", "Lcom/menteencalma/app/presentation/viewmodels/CreateProfileViewModel;", "GenderSelection", "selectedGender", "", "onGenderSelected", "Lkotlin/Function1;", "ProfileForm", "uiState", "Lcom/menteencalma/app/presentation/viewmodels/CreateProfileUiState;", "onDisplayNameChange", "onAgeChange", "onGenderChange", "onTherapistGenderChange", "onCreateProfile", "onClearError", "ProfileHeader", "TherapistCard", "name", "description", "value", "isSelected", "", "onSelected", "isError", "TherapistSelection", "selectedTherapist", "onTherapistSelected", "app_debug"})
public final class CreateProfileScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void CreateProfileScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onProfileCreated, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.presentation.viewmodels.CreateProfileViewModel createProfileViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ProfileHeader() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ProfileForm(com.menteencalma.app.presentation.viewmodels.CreateProfileUiState uiState, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onDisplayNameChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onAgeChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onGenderChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTherapistGenderChange, kotlin.jvm.functions.Function0<kotlin.Unit> onCreateProfile, kotlin.jvm.functions.Function0<kotlin.Unit> onClearError) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void GenderSelection(java.lang.String selectedGender, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onGenderSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TherapistSelection(java.lang.String selectedTherapist, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTherapistSelected, boolean isError) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TherapistCard(java.lang.String name, java.lang.String description, java.lang.String value, boolean isSelected, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSelected, boolean isError) {
    }
}