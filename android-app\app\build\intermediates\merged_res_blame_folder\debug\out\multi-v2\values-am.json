{"logs": [{"outputFile": "com.menteencalma.app-mergeDebugResources-3:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\0a2f82cac04b16c6074a060829451b01\\transformed\\play-services-basement-18.2.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4495", "endColumns": "131", "endOffsets": "4622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\98bcb59e84dea2106bcefc907e055731\\transformed\\material3-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,276,384,491,585,675,782,910,1020,1149,1231,1329,1416,1509,1619,1738,1841,1964,2089,2213,2361,2477,2590,2704,2819,2907,3002,3112,3231,3326,3428,3530,3650,3776,3880,3976,4050,4143,4235,4319,4404,4506,4587,4670,4770,4867,4962,5057,5142,5244,5343,5442,5560,5641,5742", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "161,271,379,486,580,670,777,905,1015,1144,1226,1324,1411,1504,1614,1733,1836,1959,2084,2208,2356,2472,2585,2699,2814,2902,2997,3107,3226,3321,3423,3525,3645,3771,3875,3971,4045,4138,4230,4314,4399,4501,4582,4665,4765,4862,4957,5052,5137,5239,5338,5437,5555,5636,5737,5834"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6401,6512,6622,6730,6837,6931,7021,7128,7256,7366,7495,7577,7675,7762,7855,7965,8084,8187,8310,8435,8559,8707,8823,8936,9050,9165,9253,9348,9458,9577,9672,9774,9876,9996,10122,10226,10322,10396,10489,10581,10665,10750,10852,10933,11016,11116,11213,11308,11403,11488,11590,11689,11788,11906,11987,12088", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "6507,6617,6725,6832,6926,7016,7123,7251,7361,7490,7572,7670,7757,7850,7960,8079,8182,8305,8430,8554,8702,8818,8931,9045,9160,9248,9343,9453,9572,9667,9769,9871,9991,10117,10221,10317,10391,10484,10576,10660,10745,10847,10928,11011,11111,11208,11303,11398,11483,11585,11684,11783,11901,11982,12083,12180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d60095cfcd5f6da5b72a1052224dd592\\transformed\\ui-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,922,988,1066,1147,1217,1297,1362", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,917,983,1061,1142,1212,1292,1357,1473"}, "to": {"startLines": "36,37,57,58,59,63,64,121,122,123,124,125,126,128,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3410,3493,5672,5764,5860,6240,6318,12185,12267,12345,12411,12477,12555,12716,12887,12967,13032", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "3488,3565,5759,5855,5937,6313,6396,12262,12340,12406,12472,12550,12631,12781,12962,13027,13143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\b7c5e14f30f524dc22cc8925fd453a32\\transformed\\core-1.12.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "29,30,31,32,33,34,35,129", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2723,2816,2916,3013,3112,3208,3310,12786", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "2811,2911,3008,3107,3203,3305,3405,12882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\812ae415204e3b60a6d9571a5434b24a\\transformed\\appcompat-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,12636", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,12711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ca611de3937d8b7fad81038213315e33\\transformed\\foundation-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,85", "endOffsets": "137,223"}, "to": {"startLines": "133,134", "startColumns": "4,4", "startOffsets": "13148,13235", "endColumns": "86,85", "endOffsets": "13230,13316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ec6bd2faa3e0a998fc0a19c7dd0c22ed\\transformed\\browser-1.4.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "56,60,61,62", "startColumns": "4,4,4,4", "startOffsets": "5576,5942,6037,6143", "endColumns": "95,94,105,96", "endOffsets": "5667,6032,6138,6235"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\bc87254a1d5a4bb9d2cd1a5740dba329\\transformed\\play-services-base-18.1.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3570,3672,3811,3933,4035,4162,4285,4393,4627,4755,4858,5003,5126,5261,5388,5448,5505", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "3667,3806,3928,4030,4157,4280,4388,4490,4750,4853,4998,5121,5256,5383,5443,5500,5571"}}]}]}