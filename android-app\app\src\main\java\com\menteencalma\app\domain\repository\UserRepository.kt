package com.menteencalma.app.domain.repository

import com.menteencalma.app.domain.model.User
import kotlinx.coroutines.flow.Flow

/**
 * Repositorio para operaciones de usuario
 */
interface UserRepository {
    
    /**
     * Obtiene el perfil del usuario por ID
     */
    suspend fun getUserProfile(userId: String): User?
    
    /**
     * Crea un nuevo perfil de usuario
     */
    suspend fun createUserProfile(user: User): Result<Unit>
    
    /**
     * Actualiza el perfil del usuario
     */
    suspend fun updateUserProfile(user: User): Result<Unit>
    
    /**
     * Observa los cambios en el perfil del usuario
     */
    fun observeUserProfile(userId: String): Flow<User?>
    
    /**
     * Actualiza el estado de suscripción del usuario
     */
    suspend fun updateSubscriptionStatus(
        userId: String,
        status: String,
        platform: String?,
        expiresAt: Long?
    ): Result<Unit>
    
    /**
     * Elimina el perfil del usuario
     */
    suspend fun deleteUserProfile(userId: String): Result<Unit>
}
