package com.menteencalma.app.presentation.screens.profile;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000D\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\u001a\u001e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a\u0012\u0010\u0006\u001a\u00020\u00012\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0003\u001a\u009a\u0001\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u00032\u0006\u0010\u000b\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u000e2\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00102\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00102\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00102\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\u00102\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\u0010H\u0003\u001a\u001c\u0010\u0016\u001a\u00020\u00012\b\u0010\u0017\u001a\u0004\u0018\u00010\u00182\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0003\u001a<\u0010\u0019\u001a\u00020\u00012\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u001d\u001a\u00020\u001eH\u0007\u001a,\u0010\u001f\u001a\u00020\u00012\u0006\u0010 \u001a\u00020!2\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a\u0010\u0010#\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0003\u001a>\u0010$\u001a\u00020\u00012\u0006\u0010%\u001a\u00020\u00032\u0006\u0010&\u001a\u00020\u000e2\f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\n\u001a\u00020\u00032\u0006\u0010(\u001a\u00020\u00032\u0006\u0010)\u001a\u00020\u000eH\u0003\u00a8\u0006*"}, d2 = {"ErrorCard", "", "message", "", "onDismiss", "Lkotlin/Function0;", "LoadingState", "modifier", "Landroidx/compose/ui/Modifier;", "ProfileEditCard", "displayName", "age", "therapistGender", "isUpdating", "", "onDisplayNameChange", "Lkotlin/Function1;", "onAgeChange", "onTherapistGenderChange", "onSaveChanges", "getTherapistDisplayName", "getTherapistDescription", "ProfileHeader", "user", "Lcom/menteencalma/app/domain/model/User;", "ProfileScreen", "onNavigateToSubscribe", "onNavigateToSettings", "onLogout", "profileViewModel", "Lcom/menteencalma/app/presentation/viewmodels/ProfileViewModel;", "SubscriptionCard", "subscriptionInfo", "Lcom/menteencalma/app/presentation/viewmodels/SubscriptionDisplayInfo;", "onManageSubscription", "SuccessCard", "TherapistSelectionCard", "therapistId", "isSelected", "onSelect", "description", "enabled", "app_debug"})
public final class ProfileScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ProfileScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSubscribe, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onLogout, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.presentation.viewmodels.ProfileViewModel profileViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void LoadingState(androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ProfileHeader(com.menteencalma.app.domain.model.User user, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void SubscriptionCard(com.menteencalma.app.presentation.viewmodels.SubscriptionDisplayInfo subscriptionInfo, kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSubscribe, kotlin.jvm.functions.Function0<kotlin.Unit> onManageSubscription) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ProfileEditCard(java.lang.String displayName, java.lang.String age, java.lang.String therapistGender, boolean isUpdating, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onDisplayNameChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onAgeChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTherapistGenderChange, kotlin.jvm.functions.Function0<kotlin.Unit> onSaveChanges, kotlin.jvm.functions.Function1<? super java.lang.String, java.lang.String> getTherapistDisplayName, kotlin.jvm.functions.Function1<? super java.lang.String, java.lang.String> getTherapistDescription) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TherapistSelectionCard(java.lang.String therapistId, boolean isSelected, kotlin.jvm.functions.Function0<kotlin.Unit> onSelect, java.lang.String displayName, java.lang.String description, boolean enabled) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ErrorCard(java.lang.String message, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void SuccessCard(java.lang.String message) {
    }
}