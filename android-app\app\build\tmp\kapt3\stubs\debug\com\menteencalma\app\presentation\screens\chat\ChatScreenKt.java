package com.menteencalma.app.presentation.screens.chat;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000F\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\n\u001aB\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\tH\u0003\u001a\u0010\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\rH\u0003\u001a(\u0010\u000e\u001a\u00020\u00012\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\r0\u00102\u0006\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0013\u001a\u00020\u0014H\u0003\u001a \u0010\u0015\u001a\u00020\u00012\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\u0017\u001a\u00020\u0018H\u0007\u001a \u0010\u0019\u001a\u00020\u00012\u0006\u0010\u001a\u001a\u00020\u00032\u0006\u0010\u001b\u001a\u00020\u00032\u0006\u0010\u001c\u001a\u00020\tH\u0003\u001a\u0010\u0010\u001d\u001a\u00020\u00012\u0006\u0010\u001a\u001a\u00020\u0003H\u0003\u001a(\u0010\u001e\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\u00032\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\u0013\u001a\u00020\u0014H\u0003\u001a\b\u0010 \u001a\u00020\u0001H\u0003\u001a\u0016\u0010!\u001a\u00020\u00012\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0003\u00a8\u0006\""}, d2 = {"ChatInputBar", "", "messageText", "", "onMessageTextChange", "Lkotlin/Function1;", "onSendMessage", "Lkotlin/Function0;", "isSending", "", "enabled", "ChatMessageBubble", "message", "Lcom/menteencalma/app/domain/model/ChatMessage;", "ChatMessagesList", "messages", "", "listState", "Landroidx/compose/foundation/lazy/LazyListState;", "modifier", "Landroidx/compose/ui/Modifier;", "ChatScreen", "onNavigateToSubscribe", "chatViewModel", "Lcom/menteencalma/app/presentation/screens/chat/ChatViewModel;", "ChatTopBar", "therapistName", "therapistAvatar", "isOnline", "EmptyState", "ErrorSnackbar", "onDismiss", "LoadingState", "PaywallBottomBar", "app_debug"})
public final class ChatScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ChatScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSubscribe, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.presentation.screens.chat.ChatViewModel chatViewModel) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void ChatTopBar(java.lang.String therapistName, java.lang.String therapistAvatar, boolean isOnline) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ChatMessagesList(java.util.List<com.menteencalma.app.domain.model.ChatMessage> messages, androidx.compose.foundation.lazy.LazyListState listState, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ChatMessageBubble(com.menteencalma.app.domain.model.ChatMessage message) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ChatInputBar(java.lang.String messageText, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMessageTextChange, kotlin.jvm.functions.Function0<kotlin.Unit> onSendMessage, boolean isSending, boolean enabled) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void PaywallBottomBar(kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSubscribe) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void LoadingState() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void EmptyState(java.lang.String therapistName) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ErrorSnackbar(java.lang.String message, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, androidx.compose.ui.Modifier modifier) {
    }
}