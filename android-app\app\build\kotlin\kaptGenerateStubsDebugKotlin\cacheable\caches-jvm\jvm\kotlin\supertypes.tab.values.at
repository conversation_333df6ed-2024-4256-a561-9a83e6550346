/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity android.app.Application3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum6 5com.menteencalma.app.domain.repository.AuthRepository: 9com.menteencalma.app.domain.repository.DatabaseRepository: 9com.menteencalma.app.domain.repository.DatabaseRepository6 5com.menteencalma.app.domain.repository.UserRepository java.lang.Exception kotlin.Annotation kotlin.Annotation3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer, +com.menteencalma.app.domain.model.AuthState, +com.menteencalma.app.domain.model.AuthState, +com.menteencalma.app.domain.model.AuthState, +com.menteencalma.app.domain.model.AuthState, +com.menteencalma.app.domain.model.AuthState kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializerL Kcom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperationL Kcom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperationL Kcom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperationL Kcom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation' &com.menteencalma.app.navigation.Screen' &com.menteencalma.app.navigation.Screen' &com.menteencalma.app.navigation.Screen' &com.menteencalma.app.navigation.Screen' &com.menteencalma.app.navigation.Screen' &com.menteencalma.app.navigation.Screen' &com.menteencalma.app.navigation.Screen' &com.menteencalma.app.navigation.Screen' &com.menteencalma.app.navigation.Screen' &com.menteencalma.app.navigation.Screen' &com.menteencalma.app.navigation.Screen' &com.menteencalma.app.navigation.Screen' &com.menteencalma.app.navigation.Screen androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Annotation kotlin.Annotation