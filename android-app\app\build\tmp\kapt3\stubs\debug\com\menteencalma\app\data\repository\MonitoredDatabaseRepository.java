package com.menteencalma.app.data.repository;

/**
 * Wrapper que añade monitoreo a cualquier implementación de DatabaseRepository
 * Permite cambiar la implementación subyacente sin perder el monitoreo
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0080\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0010\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0001\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J$\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0006\u0010\t\u001a\u00020\nH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u000b\u0010\fJ*\u0010\r\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0011\u0010\u0012J&\u0010\u0013\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00140\u00072\u0006\u0010\u0015\u001a\u00020\u0016H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0017\u0010\u0018JF\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u000f0\u00072\b\u0010\u001a\u001a\u0004\u0018\u00010\u00162\b\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u001eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b \u0010!J<\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020#0\u000f0\u00072\u0006\u0010$\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\b\u0010%\u001a\u0004\u0018\u00010\u0016H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b&\u0010\'J&\u0010(\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010)0\u00072\u0006\u0010$\u001a\u00020\u0016H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b*\u0010\u0018J8\u0010+\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020-0,0\u00072\u0006\u0010.\u001a\u00020/2\u0006\u00100\u001a\u00020/H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b1\u00102J@\u00103\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020-0,0\u00072\u0006\u0010$\u001a\u00020\u00162\u0006\u0010.\u001a\u00020/2\u0006\u00100\u001a\u00020/H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b4\u00105J&\u00106\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\n0\u00072\u0006\u0010$\u001a\u00020\u0016H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b7\u0010\u0018J8\u00108\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0006\u00109\u001a\u00020\u00162\u0012\u0010:\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020-0,H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b;\u0010<J2\u0010=\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0006\u0010$\u001a\u00020\u00162\f\u0010>\u001a\b\u0012\u0004\u0012\u00020\u00160\u000fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b?\u0010@J\u001c\u0010A\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020#0\u000f0B2\u0006\u0010$\u001a\u00020\u0016H\u0016J\u0018\u0010C\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\n0B2\u0006\u0010$\u001a\u00020\u0016H\u0016J,\u0010D\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0006\u0010$\u001a\u00020\u00162\u0006\u0010\u0015\u001a\u00020\u0016H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bE\u0010FJR\u0010G\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\u000f0\u00072\b\u0010H\u001a\u0004\u0018\u00010I2\b\u0010J\u001a\u0004\u0018\u00010\u00162\b\u0010K\u001a\u0004\u0018\u00010\u00162\b\u0010L\u001a\u0004\u0018\u00010\u00162\u0006\u0010\u001d\u001a\u00020\u001eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bM\u0010NJ$\u0010O\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0006\u0010P\u001a\u00020#H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bQ\u0010RJ$\u0010S\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0006\u0010T\u001a\u00020)H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bU\u0010VJ$\u0010W\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0006\u0010\t\u001a\u00020\nH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bX\u0010\fR\u000e\u0010\u0002\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006Y"}, d2 = {"Lcom/menteencalma/app/data/repository/MonitoredDatabaseRepository;", "Lcom/menteencalma/app/domain/repository/DatabaseRepository;", "actualRepository", "monitoringService", "Lcom/menteencalma/app/data/monitoring/DatabaseMonitoringService;", "(Lcom/menteencalma/app/domain/repository/DatabaseRepository;Lcom/menteencalma/app/data/monitoring/DatabaseMonitoringService;)V", "createUser", "Lkotlin/Result;", "", "user", "Lcom/menteencalma/app/domain/model/User;", "createUser-gIAlu-s", "(Lcom/menteencalma/app/domain/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "executeTransaction", "operations", "", "Lcom/menteencalma/app/domain/repository/DatabaseRepository$DatabaseOperation;", "executeTransaction-gIAlu-s", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getArticle", "Lcom/menteencalma/app/domain/model/Article;", "articleId", "", "getArticle-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getArticles", "category", "isPremium", "", "limit", "", "offset", "getArticles-yxL6bBk", "(Ljava/lang/String;Ljava/lang/Boolean;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getChatMessages", "Lcom/menteencalma/app/domain/model/ChatMessage;", "userId", "lastMessageId", "getChatMessages-BWLJW6A", "(Ljava/lang/String;ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSubscription", "Lcom/menteencalma/app/domain/model/Subscription;", "getSubscription-gIAlu-s", "getSubscriptionStats", "", "", "startDate", "", "endDate", "getSubscriptionStats-0E7RQCE", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUsageMetrics", "getUsageMetrics-BWLJW6A", "(Ljava/lang/String;JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUser", "getUser-gIAlu-s", "logEvent", "eventName", "parameters", "logEvent-0E7RQCE", "(Ljava/lang/String;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markMessagesAsRead", "messageIds", "markMessagesAsRead-0E7RQCE", "(Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "observeChatMessages", "Lkotlinx/coroutines/flow/Flow;", "observeUser", "recordArticleRead", "recordArticleRead-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchUsers", "ageRange", "Lkotlin/ranges/IntRange;", "gender", "therapistPreference", "subscriptionStatus", "searchUsers-hUnOzRk", "(Lkotlin/ranges/IntRange;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "sendChatMessage", "message", "sendChatMessage-gIAlu-s", "(Lcom/menteencalma/app/domain/model/ChatMessage;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSubscription", "subscription", "updateSubscription-gIAlu-s", "(Lcom/menteencalma/app/domain/model/Subscription;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUser", "updateUser-gIAlu-s", "app_debug"})
public final class MonitoredDatabaseRepository implements com.menteencalma.app.domain.repository.DatabaseRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.repository.DatabaseRepository actualRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.data.monitoring.DatabaseMonitoringService monitoringService = null;
    
    @javax.inject.Inject()
    public MonitoredDatabaseRepository(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.DatabaseRepository actualRepository, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.data.monitoring.DatabaseMonitoringService monitoringService) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<com.menteencalma.app.domain.model.User> observeUser(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.List<com.menteencalma.app.domain.model.ChatMessage>> observeChatMessages(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
        return null;
    }
}