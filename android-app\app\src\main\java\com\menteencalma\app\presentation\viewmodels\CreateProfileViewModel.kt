package com.menteencalma.app.presentation.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.userProfileChangeRequest
import com.google.firebase.functions.FirebaseFunctions
import com.menteencalma.app.domain.model.User
import com.menteencalma.app.domain.repository.AuthRepository
import com.menteencalma.app.domain.repository.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import javax.inject.Inject

data class CreateProfileUiState(
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val isProfileCreated: Boolean = false,
    val displayName: String = "",
    val age: String = "",
    val gender: String = "",
    val therapistGender: String = ""
)

@HiltViewModel
class CreateProfileViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val userRepository: UserRepository,
    private val functions: FirebaseFunctions
) : ViewModel() {

    private val _uiState = MutableStateFlow(CreateProfileUiState())
    val uiState: StateFlow<CreateProfileUiState> = _uiState.asStateFlow()

    fun updateDisplayName(name: String) {
        _uiState.value = _uiState.value.copy(
            displayName = name,
            errorMessage = null
        )
    }

    fun updateAge(age: String) {
        _uiState.value = _uiState.value.copy(
            age = age,
            errorMessage = null
        )
    }

    fun updateGender(gender: String) {
        _uiState.value = _uiState.value.copy(
            gender = gender,
            errorMessage = null
        )
    }

    fun updateTherapistGender(therapistGender: String) {
        _uiState.value = _uiState.value.copy(
            therapistGender = therapistGender,
            errorMessage = null
        )
    }

    fun createProfile() {
        val currentState = _uiState.value

        // Validaciones
        if (currentState.displayName.isBlank()) {
            _uiState.value = currentState.copy(
                errorMessage = "El nombre es obligatorio"
            )
            return
        }

        if (currentState.displayName.length < 2) {
            _uiState.value = currentState.copy(
                errorMessage = "El nombre debe tener al menos 2 caracteres"
            )
            return
        }

        if (currentState.age.isNotBlank()) {
            val ageInt = currentState.age.toIntOrNull()
            if (ageInt == null || ageInt < 13 || ageInt > 120) {
                _uiState.value = currentState.copy(
                    errorMessage = "Por favor ingresa una edad válida (13-120 años)"
                )
                return
            }
        }

        if (currentState.therapistGender.isBlank()) {
            _uiState.value = currentState.copy(
                errorMessage = "Por favor selecciona tu terapeuta preferido"
            )
            return
        }

        viewModelScope.launch {
            _uiState.value = currentState.copy(
                isLoading = true,
                errorMessage = null
            )

            try {
                val firebaseUser = authRepository.getCurrentUser()
                if (firebaseUser == null) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = "Error: Usuario no autenticado"
                    )
                    return@launch
                }

                // 1. Actualizar displayName en Firebase Auth
                val profileUpdates = userProfileChangeRequest {
                    displayName = currentState.displayName
                }
                firebaseUser.updateProfile(profileUpdates).await()

                // 2. Crear documento en Firestore
                val user = User(
                    id = firebaseUser.uid,
                    email = firebaseUser.email ?: "",
                    displayName = currentState.displayName,
                    age = if (currentState.age.isNotBlank()) currentState.age.toIntOrNull() else null,
                    gender = if (currentState.gender.isNotBlank()) currentState.gender else null,
                    therapistGender = currentState.therapistGender,
                    subscriptionStatus = "free",
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis()
                )

                val createResult = userRepository.createUserProfile(user)
                if (createResult.isSuccess) {
                    // 3. Llamar a Cloud Function para generar saludo personalizado
                    try {
                        callCreateUserProfileFunction(firebaseUser.uid)
                    } catch (e: Exception) {
                        // No fallar si la Cloud Function falla, solo log
                        println("Warning: Cloud Function failed: ${e.message}")
                    }

                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isProfileCreated = true
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = "Error al crear el perfil: ${createResult.exceptionOrNull()?.message}"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Error inesperado: ${e.message}"
                )
            }
        }
    }

    private suspend fun callCreateUserProfileFunction(userId: String) {
        try {
            val data = hashMapOf(
                "userId" to userId
            )

            functions
                .getHttpsCallable("createUserProfile")
                .call(data)
                .await()
        } catch (e: Exception) {
            // Log el error pero no fallar la creación del perfil
            println("Error calling createUserProfile function: ${e.message}")
            throw e
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun resetState() {
        _uiState.value = CreateProfileUiState()
    }

    companion object {
        const val THERAPIST_AURORA = "aurora"
        const val THERAPIST_ALEJANDRO = "alejandro"
        
        const val GENDER_MALE = "male"
        const val GENDER_FEMALE = "female"
        const val GENDER_OTHER = "other"
        const val GENDER_PREFER_NOT_TO_SAY = "prefer_not_to_say"
    }
}
