{"logs": [{"outputFile": "com.menteencalma.app-mergeDebugResources-3:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\8b1e49370ed9ee03b5df4273fd102a11\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "6,7,9,10,11,12,266,267,268,269,270,271,272,365,844,845,846,1676,1678,2015,2024,2037", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "359,419,519,588,660,723,17793,17867,17943,18019,18096,18167,18236,23473,56307,56388,56480,108917,109026,134254,134714,135489", "endLines": "6,7,9,10,11,12,266,267,268,269,270,271,272,365,844,845,846,1677,1679,2023,2036,2040", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "414,473,583,655,718,790,17862,17938,18014,18091,18162,18231,18302,23536,56383,56475,56568,109021,109142,134709,135484,135757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\bc87254a1d5a4bb9d2cd1a5740dba329\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "44,45,46,47,48,49,50,51,415,416,417,418,419,420,421,422,424,425,426,427,428,429,430,431,432,3030,3298", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3004,3094,3174,3264,3354,3434,3515,3595,26899,27004,27185,27310,27417,27597,27720,27836,28106,28294,28399,28580,28705,28880,29028,29091,29153,174589,183520", "endLines": "44,45,46,47,48,49,50,51,415,416,417,418,419,420,421,422,424,425,426,427,428,429,430,431,432,3042,3316", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "3089,3169,3259,3349,3429,3510,3590,3670,26999,27180,27305,27412,27592,27715,27831,27934,28289,28394,28575,28700,28875,29023,29086,29148,29227,174899,183932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d260b924aea8be51d43edb0c005cc39b\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "321", "startColumns": "4", "startOffsets": "21162", "endColumns": "49", "endOffsets": "21207"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\9d8220d97db23c7cdad1f764f40cfcc2\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "328,332", "startColumns": "4,4", "startOffsets": "21522,21699", "endColumns": "53,66", "endOffsets": "21571,21761"}}, {"source": "B:\\Mente en calma\\android-app\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "59,65,61,42,44,53,54,52,43,45,31,33,35,37,49,51,39,41,46,47,30,32,34,36,48,50,38,40,15,17,26,27,25,16,18,4,6,8,10,22,24,12,14,19,20,3,5,7,9,21,23,11,13,57,58,60,66,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3392,3609,3491,2470,2580,3132,3197,3069,2524,2643,1776,1899,2024,2151,2883,3004,2277,2402,2708,2764,1720,1834,1966,2084,2827,2941,2220,2336,867,979,1540,1606,1476,922,1043,162,287,414,543,1287,1410,671,798,1109,1166,105,221,355,475,1230,1346,613,731,3294,3342,3443,3649,3569", "endColumns": "50,39,45,53,62,64,62,62,55,64,57,66,59,68,57,64,58,67,55,62,55,64,57,66,55,62,56,65,54,63,65,63,63,56,65,58,67,60,69,58,65,59,68,56,63,56,65,58,67,56,63,57,66,47,49,47,47,39", "endOffsets": "3438,3644,3532,2519,2638,3192,3255,3127,2575,2703,1829,1961,2079,2215,2936,3064,2331,2465,2759,2822,1771,1894,2019,2146,2878,2999,2272,2397,917,1038,1601,1665,1535,974,1104,216,350,470,608,1341,1471,726,862,1161,1225,157,282,409,538,1282,1405,666,793,3337,3387,3486,3692,3604"}, "to": {"startLines": "24,29,56,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,127,138,143,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1573,1943,3955,5074,5128,5191,5256,5319,5382,5438,5503,5561,5628,5688,5757,5815,5880,5939,6007,6063,6126,6182,6247,6305,6372,6428,6491,6548,6614,6669,6733,6799,6863,6927,6984,7050,7109,7177,7238,7308,7367,7433,7493,7562,7619,7683,7740,7806,7865,7933,7990,8054,8112,8311,9062,9408,9863,9911", "endColumns": "50,39,45,53,62,64,62,62,55,64,57,66,59,68,57,64,58,67,55,62,55,64,57,66,55,62,56,65,54,63,65,63,63,56,65,58,67,60,69,58,65,59,68,56,63,56,65,58,67,56,63,57,66,47,49,47,47,39", "endOffsets": "1619,1978,3996,5123,5186,5251,5314,5377,5433,5498,5556,5623,5683,5752,5810,5875,5934,6002,6058,6121,6177,6242,6300,6367,6423,6486,6543,6609,6664,6728,6794,6858,6922,6979,7045,7104,7172,7233,7303,7362,7428,7488,7557,7614,7678,7735,7801,7860,7928,7985,8049,8107,8174,8354,9107,9451,9906,9946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\98bcb59e84dea2106bcefc907e055731\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "463,464,465,466,467,468,469,470,471,472,475,476,477,478,479,480,481,482,483,484,485,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "31389,31477,31563,31644,31728,31797,31862,31945,32051,32137,32257,32311,32380,32441,32510,32599,32694,32768,32865,32958,33056,33205,33296,33384,33480,33578,33642,33710,33797,33891,33958,34030,34102,34203,34312,34388,34457,34505,34571,34635,34692,34749,34821,34871,34925,34996,35067,35137,35206,35264,35340,35411,35485,35571,35621,35691", "endLines": "463,464,465,466,467,468,469,470,471,474,475,476,477,478,479,480,481,482,483,484,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "31472,31558,31639,31723,31792,31857,31940,32046,32132,32252,32306,32375,32436,32505,32594,32689,32763,32860,32953,33051,33200,33291,33379,33475,33573,33637,33705,33792,33886,33953,34025,34097,34198,34307,34383,34452,34500,34566,34630,34687,34744,34816,34866,34920,34991,35062,35132,35201,35259,35335,35406,35480,35566,35616,35686,35751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\0eb584644ee2de7f40b061feda878225\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "331,2254,3236,3239", "startColumns": "4,4,4,4", "startOffsets": "21646,148083,181430,181545", "endLines": "331,2260,3238,3241", "endColumns": "52,24,24,24", "endOffsets": "21694,148382,181540,181655"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\4d223db05fd24be12ff04f297ed14cfc\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "324,336,359,2946,2951", "startColumns": "4,4,4,4,4", "startOffsets": "21325,21904,23113,172208,172378", "endLines": "324,336,359,2950,2954", "endColumns": "56,64,63,24,24", "endOffsets": "21377,21964,23172,172373,172522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\3d9a7801f6683606bdfbc1f167eb6309\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "357", "startColumns": "4", "startOffsets": "23009", "endColumns": "53", "endOffsets": "23058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\0a2f82cac04b16c6074a060829451b01\\transformed\\play-services-basement-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "366,423", "startColumns": "4,4", "startOffsets": "23541,27939", "endColumns": "67,166", "endOffsets": "23604,28101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\50ee6f3e6ca87ad54db36714ad7f176c\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2271,2287,2293,3329,3345", "startColumns": "4,4,4,4,4", "startOffsets": "148904,149329,149507,184249,184660", "endLines": "2286,2292,2302,3344,3348", "endColumns": "24,24,24,24,24", "endOffsets": "149324,149502,149786,184655,184782"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d60095cfcd5f6da5b72a1052224dd592\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,320,322,323,325,327,360,413,414,439,440,442,458,459,532,533,534,536,542,543,549,557,558,559,1680,1683,1686", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18973,19032,19091,19151,19211,19271,19331,19391,19451,19511,19571,19631,19691,19750,19810,19870,19930,19990,20050,20110,20170,20230,20290,20350,20409,20469,20529,20588,20647,20706,20765,20824,21088,21212,21270,21382,21467,23177,26780,26845,29631,29697,29942,31106,31158,36220,36282,36336,36411,36815,36865,37272,37738,37785,37821,109147,109259,109370", "endLines": "284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,320,322,323,325,327,360,413,414,439,440,442,458,459,532,533,534,536,542,543,549,557,558,559,1682,1685,1689", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "19027,19086,19146,19206,19266,19326,19386,19446,19506,19566,19626,19686,19745,19805,19865,19925,19985,20045,20105,20165,20225,20285,20345,20404,20464,20524,20583,20642,20701,20760,20819,20878,21157,21265,21320,21428,21517,23225,26840,26894,29692,29793,29995,31153,31213,36277,36331,36367,36440,36860,36914,37313,37780,37816,37906,109254,109365,109560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\b269d4c19ab9d1b3baa498ab4d0bb27d\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "335,356", "startColumns": "4,4", "startOffsets": "21862,22949", "endColumns": "41,59", "endOffsets": "21899,23004"}}, {"source": "B:\\Mente en calma\\android-app\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3,22,25", "startColumns": "4,4,4", "startOffsets": "171,1029,1149", "endLines": "20,22,30", "endColumns": "12,72,12", "endOffsets": "1023,1097,1481"}, "to": {"startLines": "826,2008,2009", "startColumns": "4,4,4", "startOffsets": "55540,133845,133917", "endLines": "843,2008,2014", "endColumns": "12,71,12", "endOffsets": "56302,133912,134249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\df5a889645fb5170e23f0cfa48fe0b2f\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "355", "startColumns": "4", "startOffsets": "22906", "endColumns": "42", "endOffsets": "22944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ca611de3937d8b7fad81038213315e33\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "565,566", "startColumns": "4,4", "startOffsets": "38333,38389", "endColumns": "55,54", "endOffsets": "38384,38439"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\812ae415204e3b60a6d9571a5434b24a\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,8,13,14,15,16,17,18,19,20,21,25,26,27,28,30,31,32,33,34,35,40,41,52,53,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,128,129,130,131,132,133,134,135,136,137,139,140,141,142,144,145,146,147,148,149,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,242,243,244,245,246,247,248,249,250,273,274,275,276,277,278,279,280,316,317,318,319,326,333,334,337,354,361,362,363,364,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,548,567,568,569,570,571,572,580,581,585,589,593,598,604,611,615,619,624,628,632,636,640,644,648,654,658,664,668,674,678,683,687,690,694,700,704,710,714,720,723,727,731,735,739,743,744,745,746,749,752,755,758,762,763,764,765,766,769,771,773,775,780,781,785,791,795,796,798,810,811,815,821,825,847,848,852,879,883,884,888,916,1088,1114,1285,1311,1342,1350,1356,1372,1394,1399,1404,1414,1423,1432,1436,1443,1462,1469,1470,1479,1482,1485,1489,1493,1497,1500,1501,1506,1511,1521,1526,1533,1539,1540,1543,1547,1552,1554,1556,1559,1562,1564,1568,1571,1578,1581,1584,1588,1590,1594,1596,1598,1600,1604,1612,1620,1632,1638,1647,1650,1661,1664,1665,1670,1671,1690,1759,1829,1830,1840,1849,1850,1852,1856,1859,1862,1865,1868,1871,1874,1877,1881,1884,1887,1890,1894,1897,1901,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1927,1929,1930,1931,1932,1933,1934,1935,1936,1938,1939,1941,1942,1944,1946,1947,1949,1950,1951,1952,1953,1954,1956,1957,1958,1959,1960,1972,1974,1976,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1992,1993,1994,1995,1996,1997,1998,2000,2004,2041,2042,2043,2044,2045,2046,2050,2051,2052,2053,2055,2057,2059,2061,2063,2064,2065,2066,2068,2070,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2086,2087,2088,2089,2091,2093,2094,2096,2097,2099,2101,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2116,2117,2118,2119,2121,2122,2123,2124,2125,2127,2129,2131,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2150,2225,2228,2231,2234,2248,2261,2303,2306,2335,2362,2371,2435,2798,2808,2846,2874,2994,3018,3024,3043,3064,3188,3247,3253,3257,3263,3317,3349,3415,3435,3490,3502,3528", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,478,795,850,912,976,1046,1107,1182,1258,1335,1624,1709,1791,1867,1983,2060,2138,2244,2350,2429,2758,2815,3675,3749,3824,3889,4001,4061,4122,4194,4267,4334,4402,4461,4520,4579,4638,4697,4751,4805,4858,4912,4966,5020,8359,8433,8512,8585,8659,8730,8802,8874,8947,9004,9112,9185,9259,9333,9456,9528,9601,9671,9742,9802,9951,10020,10089,10159,10233,10309,10373,10450,10526,10603,10668,10737,10814,10889,10958,11026,11103,11169,11230,11327,11392,11461,11560,11631,11690,11748,11805,11864,11928,11999,12071,12143,12215,12287,12354,12422,12490,12549,12612,12676,12766,12857,12917,12983,13050,13116,13186,13250,13303,13370,13431,13498,13611,13669,13732,13797,13862,13937,14010,14082,14126,14173,14219,14268,14329,14390,14451,14513,14577,14641,14705,14770,14833,14893,14954,15020,15079,15139,15201,15272,15332,16031,16117,16204,16294,16381,16469,16551,16634,16724,18307,18359,18417,18462,18528,18592,18649,18706,20883,20940,20988,21037,21433,21766,21813,21969,22874,23230,23294,23356,23416,23679,23753,23823,23901,23955,24025,24110,24158,24204,24265,24328,24394,24458,24529,24592,24657,24721,24782,24843,24895,24968,25042,25111,25186,25260,25334,25475,37219,38444,38522,38612,38700,38796,38886,39468,39557,39804,40085,40337,40622,41015,41492,41714,41936,42212,42439,42669,42899,43129,43359,43586,44005,44231,44656,44886,45314,45533,45816,46024,46155,46382,46808,47033,47460,47681,48106,48226,48502,48803,49127,49418,49732,49869,50000,50105,50347,50514,50718,50926,51197,51309,51421,51526,51643,51857,52003,52143,52229,52577,52665,52911,53329,53578,53660,53758,54415,54515,54767,55191,55446,56573,56662,56899,58923,59165,59267,59520,61676,72357,73873,84568,86096,87853,88479,88899,90160,91425,91681,91917,92464,92958,93563,93761,94341,95709,96084,96202,96740,96897,97093,97366,97622,97792,97933,97997,98362,98729,99405,99669,100007,100360,100454,100640,100946,101208,101333,101460,101699,101910,102029,102222,102399,102854,103035,103157,103416,103529,103716,103818,103925,104054,104329,104837,105333,106210,106504,107074,107223,107955,108127,108211,108547,108639,109565,114796,120167,120229,120807,121391,121482,121595,121824,121984,122136,122307,122473,122642,122809,122972,123215,123385,123558,123729,124003,124202,124407,124737,124821,124917,125013,125111,125211,125313,125415,125517,125619,125721,125821,125917,126029,126158,126281,126412,126543,126641,126755,126849,126989,127123,127219,127331,127431,127547,127643,127755,127855,127995,128131,128295,128425,128583,128733,128874,129018,129153,129265,129415,129543,129671,129807,129939,130069,130199,130311,131209,131355,131499,131637,131703,131793,131869,131973,132063,132165,132273,132381,132481,132561,132653,132751,132861,132913,132991,133097,133189,133293,133403,133525,133688,135762,135842,135942,136032,136142,136232,136473,136567,136673,136765,136865,136977,137091,137207,137323,137417,137531,137643,137745,137865,137987,138069,138173,138293,138419,138517,138611,138699,138811,138927,139049,139161,139336,139452,139538,139630,139742,139866,139933,140059,140127,140255,140399,140527,140596,140691,140806,140919,141018,141127,141238,141349,141450,141555,141655,141785,141876,141999,142093,142205,142291,142395,142491,142579,142697,142801,142905,143031,143119,143227,143327,143417,143527,143611,143713,143797,143851,143915,144021,144107,144217,144301,144560,147176,147294,147409,147489,147850,148387,149791,149869,151213,152574,152962,155805,165858,166196,167867,169224,173376,174127,174389,174904,175283,179561,181842,182071,182222,182437,183937,184787,187813,188557,190688,191028,192339", "endLines": "2,3,4,8,13,14,15,16,17,18,19,20,21,25,26,27,28,30,31,32,33,34,35,40,41,52,53,54,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,128,129,130,131,132,133,134,135,136,137,139,140,141,142,144,145,146,147,148,149,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,242,243,244,245,246,247,248,249,250,273,274,275,276,277,278,279,280,316,317,318,319,326,333,334,337,354,361,362,363,364,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,548,567,568,569,570,571,579,580,584,588,592,597,603,610,614,618,623,627,631,635,639,643,647,653,657,663,667,673,677,682,686,689,693,699,703,709,713,719,722,726,730,734,738,742,743,744,745,748,751,754,757,761,762,763,764,765,768,770,772,774,779,780,784,790,794,795,797,809,810,814,820,824,825,847,851,878,882,883,887,915,1087,1113,1284,1310,1341,1349,1355,1371,1393,1398,1403,1413,1422,1431,1435,1442,1461,1468,1469,1478,1481,1484,1488,1492,1496,1499,1500,1505,1510,1520,1525,1532,1538,1539,1542,1546,1551,1553,1555,1558,1561,1563,1567,1570,1577,1580,1583,1587,1589,1593,1595,1597,1599,1603,1611,1619,1631,1637,1646,1649,1660,1663,1664,1669,1670,1675,1758,1828,1829,1839,1848,1849,1851,1855,1858,1861,1864,1867,1870,1873,1876,1880,1883,1886,1889,1893,1896,1900,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1926,1928,1929,1930,1931,1932,1933,1934,1935,1937,1938,1940,1941,1943,1945,1946,1948,1949,1950,1951,1952,1953,1955,1956,1957,1958,1959,1960,1973,1975,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1991,1992,1993,1994,1995,1996,1997,1999,2003,2007,2041,2042,2043,2044,2045,2049,2050,2051,2052,2054,2056,2058,2060,2062,2063,2064,2065,2067,2069,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2085,2086,2087,2088,2090,2092,2093,2095,2096,2098,2100,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2115,2116,2117,2118,2120,2121,2122,2123,2124,2126,2128,2130,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2224,2227,2230,2233,2247,2253,2270,2305,2334,2361,2370,2434,2797,2801,2835,2873,2891,3017,3023,3029,3063,3187,3207,3252,3256,3262,3297,3328,3414,3434,3489,3501,3527,3534", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,514,845,907,971,1041,1102,1177,1253,1330,1408,1704,1786,1862,1938,2055,2133,2239,2345,2424,2504,2810,2868,3744,3819,3884,3950,4056,4117,4189,4262,4329,4397,4456,4515,4574,4633,4692,4746,4800,4853,4907,4961,5015,5069,8428,8507,8580,8654,8725,8797,8869,8942,8999,9057,9180,9254,9328,9403,9523,9596,9666,9737,9797,9858,10015,10084,10154,10228,10304,10368,10445,10521,10598,10663,10732,10809,10884,10953,11021,11098,11164,11225,11322,11387,11456,11555,11626,11685,11743,11800,11859,11923,11994,12066,12138,12210,12282,12349,12417,12485,12544,12607,12671,12761,12852,12912,12978,13045,13111,13181,13245,13298,13365,13426,13493,13606,13664,13727,13792,13857,13932,14005,14077,14121,14168,14214,14263,14324,14385,14446,14508,14572,14636,14700,14765,14828,14888,14949,15015,15074,15134,15196,15267,15327,15395,16112,16199,16289,16376,16464,16546,16629,16719,16810,18354,18412,18457,18523,18587,18644,18701,18755,20935,20983,21032,21083,21462,21808,21857,22010,22901,23289,23351,23411,23468,23748,23818,23896,23950,24020,24105,24153,24199,24260,24323,24389,24453,24524,24587,24652,24716,24777,24838,24890,24963,25037,25106,25181,25255,25329,25470,25540,37267,38517,38607,38695,38791,38881,39463,39552,39799,40080,40332,40617,41010,41487,41709,41931,42207,42434,42664,42894,43124,43354,43581,44000,44226,44651,44881,45309,45528,45811,46019,46150,46377,46803,47028,47455,47676,48101,48221,48497,48798,49122,49413,49727,49864,49995,50100,50342,50509,50713,50921,51192,51304,51416,51521,51638,51852,51998,52138,52224,52572,52660,52906,53324,53573,53655,53753,54410,54510,54762,55186,55441,55535,56657,56894,58918,59160,59262,59515,61671,72352,73868,84563,86091,87848,88474,88894,90155,91420,91676,91912,92459,92953,93558,93756,94336,95704,96079,96197,96735,96892,97088,97361,97617,97787,97928,97992,98357,98724,99400,99664,100002,100355,100449,100635,100941,101203,101328,101455,101694,101905,102024,102217,102394,102849,103030,103152,103411,103524,103711,103813,103920,104049,104324,104832,105328,106205,106499,107069,107218,107950,108122,108206,108542,108634,108912,114791,120162,120224,120802,121386,121477,121590,121819,121979,122131,122302,122468,122637,122804,122967,123210,123380,123553,123724,123998,124197,124402,124732,124816,124912,125008,125106,125206,125308,125410,125512,125614,125716,125816,125912,126024,126153,126276,126407,126538,126636,126750,126844,126984,127118,127214,127326,127426,127542,127638,127750,127850,127990,128126,128290,128420,128578,128728,128869,129013,129148,129260,129410,129538,129666,129802,129934,130064,130194,130306,130446,131350,131494,131632,131698,131788,131864,131968,132058,132160,132268,132376,132476,132556,132648,132746,132856,132908,132986,133092,133184,133288,133398,133520,133683,133840,135837,135937,136027,136137,136227,136468,136562,136668,136760,136860,136972,137086,137202,137318,137412,137526,137638,137740,137860,137982,138064,138168,138288,138414,138512,138606,138694,138806,138922,139044,139156,139331,139447,139533,139625,139737,139861,139928,140054,140122,140250,140394,140522,140591,140686,140801,140914,141013,141122,141233,141344,141445,141550,141650,141780,141871,141994,142088,142200,142286,142390,142486,142574,142692,142796,142900,143026,143114,143222,143322,143412,143522,143606,143708,143792,143846,143910,144016,144102,144212,144296,144416,147171,147289,147404,147484,147845,148078,148899,149864,151208,152569,152957,155800,165853,165988,167561,169219,169791,174122,174384,174584,175278,179556,180162,182066,182217,182432,183515,184244,187808,188552,190683,191023,192334,192537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\b7c5e14f30f524dc22cc8925fd453a32\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "5,22,23,42,43,125,126,235,236,237,238,239,240,241,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,281,282,283,329,330,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,367,403,404,405,406,407,408,409,555,1961,1962,1966,1967,1971,2148,2149,2802,2836,2892,2925,2955,2988", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,1413,1485,2873,2938,8179,8248,15543,15613,15681,15753,15823,15884,15958,16815,16876,16937,16999,17063,17125,17186,17254,17354,17414,17480,17553,17622,17679,17731,18760,18832,18908,21576,21611,22015,22070,22133,22188,22246,22304,22365,22428,22485,22536,22586,22647,22704,22770,22804,22839,23609,26053,26120,26192,26261,26330,26404,26476,37603,130451,130568,130769,130879,131080,144421,144493,165993,167566,169796,171527,172527,173209", "endLines": "5,22,23,42,43,125,126,235,236,237,238,239,240,241,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,281,282,283,329,330,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,367,403,404,405,406,407,408,409,555,1961,1965,1966,1970,1971,2148,2149,2807,2845,2924,2945,2987,2993", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "354,1480,1568,2933,2999,8243,8306,15608,15676,15748,15818,15879,15953,16026,16871,16932,16994,17058,17120,17181,17249,17349,17409,17475,17548,17617,17674,17726,17788,18827,18903,18968,21606,21641,22065,22128,22183,22241,22299,22360,22423,22480,22531,22581,22642,22699,22765,22799,22834,22869,23674,26115,26187,26256,26325,26399,26471,26559,37669,130563,130764,130874,131075,131204,144488,144555,166191,167862,171522,172203,173204,173371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ec6bd2faa3e0a998fc0a19c7dd0c22ed\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "36,37,38,39,233,234,436,445,446,447", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2509,2567,2633,2696,15400,15471,29438,30086,30153,30232", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2562,2628,2691,2753,15466,15538,29501,30148,30227,30296"}}, {"source": "B:\\Mente en calma\\android-app\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "47,48,3,4,22,21,72,70,16,15,43,35,38,29,52,33,68,62,49,61,63,64,67,28,75,76,45,46,9,7,11,10,12,8,71,34,44,25,24,19,18,69,51,77,36,39,37,40,30,57,58,55,56,50", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2240,2294,77,129,985,932,3468,3385,617,561,1976,1583,1769,1328,2535,1483,3300,3059,2345,3009,3110,3156,3252,1273,3547,3615,2130,2178,331,225,433,382,482,267,3429,1529,2040,1150,1098,804,738,3340,2469,3665,1655,1839,1713,1894,1390,2801,2869,2623,2685,2404", "endColumns": "53,50,51,66,107,52,39,43,115,55,63,71,69,61,62,45,39,50,58,49,45,70,47,54,67,49,47,61,50,41,48,50,46,63,38,53,89,91,51,122,65,44,65,62,57,54,55,52,63,67,110,61,115,64", "endOffsets": "2289,2340,124,191,1088,980,3503,3424,728,612,2035,1650,1834,1385,2593,1524,3335,3105,2399,3054,3151,3222,3295,1323,3610,3660,2173,2235,377,262,477,428,524,326,3463,1578,2125,1237,1145,922,799,3380,2530,3723,1708,1889,1764,1942,1449,2864,2975,2680,2796,2464"}, "to": {"startLines": "395,396,398,399,400,401,402,410,411,412,433,434,435,437,438,443,444,449,450,451,452,453,460,461,462,523,524,525,526,527,528,529,530,531,535,537,538,539,540,544,545,546,547,550,551,552,553,554,556,560,561,562,563,564", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25545,25599,25733,25785,25852,25960,26013,26564,26608,26724,29232,29296,29368,29506,29568,30000,30046,30383,30434,30493,30543,30589,31218,31266,31321,35756,35806,35854,35916,35967,36009,36058,36109,36156,36372,36445,36499,36589,36681,36919,37042,37108,37153,37318,37381,37439,37494,37550,37674,37911,37979,38090,38152,38268", "endColumns": "53,50,51,66,107,52,39,43,115,55,63,71,69,61,62,45,39,50,58,49,45,70,47,54,67,49,47,61,50,41,48,50,46,63,38,53,89,91,51,122,65,44,65,62,57,54,55,52,63,67,110,61,115,64", "endOffsets": "25594,25645,25780,25847,25955,26008,26048,26603,26719,26775,29291,29363,29433,29563,29626,30041,30081,30429,30488,30538,30584,30655,31261,31316,31384,35801,35849,35911,35962,36004,36053,36104,36151,36215,36406,36494,36584,36676,36728,37037,37103,37148,37214,37376,37434,37489,37545,37598,37733,37974,38085,38147,38263,38328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\64f4d6b2a08f902b0fb0339d225cf748\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "397", "startColumns": "4", "startOffsets": "25650", "endColumns": "82", "endOffsets": "25728"}}, {"source": "B:\\Mente en calma\\android-app\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,385,494,614,727", "endColumns": "143,81,103,108,119,112,81", "endOffsets": "194,276,380,489,609,722,804"}, "to": {"startLines": "441,448,454,455,456,457,541", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "29798,30301,30660,30764,30873,30993,36733", "endColumns": "143,81,103,108,119,112,81", "endOffsets": "29937,30378,30759,30868,30988,31101,36810"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\316a63a31dcb075550a6a1c29016c7d4\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3208,3221,3227,3233,3242", "startColumns": "4,4,4,4,4", "startOffsets": "180167,180806,181050,181297,181660", "endLines": "3220,3226,3232,3235,3246", "endColumns": "24,24,24,24,24", "endOffsets": "180801,181045,181292,181425,181837"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\47d882b02ff06b4aab905988600b4821\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "358", "startColumns": "4", "startOffsets": "23063", "endColumns": "49", "endOffsets": "23108"}}]}]}