{"logs": [{"outputFile": "com.menteencalma.app-mergeDebugResources-3:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\8b1e49370ed9ee03b5df4273fd102a11\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "3,4,5,6,7,8,51,52,53,54,55,56,57,129,303,304,305,306,308,331,340,353", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "210,270,329,398,470,533,3557,3631,3707,3783,3860,3931,4000,8128,21180,21261,21353,21446,21555,22852,23312,24087", "endLines": "3,4,5,6,7,8,51,52,53,54,55,56,57,129,303,304,305,307,309,339,352,356", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "265,324,393,465,528,600,3626,3702,3778,3855,3926,3995,4066,8191,21256,21348,21441,21550,21671,23307,24082,24355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\bc87254a1d5a4bb9d2cd1a5740dba329\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "17,18,19,20,21,22,23,24,152,153,154,155,156,157,158,159,161,162,163,164,165,166,167,168,169,516,568", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1145,1235,1315,1405,1495,1575,1656,1736,9688,9793,9974,10099,10206,10386,10509,10625,10895,11083,11188,11369,11494,11669,11817,11880,11942,29774,31764", "endLines": "17,18,19,20,21,22,23,24,152,153,154,155,156,157,158,159,161,162,163,164,165,166,167,168,169,528,586", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "1230,1310,1400,1490,1570,1651,1731,1811,9788,9969,10094,10201,10381,10504,10620,10723,11078,11183,11364,11489,11664,11812,11875,11937,12016,30084,32176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d260b924aea8be51d43edb0c005cc39b\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "94", "startColumns": "4", "startOffsets": "6268", "endColumns": "49", "endOffsets": "6313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\9d8220d97db23c7cdad1f764f40cfcc2\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "100,104", "startColumns": "4,4", "startOffsets": "6594,6771", "endColumns": "53,66", "endOffsets": "6643,6833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\98bcb59e84dea2106bcefc907e055731\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "200,201,202,203,204,205,206,207,208,209,212,213,214,215,216,217,218,219,220,221,222,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14178,14266,14352,14433,14517,14586,14651,14734,14840,14926,15046,15100,15169,15230,15299,15388,15483,15557,15654,15747,15845,15994,16085,16173,16269,16367,16431,16499,16586,16680,16747,16819,16891,16992,17101,17177,17246,17294,17360,17424,17481,17538,17610,17660,17714,17785,17856,17926,17995,18053,18129,18200,18274,18360,18410,18480", "endLines": "200,201,202,203,204,205,206,207,208,211,212,213,214,215,216,217,218,219,220,221,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "14261,14347,14428,14512,14581,14646,14729,14835,14921,15041,15095,15164,15225,15294,15383,15478,15552,15649,15742,15840,15989,16080,16168,16264,16362,16426,16494,16581,16675,16742,16814,16886,16987,17096,17172,17241,17289,17355,17419,17476,17533,17605,17655,17709,17780,17851,17921,17990,18048,18124,18195,18269,18355,18405,18475,18540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\0eb584644ee2de7f40b061feda878225\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "103,359,557,560", "startColumns": "4,4,4,4", "startOffsets": "6718,24499,31352,31467", "endLines": "103,365,559,562", "endColumns": "52,24,24,24", "endOffsets": "6766,24798,31462,31577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\4d223db05fd24be12ff04f297ed14cfc\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "97,106,127,468,473", "startColumns": "4,4,4,4,4", "startOffsets": "6431,6880,8011,28606,28776", "endLines": "97,106,127,472,476", "endColumns": "56,64,63,24,24", "endOffsets": "6483,6940,8070,28771,28920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\3d9a7801f6683606bdfbc1f167eb6309\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "125", "startColumns": "4", "startOffsets": "7907", "endColumns": "53", "endOffsets": "7956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\0a2f82cac04b16c6074a060829451b01\\transformed\\play-services-basement-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "130,160", "startColumns": "4,4", "startOffsets": "8196,10728", "endColumns": "67,166", "endOffsets": "8259,10890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\50ee6f3e6ca87ad54db36714ad7f176c\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "366,382,388,587,603", "startColumns": "4,4,4,4,4", "startOffsets": "24803,25228,25406,32181,32592", "endLines": "381,387,397,602,606", "endColumns": "24,24,24,24,24", "endOffsets": "25223,25401,25685,32587,32714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d60095cfcd5f6da5b72a1052224dd592\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,95,96,98,99,128,150,151,176,177,179,195,196,269,270,271,273,279,280,285,293,294,295,310,313,316", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4284,4343,4402,4462,4522,4582,4642,4702,4762,4822,4882,4942,5002,5061,5121,5181,5241,5301,5361,5421,5481,5541,5601,5661,5720,5780,5840,5899,5958,6017,6076,6135,6194,6318,6376,6488,6539,8075,9569,9634,12420,12486,12731,13895,13947,19009,19071,19125,19200,19604,19654,20008,20474,20521,20557,21676,21788,21899", "endLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,95,96,98,99,128,150,151,176,177,179,195,196,269,270,271,273,279,280,285,293,294,295,312,315,319", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "4338,4397,4457,4517,4577,4637,4697,4757,4817,4877,4937,4997,5056,5116,5176,5236,5296,5356,5416,5476,5536,5596,5656,5715,5775,5835,5894,5953,6012,6071,6130,6189,6263,6371,6426,6534,6589,8123,9629,9683,12481,12582,12784,13942,14002,19066,19120,19156,19229,19649,19703,20049,20516,20552,20642,21783,21894,22089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\b269d4c19ab9d1b3baa498ab4d0bb27d\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "105,124", "startColumns": "4,4", "startOffsets": "6838,7847", "endColumns": "41,59", "endOffsets": "6875,7902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\df5a889645fb5170e23f0cfa48fe0b2f\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "123", "startColumns": "4", "startOffsets": "7804", "endColumns": "42", "endOffsets": "7842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ca611de3937d8b7fad81038213315e33\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "301,302", "startColumns": "4,4", "startOffsets": "21069,21125", "endColumns": "55,54", "endOffsets": "21120,21175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\b7c5e14f30f524dc22cc8925fd453a32\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,9,10,15,16,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,58,59,60,101,102,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,131,140,141,142,143,144,145,146,291,320,321,325,326,330,357,358,398,404,414,447,477,510", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,605,677,1014,1079,1816,1885,2091,2161,2229,2301,2371,2432,2506,2579,2640,2701,2763,2827,2889,2950,3018,3118,3178,3244,3317,3386,3443,3495,4071,4143,4219,6648,6683,6945,7000,7063,7118,7176,7234,7295,7358,7415,7466,7516,7577,7634,7700,7734,7769,8264,8842,8909,8981,9050,9119,9193,9265,20339,22094,22211,22412,22522,22723,24360,24432,25690,25893,26194,27925,28925,29607", "endLines": "2,9,10,15,16,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,58,59,60,101,102,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,131,140,141,142,143,144,145,146,291,320,324,325,329,330,357,358,403,413,446,467,509,515", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,672,760,1074,1140,1880,1943,2156,2224,2296,2366,2427,2501,2574,2635,2696,2758,2822,2884,2945,3013,3113,3173,3239,3312,3381,3438,3490,3552,4138,4214,4279,6678,6713,6995,7058,7113,7171,7229,7290,7353,7410,7461,7511,7572,7629,7695,7729,7764,7799,8329,8904,8976,9045,9114,9188,9260,9348,20405,22206,22407,22517,22718,22847,24427,24494,25888,26189,27920,28601,29602,29769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ec6bd2faa3e0a998fc0a19c7dd0c22ed\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "11,12,13,14,27,28,173,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "765,823,889,952,1948,2019,12227,12875,12942,13021", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "818,884,947,1009,2014,2086,12290,12937,13016,13085"}}, {"source": "B:\\Mente en calma\\android-app\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "47,48,3,4,22,21,72,70,16,15,43,35,38,29,52,33,68,62,49,61,63,64,67,28,75,76,45,46,9,7,11,10,12,8,71,34,44,25,24,19,18,69,51,77,36,39,37,40,30,57,58,55,56,50", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2240,2294,77,129,985,932,3468,3385,617,561,1976,1583,1769,1328,2535,1483,3300,3059,2345,3009,3110,3156,3252,1273,3547,3615,2130,2178,331,225,433,382,482,267,3429,1529,2040,1150,1098,804,738,3340,2469,3665,1655,1839,1713,1894,1390,2801,2869,2623,2685,2404", "endColumns": "53,50,51,66,107,52,39,43,115,55,63,71,69,61,62,45,39,50,58,49,45,70,47,54,67,49,47,61,50,41,48,50,46,63,38,53,89,91,51,122,65,44,65,62,57,54,55,52,63,67,110,61,115,64", "endOffsets": "2289,2340,124,191,1088,980,3503,3424,728,612,2035,1650,1834,1385,2593,1524,3335,3105,2399,3054,3151,3222,3295,1323,3610,3660,2173,2235,377,262,477,428,524,326,3463,1578,2125,1237,1145,922,799,3380,2530,3723,1708,1889,1764,1942,1449,2864,2975,2680,2796,2464"}, "to": {"startLines": "132,133,135,136,137,138,139,147,148,149,170,171,172,174,175,180,181,186,187,188,189,190,197,198,199,260,261,262,263,264,265,266,267,268,272,274,275,276,277,281,282,283,284,286,287,288,289,290,292,296,297,298,299,300", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8334,8388,8522,8574,8641,8749,8802,9353,9397,9513,12021,12085,12157,12295,12357,12789,12835,13172,13223,13282,13332,13378,14007,14055,14110,18545,18595,18643,18705,18756,18798,18847,18898,18945,19161,19234,19288,19378,19470,19708,19831,19897,19942,20054,20117,20175,20230,20286,20410,20647,20715,20826,20888,21004", "endColumns": "53,50,51,66,107,52,39,43,115,55,63,71,69,61,62,45,39,50,58,49,45,70,47,54,67,49,47,61,50,41,48,50,46,63,38,53,89,91,51,122,65,44,65,62,57,54,55,52,63,67,110,61,115,64", "endOffsets": "8383,8434,8569,8636,8744,8797,8837,9392,9508,9564,12080,12152,12222,12352,12415,12830,12870,13218,13277,13327,13373,13444,14050,14105,14173,18590,18638,18700,18751,18793,18842,18893,18940,19004,19195,19283,19373,19465,19517,19826,19892,19937,20003,20112,20170,20225,20281,20334,20469,20710,20821,20883,20999,21064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\64f4d6b2a08f902b0fb0339d225cf748\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "134", "startColumns": "4", "startOffsets": "8439", "endColumns": "82", "endOffsets": "8517"}}, {"source": "B:\\Mente en calma\\android-app\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,385,494,614,727", "endColumns": "143,81,103,108,119,112,81", "endOffsets": "194,276,380,489,609,722,804"}, "to": {"startLines": "178,185,191,192,193,194,278", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12587,13090,13449,13553,13662,13782,19522", "endColumns": "143,81,103,108,119,112,81", "endOffsets": "12726,13167,13548,13657,13777,13890,19599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\316a63a31dcb075550a6a1c29016c7d4\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "529,542,548,554,563", "startColumns": "4,4,4,4,4", "startOffsets": "30089,30728,30972,31219,31582", "endLines": "541,547,553,556,567", "endColumns": "24,24,24,24,24", "endOffsets": "30723,30967,31214,31347,31759"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\47d882b02ff06b4aab905988600b4821\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "126", "startColumns": "4", "startOffsets": "7961", "endColumns": "49", "endOffsets": "8006"}}]}]}