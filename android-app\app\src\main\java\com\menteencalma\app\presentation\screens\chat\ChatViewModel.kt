package com.menteencalma.app.presentation.screens.chat

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.menteencalma.app.data.service.CloudFunctionsService
import com.menteencalma.app.domain.model.ChatMessage
import com.menteencalma.app.domain.model.User
import com.menteencalma.app.domain.repository.DatabaseRepository
import com.menteencalma.app.domain.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.UUID
import javax.inject.Inject

data class ChatUiState(
    val messages: List<ChatMessage> = emptyList(),
    val currentUser: User? = null,
    val isLoading: Boolean = false,
    val isSendingMessage: Boolean = false,
    val hasReachedLimit: Boolean = false,
    val errorMessage: String? = null,
    val therapistName: String = "",
    val therapistAvatar: String = ""
)

@HiltViewModel
class ChatViewModel @Inject constructor(
    private val cloudFunctionsService: CloudFunctionsService,
    private val databaseRepository: DatabaseRepository,
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ChatUiState())
    val uiState: StateFlow<ChatUiState> = _uiState.asStateFlow()

    init {
        loadCurrentUser()
        observeMessages()
    }

    private fun loadCurrentUser() {
        viewModelScope.launch {
            try {
                val firebaseUser = authRepository.getCurrentUser()
                if (firebaseUser != null) {
                    val userResult = databaseRepository.getUser(firebaseUser.uid)
                    if (userResult.isSuccess) {
                        val user = userResult.getOrNull()
                        _uiState.value = _uiState.value.copy(
                            currentUser = user,
                            therapistName = getTherapistName(user?.therapistGender),
                            therapistAvatar = getTherapistAvatar(user?.therapistGender)
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error loading user: ${e.message}"
                )
            }
        }
    }

    private fun observeMessages() {
        viewModelScope.launch {
            try {
                val firebaseUser = authRepository.getCurrentUser()
                if (firebaseUser != null) {
                    databaseRepository.observeChatMessages(firebaseUser.uid).collect { messages ->
                        _uiState.value = _uiState.value.copy(
                            messages = messages.sortedBy { it.timestamp },
                            isLoading = false
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error loading messages: ${e.message}",
                    isLoading = false
                )
            }
        }
    }

    fun sendMessage(text: String) {
        if (text.isBlank() || _uiState.value.isSendingMessage) return

        val currentUser = _uiState.value.currentUser ?: return
        val userId = currentUser.id
        val therapistId = currentUser.therapistGender ?: "aurora"

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSendingMessage = true, errorMessage = null)

            try {
                // 1. Crear y añadir mensaje del usuario inmediatamente para UI instantánea
                val userMessage = ChatMessage(
                    id = UUID.randomUUID().toString(),
                    userId = userId,
                    content = text,
                    type = ChatMessage.MessageType.USER,
                    timestamp = System.currentTimeMillis()
                )

                // Añadir a la lista local inmediatamente
                val currentMessages = _uiState.value.messages.toMutableList()
                currentMessages.add(userMessage)
                _uiState.value = _uiState.value.copy(messages = currentMessages)

                // 2. Guardar mensaje del usuario en Firestore
                val saveUserMessageResult = databaseRepository.sendChatMessage(userMessage)
                if (saveUserMessageResult.isFailure) {
                    throw Exception("Failed to save user message: ${saveUserMessageResult.exceptionOrNull()?.message}")
                }

                // 3. Llamar a Cloud Function para respuesta de IA
                val conversationHistory = currentMessages.takeLast(10)
                val chatbotResult = cloudFunctionsService.sendChatMessage(
                    userId = userId,
                    message = text,
                    therapistId = therapistId,
                    conversationHistory = conversationHistory
                )

                if (chatbotResult.isSuccess) {
                    val response = chatbotResult.getOrNull()!!

                    // 4. Crear mensaje de respuesta de IA
                    val aiMessage = ChatMessage(
                        id = UUID.randomUUID().toString(),
                        userId = userId,
                        content = response.message,
                        type = ChatMessage.MessageType.THERAPIST_AI,
                        timestamp = System.currentTimeMillis(),
                        metadata = ChatMessage.MessageMetadata(
                            therapistId = response.therapistId,
                            sentiment = response.sentiment,
                            confidence = response.confidence,
                            topics = response.topics,
                            sessionId = response.sessionId
                        )
                    )

                    // 5. Guardar respuesta de IA en Firestore
                    val saveAiMessageResult = databaseRepository.sendChatMessage(aiMessage)
                    if (saveAiMessageResult.isFailure) {
                        throw Exception("Failed to save AI message: ${saveAiMessageResult.exceptionOrNull()?.message}")
                    }

                    _uiState.value = _uiState.value.copy(isSendingMessage = false)
                } else {
                    val exception = chatbotResult.exceptionOrNull()
                    if (exception is com.menteencalma.app.data.service.CloudFunctionException) {
                        when (exception.code) {
                            CloudFunctionsService.USAGE_LIMIT_REACHED -> {
                                _uiState.value = _uiState.value.copy(
                                    hasReachedLimit = true,
                                    isSendingMessage = false
                                )
                            }
                            else -> {
                                _uiState.value = _uiState.value.copy(
                                    errorMessage = "Error: ${exception.message}",
                                    isSendingMessage = false
                                )
                            }
                        }
                    } else {
                        _uiState.value = _uiState.value.copy(
                            errorMessage = "Error sending message: ${exception?.message}",
                            isSendingMessage = false
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Unexpected error: ${e.message}",
                    isSendingMessage = false
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun resetLimitReached() {
        _uiState.value = _uiState.value.copy(hasReachedLimit = false)
    }

    private fun getTherapistName(therapistGender: String?): String {
        return when (therapistGender) {
            "aurora" -> "Psicóloga Aurora"
            "alejandro" -> "Psicólogo Alejandro"
            else -> "Tu Terapeuta IA"
        }
    }

    private fun getTherapistAvatar(therapistGender: String?): String {
        return when (therapistGender) {
            "aurora" -> "👩‍⚕️" // En una app real, sería una URL de imagen
            "alejandro" -> "👨‍⚕️"
            else -> "🤖"
        }
    }

    /**
     * Marca mensajes como leídos
     */
    fun markMessagesAsRead() {
        viewModelScope.launch {
            try {
                val currentUser = _uiState.value.currentUser ?: return@launch
                val unreadMessages = _uiState.value.messages
                    .filter { !it.isRead && it.type == ChatMessage.MessageType.THERAPIST_AI }
                    .map { it.id }

                if (unreadMessages.isNotEmpty()) {
                    databaseRepository.markMessagesAsRead(currentUser.id, unreadMessages)
                }
            } catch (e: Exception) {
                // Log error but don't show to user
                println("Error marking messages as read: ${e.message}")
            }
        }
    }

    /**
     * Obtiene estadísticas de la conversación
     */
    fun getConversationStats(): ConversationStats {
        val messages = _uiState.value.messages
        return ConversationStats(
            totalMessages = messages.size,
            userMessages = messages.count { it.type == ChatMessage.MessageType.USER },
            aiMessages = messages.count { it.type == ChatMessage.MessageType.THERAPIST_AI },
            averageResponseTime = calculateAverageResponseTime(messages),
            lastMessageTime = messages.maxOfOrNull { it.timestamp } ?: 0L
        )
    }

    private fun calculateAverageResponseTime(messages: List<ChatMessage>): Long {
        val pairs = messages.zipWithNext { user, ai ->
            if (user.type == ChatMessage.MessageType.USER &&
                ai.type == ChatMessage.MessageType.THERAPIST_AI) {
                ai.timestamp - user.timestamp
            } else null
        }.filterNotNull()

        return if (pairs.isNotEmpty()) pairs.average().toLong() else 0L
    }
}

data class ConversationStats(
    val totalMessages: Int,
    val userMessages: Int,
    val aiMessages: Int,
    val averageResponseTime: Long, // en millisegundos
    val lastMessageTime: Long
)
