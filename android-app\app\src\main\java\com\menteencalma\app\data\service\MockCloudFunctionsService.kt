package com.menteencalma.app.data.service

import kotlinx.coroutines.delay
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementación mock de CloudFunctionsService para desarrollo y testing
 * Simula las respuestas de las Cloud Functions reales
 */
@Singleton
class MockCloudFunctionsService @Inject constructor() {

    companion object {
        // Simulación de límites de uso
        private var chatMessagesCount = 0
        private var recommendationsCount = 0
        private var articlesCount = 0
        
        // Límites para usuarios gratuitos
        private const val FREE_CHAT_LIMIT = 10
        private const val FREE_RECOMMENDATIONS_LIMIT = 3
        private const val FREE_ARTICLES_LIMIT = 2
    }

    /**
     * Simula la Cloud Function aiChatbotSupport
     */
    suspend fun sendChatMessage(
        userId: String,
        message: String,
        therapistId: String,
        conversationHistory: List<Map<String, Any>> = emptyList()
    ): Result<ChatbotResponse> {
        return try {
            // Simular delay de red
            delay(1500)
            
            // Simular límite de uso
            chatMessagesCount++
            if (chatMessagesCount > FREE_CHAT_LIMIT) {
                throw CloudFunctionException(
                    code = CloudFunctionsService.USAGE_LIMIT_REACHED,
                    message = "Has alcanzado el límite de mensajes gratuitos",
                    originalException = Exception("Usage limit reached")
                )
            }
            
            // Generar respuesta simulada basada en el terapeuta
            val response = generateMockChatResponse(message, therapistId)
            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Simula la Cloud Function personalizedRecommendation
     */
    suspend fun getPersonalizedRecommendation(
        userId: String,
        currentMood: String? = null,
        preferredCategory: String? = null
    ): Result<RecommendationResponse> {
        return try {
            // Simular delay de red
            delay(2000)
            
            // Simular límite de uso
            recommendationsCount++
            if (recommendationsCount > FREE_RECOMMENDATIONS_LIMIT) {
                throw CloudFunctionException(
                    code = CloudFunctionsService.USAGE_LIMIT_REACHED,
                    message = "Has alcanzado el límite de recomendaciones gratuitas",
                    originalException = Exception("Usage limit reached")
                )
            }
            
            // Generar recomendación simulada
            val response = generateMockRecommendation(currentMood, preferredCategory)
            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Simula la Cloud Function generateArticle
     */
    suspend fun generateArticle(
        userId: String,
        topic: String,
        difficulty: String = "beginner",
        length: String = "medium"
    ): Result<ArticleResponse> {
        return try {
            // Simular delay de red
            delay(3000)
            
            // Simular límite de uso
            articlesCount++
            if (articlesCount > FREE_ARTICLES_LIMIT) {
                throw CloudFunctionException(
                    code = CloudFunctionsService.ARTICLE_LIMIT_REACHED,
                    message = "Has alcanzado el límite de artículos gratuitos",
                    originalException = Exception("Article limit reached")
                )
            }
            
            // Generar artículo simulado
            val response = generateMockArticle(topic, difficulty, length)
            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Simula la Cloud Function createUserProfile
     */
    suspend fun createUserProfile(userId: String): Result<UserProfileResponse> {
        return try {
            delay(1000)
            
            val response = UserProfileResponse(
                personalizedGreeting = "¡Bienvenido a Mente en Calma! Estamos aquí para acompañarte en tu bienestar mental.",
                success = true,
                message = "Perfil creado exitosamente"
            )
            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun generateMockChatResponse(message: String, therapistId: String): ChatbotResponse {
        val therapistName = if (therapistId == "aurora") "Aurora" else "Alejandro"
        
        val responses = when {
            message.contains("ansiedad", ignoreCase = true) -> listOf(
                "Entiendo que sientes ansiedad. Es completamente normal. ¿Te gustaría que practiquemos una técnica de respiración juntos?",
                "La ansiedad puede ser abrumadora, pero hay formas efectivas de manejarla. ¿Qué situaciones específicas te generan más ansiedad?",
                "Reconocer la ansiedad es el primer paso. Te felicito por buscar ayuda. ¿Has notado algún patrón en cuándo aparece?"
            )
            message.contains("estrés", ignoreCase = true) -> listOf(
                "El estrés es una respuesta natural, pero cuando es constante puede afectarnos. ¿Qué aspectos de tu vida te generan más estrés?",
                "Hay técnicas muy efectivas para manejar el estrés. ¿Te interesaría aprender sobre mindfulness?",
                "Es importante identificar las fuentes de estrés para poder abordarlas. ¿Puedes contarme más sobre tu situación?"
            )
            message.contains("triste", ignoreCase = true) || message.contains("deprimido", ignoreCase = true) -> listOf(
                "Lamento que te sientas así. Tus sentimientos son válidos y es valiente que busques apoyo. ¿Hace cuánto te sientes de esta manera?",
                "La tristeza es parte de la experiencia humana, pero cuando persiste es importante abordarla. ¿Hay algo específico que la desencadene?",
                "Gracias por confiar en mí. Estoy aquí para escucharte y apoyarte. ¿Te gustaría hablar sobre lo que está pasando?"
            )
            message.contains("dormir", ignoreCase = true) || message.contains("sueño", ignoreCase = true) -> listOf(
                "Los problemas de sueño pueden afectar mucho nuestro bienestar. ¿Tienes dificultades para conciliar el sueño o para mantenerlo?",
                "Un buen descanso es fundamental para la salud mental. ¿Cuál es tu rutina antes de dormir?",
                "Te puedo ayudar con técnicas de relajación para mejorar tu sueño. ¿Te interesa probar algunas?"
            )
            else -> listOf(
                "Gracias por compartir eso conmigo. ¿Puedes contarme un poco más sobre cómo te sientes?",
                "Estoy aquí para escucharte y apoyarte. ¿Hay algo específico en lo que te gustaría trabajar hoy?",
                "Es importante que sepas que no estás solo en esto. ¿Qué te gustaría explorar en nuestra conversación?",
                "Me alegra que hayas decidido hablar sobre esto. ¿Cómo ha sido tu día hasta ahora?"
            )
        }
        
        return ChatbotResponse(
            message = responses.random(),
            therapistId = therapistId,
            sentiment = "supportive",
            confidence = 0.85f,
            topics = extractTopics(message),
            sessionId = "session_${System.currentTimeMillis()}",
            metadata = mapOf(
                "therapist_name" to therapistName,
                "response_type" to "empathetic",
                "generated_at" to System.currentTimeMillis()
            )
        )
    }

    private fun generateMockRecommendation(currentMood: String?, preferredCategory: String?): RecommendationResponse {
        val isPersonalized = currentMood != null || preferredCategory != null
        
        val recommendations = if (isPersonalized) {
            listOf(
                RecommendationResponse(
                    title = "Respiración 4-7-8 Personalizada",
                    content = "Basándome en tu estado actual, te recomiendo esta técnica específica de respiración. Inhala por 4 segundos, mantén por 7, exhala por 8. Esta técnica es especialmente efectiva para tu situación actual y te ayudará a encontrar calma inmediata.",
                    isPersonalized = true,
                    category = "mindfulness",
                    difficulty = "beginner",
                    estimatedDuration = 5,
                    tags = listOf("respiración", "ansiedad", "relajación"),
                    therapistId = "aurora"
                ),
                RecommendationResponse(
                    title = "Meditación Guiada Adaptada",
                    content = "He preparado esta meditación especialmente para ti. Encuentra un lugar cómodo y dedica 10 minutos a conectar contigo mismo. Esta práctica está diseñada considerando tus necesidades específicas y te ayudará a desarrollar mayor autoconciencia.",
                    isPersonalized = true,
                    category = "meditation",
                    difficulty = "intermediate",
                    estimatedDuration = 10,
                    tags = listOf("meditación", "autoconciencia", "bienestar"),
                    therapistId = "alejandro"
                )
            )
        } else {
            listOf(
                RecommendationResponse(
                    title = "Técnica de Grounding 5-4-3-2-1",
                    content = "Esta es una técnica universal muy efectiva. Identifica: 5 cosas que puedes ver, 4 que puedes tocar, 3 que puedes escuchar, 2 que puedes oler, y 1 que puedes saborear. Te ayudará a conectar con el presente.",
                    isPersonalized = false,
                    category = "grounding",
                    difficulty = "beginner",
                    estimatedDuration = 3,
                    tags = listOf("grounding", "presente", "ansiedad"),
                    therapistId = "aurora"
                ),
                RecommendationResponse(
                    title = "Caminata Consciente",
                    content = "Sal a caminar por 15 minutos prestando atención plena a cada paso. Siente tus pies tocando el suelo, observa tu entorno sin juzgar. Es una forma simple pero poderosa de practicar mindfulness.",
                    isPersonalized = false,
                    category = "mindfulness",
                    difficulty = "beginner",
                    estimatedDuration = 15,
                    tags = listOf("caminata", "mindfulness", "ejercicio"),
                    therapistId = "alejandro"
                )
            )
        }
        
        return recommendations.random()
    }

    private fun generateMockArticle(topic: String, difficulty: String, length: String): ArticleResponse {
        val articles = mapOf(
            "respiración" to ArticleResponse(
                title = "Técnicas de Respiración para la Calma Interior",
                content = """
                    La respiración es una herramienta poderosa que siempre llevamos con nosotros. En este artículo, exploraremos diferentes técnicas de respiración que pueden ayudarte a encontrar calma en momentos de estrés.
                    
                    ## ¿Por qué funciona la respiración consciente?
                    
                    Cuando respiramos de manera consciente y controlada, activamos el sistema nervioso parasimpático, que es responsable de la respuesta de relajación en nuestro cuerpo. Esto reduce los niveles de cortisol y nos ayuda a sentirnos más tranquilos.
                    
                    ## Técnica 1: Respiración 4-7-8
                    
                    Esta técnica, popularizada por el Dr. Andrew Weil, es especialmente efectiva para reducir la ansiedad:
                    
                    1. Inhala por la nariz durante 4 segundos
                    2. Mantén la respiración durante 7 segundos
                    3. Exhala por la boca durante 8 segundos
                    4. Repite el ciclo 3-4 veces
                    
                    ## Técnica 2: Respiración Abdominal
                    
                    También conocida como respiración diafragmática, esta técnica ayuda a oxigenar mejor el cuerpo:
                    
                    1. Coloca una mano en el pecho y otra en el abdomen
                    2. Respira lentamente por la nariz, asegurándote de que se mueva más la mano del abdomen
                    3. Exhala lentamente por la boca
                    4. Practica durante 5-10 minutos
                    
                    ## Cuándo practicar
                    
                    Estas técnicas son más efectivas cuando las practicas regularmente, no solo en momentos de crisis. Dedica unos minutos cada día a la respiración consciente y notarás cómo mejora tu capacidad para manejar el estrés.
                    
                    Recuerda que la práctica hace la perfección. Sé paciente contigo mismo mientras desarrollas estas habilidades.
                """.trimIndent(),
                summary = "Aprende técnicas de respiración efectivas para reducir el estrés y la ansiedad, incluyendo la respiración 4-7-8 y la respiración abdominal.",
                imageUrl = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800",
                estimatedReadTime = 5,
                tags = listOf("respiración", "ansiedad", "relajación", "mindfulness"),
                metadata = mapOf(
                    "difficulty" to difficulty,
                    "length" to length,
                    "topic" to topic
                )
            ),
            "mindfulness" to ArticleResponse(
                title = "Mindfulness: El Arte de Vivir el Presente",
                content = """
                    El mindfulness, o atención plena, es la práctica de estar completamente presente en el momento actual, sin juzgar lo que está sucediendo. Esta antigua práctica, respaldada por la ciencia moderna, puede transformar tu relación con el estrés y mejorar tu bienestar general.
                    
                    ## ¿Qué es realmente el mindfulness?
                    
                    Mindfulness significa prestar atención al momento presente de manera intencional y sin juicio. No se trata de vaciar la mente o detener los pensamientos, sino de observarlos con curiosidad y aceptación.
                    
                    ## Beneficios científicamente comprobados
                    
                    La investigación ha demostrado que la práctica regular de mindfulness puede:
                    
                    - Reducir los niveles de estrés y ansiedad
                    - Mejorar la concentración y la memoria
                    - Fortalecer el sistema inmunológico
                    - Aumentar la autoconciencia emocional
                    - Mejorar la calidad del sueño
                    
                    ## Ejercicios simples para comenzar
                    
                    ### 1. Respiración consciente
                    Dedica 5 minutos a observar tu respiración sin intentar cambiarla. Simplemente nota cómo entra y sale el aire.
                    
                    ### 2. Escaneo corporal
                    Recorre mentalmente tu cuerpo desde la cabeza hasta los pies, notando cualquier sensación sin intentar cambiarla.
                    
                    ### 3. Mindfulness en actividades cotidianas
                    Elige una actividad diaria (como lavarte los dientes) y hazla con total atención, notando cada sensación.
                    
                    ## Integrando mindfulness en tu día
                    
                    No necesitas horas de meditación para beneficiarte del mindfulness. Pequeños momentos de atención plena a lo largo del día pueden marcar una gran diferencia:
                    
                    - Toma tres respiraciones conscientes antes de revisar tu teléfono
                    - Come al menos una comida al día sin distracciones
                    - Practica la escucha mindful en tus conversaciones
                    
                    Recuerda que mindfulness es una práctica, no una perfección. Cada momento de atención plena cuenta.
                """.trimIndent(),
                summary = "Descubre cómo el mindfulness puede transformar tu vida diaria, reducir el estrés y mejorar tu bienestar con ejercicios prácticos y fáciles de implementar.",
                imageUrl = "https://images.unsplash.com/photo-1499209974431-9dddcece7f88?w=800",
                estimatedReadTime = 7,
                tags = listOf("mindfulness", "meditación", "presente", "bienestar"),
                metadata = mapOf(
                    "difficulty" to difficulty,
                    "length" to length,
                    "topic" to topic
                )
            )
        )
        
        // Buscar artículo por tema o devolver uno genérico
        val matchingArticle = articles.entries.find { 
            topic.contains(it.key, ignoreCase = true) 
        }?.value
        
        return matchingArticle ?: ArticleResponse(
            title = "Bienestar Mental: ${topic.replaceFirstChar { it.uppercase() }}",
            content = """
                En este artículo exploraremos el tema de ${topic.lowercase()} y cómo puede impactar en nuestro bienestar mental.
                
                ## Introducción
                
                El bienestar mental es fundamental para una vida plena y satisfactoria. Cuando hablamos de $topic, es importante entender cómo se relaciona con nuestra salud emocional y psicológica.
                
                ## Estrategias efectivas
                
                1. **Autoconciencia**: El primer paso es reconocer y entender nuestros patrones de pensamiento y comportamiento.
                
                2. **Técnicas de relajación**: La respiración profunda, la meditación y el mindfulness pueden ser herramientas muy útiles.
                
                3. **Apoyo social**: Conectar con otros y buscar apoyo cuando lo necesitamos es crucial para nuestro bienestar.
                
                4. **Autocuidado**: Dedicar tiempo a actividades que nos nutren y nos hacen sentir bien.
                
                ## Conclusión
                
                Recuerda que cuidar tu bienestar mental es un proceso continuo. Sé paciente contigo mismo y celebra cada pequeño paso hacia una mejor salud mental.
                
                Si sientes que necesitas apoyo adicional, no dudes en buscar ayuda profesional.
            """.trimIndent(),
            summary = "Una guía completa sobre $topic y su impacto en el bienestar mental, con estrategias prácticas para mejorar tu salud emocional.",
            imageUrl = "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800",
            estimatedReadTime = 4,
            tags = listOf(topic.lowercase(), "bienestar", "salud mental", "autocuidado"),
            metadata = mapOf(
                "difficulty" to difficulty,
                "length" to length,
                "topic" to topic
            )
        )
    }

    private fun extractTopics(message: String): List<String> {
        val topics = mutableListOf<String>()
        
        when {
            message.contains("ansiedad", ignoreCase = true) -> topics.add("ansiedad")
            message.contains("estrés", ignoreCase = true) -> topics.add("estrés")
            message.contains("triste", ignoreCase = true) -> topics.add("tristeza")
            message.contains("dormir", ignoreCase = true) -> topics.add("sueño")
            message.contains("trabajo", ignoreCase = true) -> topics.add("trabajo")
            message.contains("familia", ignoreCase = true) -> topics.add("familia")
            message.contains("relación", ignoreCase = true) -> topics.add("relaciones")
        }
        
        if (topics.isEmpty()) {
            topics.add("bienestar general")
        }
        
        return topics
    }

    /**
     * Resetea los contadores para testing
     */
    fun resetCounters() {
        chatMessagesCount = 0
        recommendationsCount = 0
        articlesCount = 0
    }
}
