package com.menteencalma.app.domain.model

import kotlinx.serialization.Serializable

/**
 * Modelo de artículo optimizado para múltiples backends
 */
@Serializable
data class Article(
    val id: String = "",
    val title: String = "",
    val content: String = "",
    val summary: String = "",
    val category: String = "",
    val tags: List<String> = emptyList(),
    val isPremium: Boolean = false,
    val authorId: String = "",
    val authorName: String = "",
    val imageUrl: String? = null,
    val estimatedReadTime: Int = 0, // en minutos
    val publishedAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val viewCount: Int = 0,
    val likeCount: Int = 0,
    val metadata: ArticleMetadata? = null,
    
    // Campos para migración/compatibilidad
    val version: Int = 1,
    val source: String = "firebase"
) {
    
    @Serializable
    data class ArticleMetadata(
        val seoTitle: String? = null,
        val seoDescription: String? = null,
        val keywords: List<String> = emptyList(),
        val difficulty: String? = null, // beginner, intermediate, advanced
        val therapistRecommended: List<String> = emptyList(), // aurora, alejandro
        val relatedArticles: List<String> = emptyList(),
        val exercises: List<String> = emptyList(), // IDs de ejercicios relacionados
        
        // Analytics
        val avgRating: Float? = null,
        val completionRate: Float? = null,
        val customData: Map<String, String> = emptyMap()
    )
    
    companion object {
        const val COLLECTION_NAME = "articles"
        const val CURRENT_VERSION = 1
        
        // Categorías predefinidas
        object Categories {
            const val ANXIETY = "anxiety"
            const val DEPRESSION = "depression"
            const val STRESS = "stress"
            const val MINDFULNESS = "mindfulness"
            const val RELATIONSHIPS = "relationships"
            const val SLEEP = "sleep"
            const val SELF_CARE = "self_care"
            const val MOTIVATION = "motivation"
        }
        
        // Índices recomendados
        val FIRESTORE_INDEXES = listOf(
            "category",
            "isPremium",
            "publishedAt",
            "category,isPremium",
            "category,publishedAt",
            "isPremium,publishedAt",
            "tags",
            "authorId"
        )
        
        // Schema SQL para migración
        const val SQL_SCHEMA = """
            CREATE TABLE articles (
                id VARCHAR(255) PRIMARY KEY,
                title VARCHAR(500) NOT NULL,
                content TEXT NOT NULL,
                summary TEXT,
                category VARCHAR(100) NOT NULL,
                tags TEXT[], -- PostgreSQL array
                is_premium BOOLEAN DEFAULT FALSE,
                author_id VARCHAR(255) NOT NULL,
                author_name VARCHAR(255) NOT NULL,
                image_url VARCHAR(1000),
                estimated_read_time INTEGER DEFAULT 0,
                published_at BIGINT NOT NULL,
                updated_at BIGINT NOT NULL,
                view_count INTEGER DEFAULT 0,
                like_count INTEGER DEFAULT 0,
                metadata JSONB,
                version INTEGER DEFAULT 1,
                source VARCHAR(50) DEFAULT 'firebase',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                INDEX idx_category (category),
                INDEX idx_premium (is_premium),
                INDEX idx_published (published_at),
                INDEX idx_category_premium (category, is_premium),
                INDEX idx_tags USING GIN (tags), -- Para búsqueda en arrays
                FULLTEXT INDEX idx_content (title, content, summary) -- Para búsqueda de texto
            );
        """
    }
    
    /**
     * Convierte a formato para Firebase
     */
    fun toFirebaseMap(): Map<String, Any?> = mapOf(
        "id" to id,
        "title" to title,
        "content" to content,
        "summary" to summary,
        "category" to category,
        "tags" to tags,
        "isPremium" to isPremium,
        "authorId" to authorId,
        "authorName" to authorName,
        "imageUrl" to imageUrl,
        "estimatedReadTime" to estimatedReadTime,
        "publishedAt" to publishedAt,
        "updatedAt" to updatedAt,
        "viewCount" to viewCount,
        "likeCount" to likeCount,
        "metadata" to metadata?.let { meta ->
            mapOf(
                "seoTitle" to meta.seoTitle,
                "seoDescription" to meta.seoDescription,
                "keywords" to meta.keywords,
                "difficulty" to meta.difficulty,
                "therapistRecommended" to meta.therapistRecommended,
                "relatedArticles" to meta.relatedArticles,
                "exercises" to meta.exercises,
                "avgRating" to meta.avgRating,
                "completionRate" to meta.completionRate,
                "customData" to meta.customData
            )
        },
        "version" to version,
        "source" to source
    )
    
    /**
     * Convierte a formato para PostgreSQL/Supabase
     */
    fun toSQLMap(): Map<String, Any?> = mapOf(
        "id" to id,
        "title" to title,
        "content" to content,
        "summary" to summary,
        "category" to category,
        "tags" to tags.toTypedArray(), // PostgreSQL array
        "is_premium" to isPremium,
        "author_id" to authorId,
        "author_name" to authorName,
        "image_url" to imageUrl,
        "estimated_read_time" to estimatedReadTime,
        "published_at" to publishedAt,
        "updated_at" to updatedAt,
        "view_count" to viewCount,
        "like_count" to likeCount,
        "metadata" to metadata?.let { kotlinx.serialization.json.Json.encodeToString(ArticleMetadata.serializer(), it) },
        "version" to version,
        "source" to source
    )
    
    /**
     * Valida el artículo antes de guardarlo
     */
    fun validate(): Result<Unit> {
        return when {
            id.isBlank() -> Result.failure(IllegalArgumentException("Article ID cannot be blank"))
            title.isBlank() -> Result.failure(IllegalArgumentException("Title cannot be blank"))
            title.length > 500 -> Result.failure(IllegalArgumentException("Title too long (max 500 chars)"))
            content.isBlank() -> Result.failure(IllegalArgumentException("Content cannot be blank"))
            category.isBlank() -> Result.failure(IllegalArgumentException("Category cannot be blank"))
            authorId.isBlank() -> Result.failure(IllegalArgumentException("Author ID cannot be blank"))
            estimatedReadTime < 0 -> Result.failure(IllegalArgumentException("Read time cannot be negative"))
            publishedAt <= 0 -> Result.failure(IllegalArgumentException("Invalid published date"))
            else -> Result.success(Unit)
        }
    }
    
    /**
     * Calcula el tiempo estimado de lectura basado en el contenido
     */
    fun calculateReadTime(): Int {
        val wordsPerMinute = 200
        val wordCount = content.split("\\s+".toRegex()).size
        return maxOf(1, wordCount / wordsPerMinute)
    }
}
