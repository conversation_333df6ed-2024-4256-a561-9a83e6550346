package com.menteencalma.app.presentation.viewmodels;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u000e\u001a\u00020\u000fJ\u0006\u0010\u0010\u001a\u00020\u000fJ\u000e\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00130\u0012H\u0002J\b\u0010\u0014\u001a\u0004\u0018\u00010\u0015J\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00170\u0012J\u001a\u0010\u0018\u001a\u00020\u000f2\b\u0010\u0019\u001a\u0004\u0018\u00010\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0015J\u0006\u0010\u001c\u001a\u00020\u000fJ\u0006\u0010\u001d\u001a\u00020\u001eJ\b\u0010\u001f\u001a\u00020\u000fH\u0002J\u000e\u0010 \u001a\u00020\u000f2\u0006\u0010!\u001a\u00020\u0013J\u0006\u0010\"\u001a\u00020\u000fJ\u000e\u0010#\u001a\u00020\u000f2\u0006\u0010!\u001a\u00020\u0013J\u0016\u0010$\u001a\u00020\u000f2\u0006\u0010!\u001a\u00020\u0013H\u0082@\u00a2\u0006\u0002\u0010%R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006&"}, d2 = {"Lcom/menteencalma/app/presentation/viewmodels/SubscribeViewModel;", "Landroidx/lifecycle/ViewModel;", "databaseRepository", "Lcom/menteencalma/app/domain/repository/DatabaseRepository;", "authRepository", "Lcom/menteencalma/app/domain/repository/AuthRepository;", "(Lcom/menteencalma/app/domain/repository/DatabaseRepository;Lcom/menteencalma/app/domain/repository/AuthRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/menteencalma/app/presentation/viewmodels/SubscribeUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "clearError", "", "clearPurchaseSuccess", "getAvailableSubscriptionPlans", "", "Lcom/menteencalma/app/presentation/viewmodels/SubscriptionPlan;", "getCurrentSubscriptionPlan", "", "getSubscriptionFeatures", "Lcom/menteencalma/app/presentation/viewmodels/SubscriptionFeature;", "handlePurchaseResult", "purchaseResult", "", "error", "initializeRevenueCat", "isUserSubscribed", "", "loadUserAndPlans", "purchasePlan", "plan", "restorePurchases", "selectPlan", "simulatePurchase", "(Lcom/menteencalma/app/presentation/viewmodels/SubscriptionPlan;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class SubscribeViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.repository.DatabaseRepository databaseRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.repository.AuthRepository authRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.menteencalma.app.presentation.viewmodels.SubscribeUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.viewmodels.SubscribeUiState> uiState = null;
    
    @javax.inject.Inject()
    public SubscribeViewModel(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.DatabaseRepository databaseRepository, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.AuthRepository authRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.viewmodels.SubscribeUiState> getUiState() {
        return null;
    }
    
    private final void loadUserAndPlans() {
    }
    
    private final java.util.List<com.menteencalma.app.presentation.viewmodels.SubscriptionPlan> getAvailableSubscriptionPlans() {
        return null;
    }
    
    public final void selectPlan(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.presentation.viewmodels.SubscriptionPlan plan) {
    }
    
    public final void purchasePlan(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.presentation.viewmodels.SubscriptionPlan plan) {
    }
    
    private final java.lang.Object simulatePurchase(com.menteencalma.app.presentation.viewmodels.SubscriptionPlan plan, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    public final void restorePurchases() {
    }
    
    public final void clearError() {
    }
    
    public final void clearPurchaseSuccess() {
    }
    
    public final boolean isUserSubscribed() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCurrentSubscriptionPlan() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.presentation.viewmodels.SubscriptionFeature> getSubscriptionFeatures() {
        return null;
    }
    
    public final void initializeRevenueCat() {
    }
    
    public final void handlePurchaseResult(@org.jetbrains.annotations.Nullable()
    java.lang.Object purchaseResult, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
    }
}