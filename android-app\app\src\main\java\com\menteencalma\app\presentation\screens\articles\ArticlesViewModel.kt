package com.menteencalma.app.presentation.screens.articles

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ListenerRegistration
import com.menteencalma.app.data.service.CloudFunctionsService
import com.menteencalma.app.domain.model.GeneratedArticle
import com.menteencalma.app.domain.model.User
import com.menteencalma.app.domain.repository.DatabaseRepository
import com.menteencalma.app.domain.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.util.UUID
import javax.inject.Inject

data class ArticlesUiState(
    val currentUser: User? = null,
    val generatedArticle: GeneratedArticle? = null,
    val savedArticles: List<GeneratedArticle> = emptyList(),
    val isGenerating: Boolean = false,
    val isSaving: Boolean = false,
    val hasReachedLimit: Boolean = false,
    val errorMessage: String? = null,
    val suggestedTopics: List<String> = GeneratedArticle.SUGGESTED_TOPICS
)

@HiltViewModel
class ArticlesViewModel @Inject constructor(
    private val cloudFunctionsService: CloudFunctionsService,
    private val databaseRepository: DatabaseRepository,
    private val authRepository: AuthRepository,
    private val firestore: FirebaseFirestore
) : ViewModel() {

    private val _uiState = MutableStateFlow(ArticlesUiState())
    val uiState: StateFlow<ArticlesUiState> = _uiState.asStateFlow()

    private var savedArticlesListener: ListenerRegistration? = null

    init {
        loadCurrentUser()
        observeSavedArticles()
    }

    override fun onCleared() {
        super.onCleared()
        savedArticlesListener?.remove()
    }

    private fun loadCurrentUser() {
        viewModelScope.launch {
            try {
                val firebaseUser = authRepository.getCurrentUser()
                if (firebaseUser != null) {
                    val userResult = databaseRepository.getUser(firebaseUser.uid)
                    if (userResult.isSuccess) {
                        _uiState.value = _uiState.value.copy(currentUser = userResult.getOrNull())
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error loading user: ${e.message}"
                )
            }
        }
    }

    private fun observeSavedArticles() {
        viewModelScope.launch {
            try {
                val firebaseUser = authRepository.getCurrentUser()
                if (firebaseUser != null) {
                    observeSavedArticlesFlow(firebaseUser.uid).collect { articles ->
                        _uiState.value = _uiState.value.copy(savedArticles = articles)
                    }
                }
            } catch (e: Exception) {
                println("Error observing saved articles: ${e.message}")
            }
        }
    }

    private fun observeSavedArticlesFlow(userId: String): Flow<List<GeneratedArticle>> = callbackFlow {
        val listener = firestore.collection("users")
            .document(userId)
            .collection(GeneratedArticle.SAVED_ARTICLES_SUBCOLLECTION)
            .orderBy("savedAt", com.google.firebase.firestore.Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                val articles = snapshot?.documents?.mapNotNull { doc ->
                    try {
                        doc.toObject(GeneratedArticle::class.java)?.copy(id = doc.id)
                    } catch (e: Exception) {
                        null
                    }
                } ?: emptyList()

                trySend(articles)
            }

        savedArticlesListener = listener
        awaitClose { listener.remove() }
    }

    fun generateArticle(
        topic: String,
        difficulty: String = "beginner",
        length: String = "medium"
    ) {
        if (topic.isBlank()) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "Por favor ingresa un tema para el artículo"
            )
            return
        }

        val currentUser = _uiState.value.currentUser ?: return

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isGenerating = true,
                errorMessage = null,
                hasReachedLimit = false
            )

            try {
                val result = cloudFunctionsService.generateArticle(
                    userId = currentUser.id,
                    topic = topic,
                    difficulty = difficulty,
                    length = length
                )

                if (result.isSuccess) {
                    val response = result.getOrNull()!!

                    val article = GeneratedArticle(
                        id = UUID.randomUUID().toString(),
                        userId = currentUser.id,
                        title = response.title,
                        content = response.content,
                        topic = topic,
                        imageUrl = response.imageUrl,
                        summary = response.summary,
                        estimatedReadTime = response.estimatedReadTime,
                        tags = response.tags,
                        isSaved = false,
                        createdAt = System.currentTimeMillis(),
                        metadata = GeneratedArticle.ArticleMetadata(
                            generationModel = "gpt-4",
                            generationTime = 0L, // TODO: Medir tiempo real
                            wordCount = response.content.split("\\s+".toRegex()).size,
                            readabilityScore = 0.8f, // TODO: Calcular score real
                            sentiment = "positive", // TODO: Analizar sentiment
                            topics = response.tags,
                            userSubscriptionStatus = currentUser.subscriptionStatus,
                            customData = response.metadata.mapValues { it.value.toString() }
                        )
                    )

                    _uiState.value = _uiState.value.copy(
                        generatedArticle = article,
                        isGenerating = false
                    )
                } else {
                    val exception = result.exceptionOrNull()
                    if (exception is com.menteencalma.app.data.service.CloudFunctionException) {
                        when (exception.code) {
                            CloudFunctionsService.ARTICLE_LIMIT_REACHED -> {
                                _uiState.value = _uiState.value.copy(
                                    hasReachedLimit = true,
                                    isGenerating = false
                                )
                            }
                            else -> {
                                _uiState.value = _uiState.value.copy(
                                    errorMessage = "Error: ${exception.message}",
                                    isGenerating = false
                                )
                            }
                        }
                    } else {
                        _uiState.value = _uiState.value.copy(
                            errorMessage = "Error generating article: ${exception?.message}",
                            isGenerating = false
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Unexpected error: ${e.message}",
                    isGenerating = false
                )
            }
        }
    }

    fun saveGeneratedArticle() {
        val article = _uiState.value.generatedArticle ?: return
        val currentUser = _uiState.value.currentUser ?: return

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSaving = true, errorMessage = null)

            try {
                val savedArticle = article.copy(
                    isSaved = true,
                    savedAt = System.currentTimeMillis()
                )

                // Guardar en la subcolección savedArticles del usuario
                firestore.collection("users")
                    .document(currentUser.id)
                    .collection(GeneratedArticle.SAVED_ARTICLES_SUBCOLLECTION)
                    .document(savedArticle.id)
                    .set(savedArticle.toFirestoreMap())
                    .await()

                // Actualizar el artículo generado actual
                _uiState.value = _uiState.value.copy(
                    generatedArticle = savedArticle,
                    isSaving = false
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error saving article: ${e.message}",
                    isSaving = false
                )
            }
        }
    }

    fun deleteSavedArticle(articleId: String) {
        val currentUser = _uiState.value.currentUser ?: return

        viewModelScope.launch {
            try {
                firestore.collection("users")
                    .document(currentUser.id)
                    .collection(GeneratedArticle.SAVED_ARTICLES_SUBCOLLECTION)
                    .document(articleId)
                    .delete()
                    .await()

                // Si el artículo eliminado es el generado actual, actualizar su estado
                if (_uiState.value.generatedArticle?.id == articleId) {
                    _uiState.value = _uiState.value.copy(
                        generatedArticle = _uiState.value.generatedArticle?.copy(
                            isSaved = false,
                            savedAt = null
                        )
                    )
                }

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error deleting article: ${e.message}"
                )
            }
        }
    }

    fun clearGeneratedArticle() {
        _uiState.value = _uiState.value.copy(
            generatedArticle = null,
            hasReachedLimit = false
        )
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun isUserPremium(): Boolean {
        return _uiState.value.currentUser?.hasActiveSubscription() ?: false
    }

    fun getDailyArticleLimit(): Int {
        val user = _uiState.value.currentUser ?: return GeneratedArticle.FREE_USER_DAILY_LIMIT

        return when (user.subscriptionStatus) {
            "premium" -> GeneratedArticle.PREMIUM_USER_DAILY_LIMIT
            "premium_plus" -> GeneratedArticle.PREMIUM_PLUS_USER_DAILY_LIMIT
            else -> GeneratedArticle.FREE_USER_DAILY_LIMIT
        }
    }

    fun getArticlesByCategory(category: String): List<GeneratedArticle> {
        return _uiState.value.savedArticles.filter { article ->
            article.tags.any { tag -> tag.contains(category, ignoreCase = true) }
        }
    }

    fun searchArticles(query: String): List<GeneratedArticle> {
        if (query.isBlank()) return _uiState.value.savedArticles

        return _uiState.value.savedArticles.filter { article ->
            article.title.contains(query, ignoreCase = true) ||
            article.content.contains(query, ignoreCase = true) ||
            article.topic.contains(query, ignoreCase = true) ||
            article.tags.any { tag -> tag.contains(query, ignoreCase = true) }
        }
    }

    fun getRecentArticles(limit: Int = 5): List<GeneratedArticle> {
        return _uiState.value.savedArticles.take(limit)
    }

    fun getTotalReadTime(): Int {
        return _uiState.value.savedArticles.sumOf { it.estimatedReadTime }
    }

    fun getArticleStats(): ArticleStats {
        val articles = _uiState.value.savedArticles
        return ArticleStats(
            totalArticles = articles.size,
            totalReadTime = articles.sumOf { it.estimatedReadTime },
            favoriteCategory = articles
                .flatMap { it.tags }
                .groupingBy { it }
                .eachCount()
                .maxByOrNull { it.value }?.key ?: "General",
            averageReadTime = if (articles.isNotEmpty())
                articles.sumOf { it.estimatedReadTime } / articles.size
            else 0
        )
    }
}

data class ArticleStats(
    val totalArticles: Int,
    val totalReadTime: Int,
    val favoriteCategory: String,
    val averageReadTime: Int
)
