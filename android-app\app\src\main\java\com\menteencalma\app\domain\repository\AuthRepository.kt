package com.menteencalma.app.domain.repository

import com.google.firebase.auth.FirebaseUser

/**
 * Repositorio para operaciones de autenticación
 */
interface AuthRepository {
    
    /**
     * Obtiene el usuario actual autenticado
     */
    suspend fun getCurrentUser(): FirebaseUser?
    
    /**
     * Inicia sesión con email y contraseña
     */
    suspend fun signInWithEmail(email: String, password: String): Result<FirebaseUser>
    
    /**
     * Registra un nuevo usuario con email y contraseña
     */
    suspend fun signUpWithEmail(email: String, password: String): Result<FirebaseUser>
    
    /**
     * Inicia sesión con Google
     */
    suspend fun signInWithGoogle(idToken: String): Result<FirebaseUser>
    
    /**
     * Cierra la sesión del usuario actual
     */
    suspend fun signOut()
    
    /**
     * Envía email de restablecimiento de contraseña
     */
    suspend fun sendPasswordResetEmail(email: String): Result<Unit>
}
