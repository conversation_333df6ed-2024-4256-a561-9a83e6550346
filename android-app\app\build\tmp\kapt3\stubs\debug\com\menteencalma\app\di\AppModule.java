package com.menteencalma.app.di;

@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0018\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0007J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0007J\b\u0010\u0011\u001a\u00020\u0012H\u0007J\u0012\u0010\u0013\u001a\u00020\u00102\b\b\u0001\u0010\u0014\u001a\u00020\u0010H\u0007J\b\u0010\u0015\u001a\u00020\u0006H\u0007J\u0010\u0010\u0016\u001a\u00020\u00102\u0006\u0010\u0017\u001a\u00020\u0018H\u0007J\b\u0010\u0019\u001a\u00020\u0018H\u0007J\b\u0010\u001a\u001a\u00020\nH\u0007J\u001a\u0010\u001b\u001a\u00020\u00102\b\b\u0001\u0010\u001c\u001a\u00020\u00102\u0006\u0010\u001d\u001a\u00020\u0012H\u0007J\u0010\u0010\u001e\u001a\u00020\u001f2\u0006\u0010\u0017\u001a\u00020\u0018H\u0007\u00a8\u0006 "}, d2 = {"Lcom/menteencalma/app/di/AppModule;", "", "()V", "provideAuthRepository", "Lcom/menteencalma/app/domain/repository/AuthRepository;", "firebaseAuth", "Lcom/google/firebase/auth/FirebaseAuth;", "provideCloudFunctionsService", "Lcom/menteencalma/app/data/service/CloudFunctionsService;", "functions", "Lcom/google/firebase/functions/FirebaseFunctions;", "mockService", "Lcom/menteencalma/app/data/service/MockCloudFunctionsService;", "provideDatabaseMigrationService", "Lcom/menteencalma/app/data/migration/DatabaseMigrationService;", "databaseRepository", "Lcom/menteencalma/app/domain/repository/DatabaseRepository;", "provideDatabaseMonitoringService", "Lcom/menteencalma/app/data/monitoring/DatabaseMonitoringService;", "provideDatabaseRepository", "repository", "provideFirebaseAuth", "provideFirebaseDatabaseRepository", "firestore", "Lcom/google/firebase/firestore/FirebaseFirestore;", "provideFirebaseFirestore", "provideFirebaseFunctions", "provideMonitoredDatabaseRepository", "actualRepository", "monitoringService", "provideUserRepository", "Lcom/menteencalma/app/domain/repository/UserRepository;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class AppModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.menteencalma.app.di.AppModule INSTANCE = null;
    
    private AppModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.google.firebase.auth.FirebaseAuth provideFirebaseAuth() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.google.firebase.firestore.FirebaseFirestore provideFirebaseFirestore() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.google.firebase.functions.FirebaseFunctions provideFirebaseFunctions() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.domain.repository.AuthRepository provideAuthRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.auth.FirebaseAuth firebaseAuth) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.domain.repository.UserRepository provideUserRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.data.monitoring.DatabaseMonitoringService provideDatabaseMonitoringService() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @FirebaseDatabase()
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.domain.repository.DatabaseRepository provideFirebaseDatabaseRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @MonitoredDatabase()
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.domain.repository.DatabaseRepository provideMonitoredDatabaseRepository(@FirebaseDatabase()
    @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.DatabaseRepository actualRepository, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.data.monitoring.DatabaseMonitoringService monitoringService) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.domain.repository.DatabaseRepository provideDatabaseRepository(@MonitoredDatabase()
    @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.DatabaseRepository repository) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.data.migration.DatabaseMigrationService provideDatabaseMigrationService(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.DatabaseRepository databaseRepository) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.data.service.CloudFunctionsService provideCloudFunctionsService(@org.jetbrains.annotations.NotNull()
    com.google.firebase.functions.FirebaseFunctions functions, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.data.service.MockCloudFunctionsService mockService) {
        return null;
    }
}