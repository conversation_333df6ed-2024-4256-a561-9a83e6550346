import React, { createContext, useContext, useEffect, useState } from 'react';
import { doc, updateDoc, onSnapshot } from 'firebase/firestore';
import { db } from '../config/firebase';
import { useAuth } from './AuthContext';
import * as InAppPurchases from 'expo-in-app-purchases';

const SubscriptionContext = createContext();

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (!context) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};

export const SubscriptionProvider = ({ children }) => {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState(null);
  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState([]);

  // IDs de productos de suscripción (configura estos en Google Play Console)
  const SUBSCRIPTION_SKUS = [
    'mente_calma_monthly',
    'mente_calma_yearly'
  ];

  useEffect(() => {
    if (user) {
      // Escuchar cambios en la suscripción del usuario
      const unsubscribe = onSnapshot(
        doc(db, 'users', user.uid),
        (doc) => {
          if (doc.exists()) {
            const userData = doc.data();
            setSubscription(userData.subscription || null);
          }
          setLoading(false);
        },
        (error) => {
          console.error('Error listening to subscription changes:', error);
          setLoading(false);
        }
      );

      return unsubscribe;
    } else {
      setSubscription(null);
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    initializeInAppPurchases();
  }, []);

  const initializeInAppPurchases = async () => {
    try {
      await InAppPurchases.connectAsync();
      const { results } = await InAppPurchases.getProductsAsync(SUBSCRIPTION_SKUS);
      setProducts(results);
    } catch (error) {
      console.error('Error initializing in-app purchases:', error);
    }
  };

  const purchaseSubscription = async (productId) => {
    try {
      setLoading(true);
      const { results } = await InAppPurchases.purchaseItemAsync(productId);
      
      if (results && results.length > 0) {
        const purchase = results[0];
        
        // Verificar la compra con Google Play
        await verifyPurchase(purchase);
        
        return purchase;
      }
    } catch (error) {
      console.error('Error purchasing subscription:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const verifyPurchase = async (purchase) => {
    try {
      // Aquí deberías verificar la compra con tu backend
      // Por ahora, actualizamos directamente Firestore
      const subscriptionData = {
        status: 'active',
        plan: purchase.productId,
        platform: 'google_play',
        purchaseToken: purchase.purchaseToken,
        expiresAt: new Date(purchase.expirationDate),
        updatedAt: new Date()
      };

      await updateDoc(doc(db, 'users', user.uid), {
        subscription: subscriptionData
      });

      // Finalizar la compra
      await InAppPurchases.finishTransactionAsync(purchase, true);
      
    } catch (error) {
      console.error('Error verifying purchase:', error);
      throw error;
    }
  };

  const restorePurchases = async () => {
    try {
      setLoading(true);
      const { results } = await InAppPurchases.getPurchaseHistoryAsync();
      
      // Procesar compras restauradas
      for (const purchase of results) {
        if (purchase.acknowledged === false) {
          await verifyPurchase(purchase);
        }
      }
    } catch (error) {
      console.error('Error restoring purchases:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const isSubscriptionActive = () => {
    if (!subscription) return false;
    
    if (subscription.status !== 'active') return false;
    
    if (subscription.expiresAt) {
      const expirationDate = subscription.expiresAt.toDate ? 
        subscription.expiresAt.toDate() : 
        new Date(subscription.expiresAt);
      
      return expirationDate > new Date();
    }
    
    return true;
  };

  const getSubscriptionPlan = () => {
    return subscription?.plan || null;
  };

  const getSubscriptionPlatform = () => {
    return subscription?.platform || null;
  };

  const value = {
    subscription,
    loading,
    products,
    purchaseSubscription,
    restorePurchases,
    isSubscriptionActive,
    getSubscriptionPlan,
    getSubscriptionPlatform
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
};
