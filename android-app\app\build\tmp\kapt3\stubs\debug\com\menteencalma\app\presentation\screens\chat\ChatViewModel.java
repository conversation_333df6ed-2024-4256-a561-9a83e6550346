package com.menteencalma.app.presentation.screens.chat;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\u0010\u001a\u00020\u00112\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u0013H\u0002J\u0006\u0010\u0015\u001a\u00020\u0016J\u0006\u0010\u0017\u001a\u00020\u0018J\u0012\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001aH\u0002J\u0012\u0010\u001c\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001aH\u0002J\b\u0010\u001d\u001a\u00020\u0016H\u0002J\u0006\u0010\u001e\u001a\u00020\u0016J\b\u0010\u001f\u001a\u00020\u0016H\u0002J\u0006\u0010 \u001a\u00020\u0016J\u000e\u0010!\u001a\u00020\u00162\u0006\u0010\"\u001a\u00020\u001aR\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006#"}, d2 = {"Lcom/menteencalma/app/presentation/screens/chat/ChatViewModel;", "Landroidx/lifecycle/ViewModel;", "cloudFunctionsService", "Lcom/menteencalma/app/data/service/CloudFunctionsService;", "databaseRepository", "Lcom/menteencalma/app/domain/repository/DatabaseRepository;", "authRepository", "Lcom/menteencalma/app/domain/repository/AuthRepository;", "(Lcom/menteencalma/app/data/service/CloudFunctionsService;Lcom/menteencalma/app/domain/repository/DatabaseRepository;Lcom/menteencalma/app/domain/repository/AuthRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/menteencalma/app/presentation/screens/chat/ChatUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "calculateAverageResponseTime", "", "messages", "", "Lcom/menteencalma/app/domain/model/ChatMessage;", "clearError", "", "getConversationStats", "Lcom/menteencalma/app/presentation/screens/chat/ConversationStats;", "getTherapistAvatar", "", "therapistGender", "getTherapistName", "loadCurrentUser", "markMessagesAsRead", "observeMessages", "resetLimitReached", "sendMessage", "text", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class ChatViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.data.service.CloudFunctionsService cloudFunctionsService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.repository.DatabaseRepository databaseRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.repository.AuthRepository authRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.menteencalma.app.presentation.screens.chat.ChatUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.screens.chat.ChatUiState> uiState = null;
    
    @javax.inject.Inject()
    public ChatViewModel(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.data.service.CloudFunctionsService cloudFunctionsService, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.DatabaseRepository databaseRepository, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.AuthRepository authRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.screens.chat.ChatUiState> getUiState() {
        return null;
    }
    
    private final void loadCurrentUser() {
    }
    
    private final void observeMessages() {
    }
    
    public final void sendMessage(@org.jetbrains.annotations.NotNull()
    java.lang.String text) {
    }
    
    public final void clearError() {
    }
    
    public final void resetLimitReached() {
    }
    
    private final java.lang.String getTherapistName(java.lang.String therapistGender) {
        return null;
    }
    
    private final java.lang.String getTherapistAvatar(java.lang.String therapistGender) {
        return null;
    }
    
    /**
     * Marca mensajes como leídos
     */
    public final void markMessagesAsRead() {
    }
    
    /**
     * Obtiene estadísticas de la conversación
     */
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.presentation.screens.chat.ConversationStats getConversationStats() {
        return null;
    }
    
    private final long calculateAverageResponseTime(java.util.List<com.menteencalma.app.domain.model.ChatMessage> messages) {
        return 0L;
    }
}