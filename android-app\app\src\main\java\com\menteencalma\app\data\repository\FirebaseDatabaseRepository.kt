package com.menteencalma.app.data.repository

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.ListenerRegistration
import com.menteencalma.app.domain.model.*
import com.menteencalma.app.domain.repository.DatabaseRepository
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FirebaseDatabaseRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) : DatabaseRepository {

    // ==================== USER OPERATIONS ====================

    override suspend fun getUser(userId: String): Result<User?> {
        return try {
            val document = firestore.collection(User.COLLECTION_NAME)
                .document(userId)
                .get()
                .await()
            
            val user = document.toObject(User::class.java)
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun createUser(user: User): Result<Unit> {
        return try {
            // Validar antes de crear
            user.validate().getOrThrow()
            
            firestore.collection(User.COLLECTION_NAME)
                .document(user.id)
                .set(user.toFirebaseMap())
                .await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateUser(user: User): Result<Unit> {
        return try {
            // Validar antes de actualizar
            user.validate().getOrThrow()
            
            val updatedUser = user.copy(updatedAt = System.currentTimeMillis())
            
            firestore.collection(User.COLLECTION_NAME)
                .document(user.id)
                .set(updatedUser.toFirebaseMap())
                .await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun observeUser(userId: String): Flow<User?> = callbackFlow {
        val listener: ListenerRegistration = firestore.collection(User.COLLECTION_NAME)
            .document(userId)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val user = snapshot?.toObject(User::class.java)
                trySend(user)
            }

        awaitClose { listener.remove() }
    }

    override suspend fun searchUsers(
        ageRange: IntRange?,
        gender: String?,
        therapistPreference: String?,
        subscriptionStatus: String?,
        limit: Int
    ): Result<List<User>> {
        return try {
            var query: Query = firestore.collection(User.COLLECTION_NAME)
            
            // Aplicar filtros
            gender?.let { query = query.whereEqualTo("gender", it) }
            therapistPreference?.let { query = query.whereEqualTo("therapistGender", it) }
            subscriptionStatus?.let { query = query.whereEqualTo("subscriptionStatus", it) }
            
            // Nota: Firestore no puede filtrar por rango de edad junto con otros filtros
            // Para consultas complejas, considera migrar a PostgreSQL/Supabase
            
            val documents = query.limit(limit.toLong()).get().await()
            val users = documents.toObjects(User::class.java)
            
            // Filtrar por edad en memoria (no ideal para grandes datasets)
            val filteredUsers = if (ageRange != null) {
                users.filter { user -> user.age?.let { it in ageRange } ?: false }
            } else {
                users
            }
            
            Result.success(filteredUsers)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // ==================== CHAT OPERATIONS ====================

    override suspend fun getChatMessages(
        userId: String,
        limit: Int,
        lastMessageId: String?
    ): Result<List<ChatMessage>> {
        return try {
            var query = firestore.collection(ChatMessage.COLLECTION_NAME)
                .whereEqualTo("userId", userId)
                .orderBy("timestamp", Query.Direction.DESCENDING)
                .limit(limit.toLong())
            
            // Paginación
            lastMessageId?.let { lastId ->
                val lastDoc = firestore.collection(ChatMessage.COLLECTION_NAME)
                    .document(lastId)
                    .get()
                    .await()
                query = query.startAfter(lastDoc)
            }
            
            val documents = query.get().await()
            val messages = documents.toObjects(ChatMessage::class.java)
            
            Result.success(messages.reversed()) // Orden cronológico
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun sendChatMessage(message: ChatMessage): Result<Unit> {
        return try {
            // Validar antes de enviar
            message.validate().getOrThrow()
            
            firestore.collection(ChatMessage.COLLECTION_NAME)
                .document(message.id)
                .set(message.toFirebaseMap())
                .await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun observeChatMessages(userId: String): Flow<List<ChatMessage>> = callbackFlow {
        val listener: ListenerRegistration = firestore.collection(ChatMessage.COLLECTION_NAME)
            .whereEqualTo("userId", userId)
            .orderBy("timestamp", Query.Direction.ASCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val messages = snapshot?.toObjects(ChatMessage::class.java) ?: emptyList()
                trySend(messages)
            }

        awaitClose { listener.remove() }
    }

    override suspend fun markMessagesAsRead(userId: String, messageIds: List<String>): Result<Unit> {
        return try {
            val batch = firestore.batch()
            
            messageIds.forEach { messageId ->
                val messageRef = firestore.collection(ChatMessage.COLLECTION_NAME).document(messageId)
                batch.update(messageRef, "isRead", true)
            }
            
            batch.commit().await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // ==================== SUBSCRIPTION OPERATIONS ====================

    override suspend fun getSubscription(userId: String): Result<Subscription?> {
        return try {
            val userResult = getUser(userId)
            val user = userResult.getOrNull()
            
            val subscription = user?.let {
                Subscription(
                    status = it.subscriptionStatus,
                    plan = null, // TODO: Añadir campo plan al User
                    platform = it.subscriptionPlatform,
                    expiresAt = it.subscriptionExpiresAt,
                    isActive = it.hasActiveSubscription()
                )
            }
            
            Result.success(subscription)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateSubscription(subscription: Subscription): Result<Unit> {
        return try {
            // Esta implementación requiere el userId, que debería estar en el subscription
            // Por ahora, retornamos un error indicando que se necesita implementar
            Result.failure(NotImplementedError("updateSubscription requires userId - consider adding to Subscription model"))
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getSubscriptionStats(startDate: Long, endDate: Long): Result<Map<String, Any>> {
        return try {
            // Firestore no es ideal para analytics complejos
            // Esta consulta sería mucho más eficiente en PostgreSQL
            val users = firestore.collection(User.COLLECTION_NAME)
                .whereGreaterThanOrEqualTo("createdAt", startDate)
                .whereLessThanOrEqualTo("createdAt", endDate)
                .get()
                .await()
                .toObjects(User::class.java)
            
            val stats = mapOf(
                "totalUsers" to users.size,
                "freeUsers" to users.count { it.subscriptionStatus == "free" },
                "premiumUsers" to users.count { it.subscriptionStatus == "premium" },
                "premiumPlusUsers" to users.count { it.subscriptionStatus == "premium_plus" },
                "googlePlayUsers" to users.count { it.subscriptionPlatform == "google_play" },
                "paypalUsers" to users.count { it.subscriptionPlatform == "paypal" },
                "stripeUsers" to users.count { it.subscriptionPlatform == "stripe" }
            )
            
            Result.success(stats)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // ==================== CONTENT OPERATIONS ====================

    override suspend fun getArticles(
        category: String?,
        isPremium: Boolean?,
        limit: Int,
        offset: Int
    ): Result<List<Article>> {
        return try {
            var query: Query = firestore.collection(Article.COLLECTION_NAME)
                .orderBy("publishedAt", Query.Direction.DESCENDING)
            
            // Aplicar filtros
            category?.let { query = query.whereEqualTo("category", it) }
            isPremium?.let { query = query.whereEqualTo("isPremium", it) }
            
            // Nota: Firestore no soporta offset nativo, usar cursor-based pagination
            val documents = query.limit(limit.toLong()).get().await()
            val articles = documents.toObjects(Article::class.java)
            
            Result.success(articles)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getArticle(articleId: String): Result<Article?> {
        return try {
            val document = firestore.collection(Article.COLLECTION_NAME)
                .document(articleId)
                .get()
                .await()
            
            val article = document.toObject(Article::class.java)
            Result.success(article)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun recordArticleRead(userId: String, articleId: String): Result<Unit> {
        return try {
            // Incrementar contador de vistas del artículo
            val articleRef = firestore.collection(Article.COLLECTION_NAME).document(articleId)
            firestore.runTransaction { transaction ->
                val article = transaction.get(articleRef).toObject(Article::class.java)
                article?.let {
                    transaction.update(articleRef, "viewCount", it.viewCount + 1)
                }
            }.await()
            
            // Añadir a la lista de artículos leídos del usuario
            val userRef = firestore.collection(User.COLLECTION_NAME).document(userId)
            firestore.runTransaction { transaction ->
                val user = transaction.get(userRef).toObject(User::class.java)
                user?.let {
                    val updatedArticles = it.analytics.completedArticles.toMutableList()
                    if (!updatedArticles.contains(articleId)) {
                        updatedArticles.add(articleId)
                        val updatedAnalytics = it.analytics.copy(completedArticles = updatedArticles)
                        transaction.update(userRef, "analytics", updatedAnalytics)
                    }
                }
            }.await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // ==================== ANALYTICS OPERATIONS ====================

    override suspend fun logEvent(eventName: String, parameters: Map<String, Any>): Result<Unit> {
        return try {
            // Para Firebase, usarías Firebase Analytics
            // Por ahora, guardamos en una colección de eventos
            val event = mapOf(
                "eventName" to eventName,
                "parameters" to parameters,
                "timestamp" to System.currentTimeMillis()
            )
            
            firestore.collection("analytics_events")
                .add(event)
                .await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getUsageMetrics(
        userId: String,
        startDate: Long,
        endDate: Long
    ): Result<Map<String, Any>> {
        return try {
            // Obtener métricas del usuario
            val user = getUser(userId).getOrNull()
            
            val metrics = user?.let {
                mapOf(
                    "totalSessions" to it.analytics.totalSessions,
                    "totalMinutes" to it.analytics.totalMinutes,
                    "streakDays" to it.analytics.streakDays,
                    "longestStreak" to it.analytics.longestStreak,
                    "completedArticles" to it.analytics.completedArticles.size,
                    "favoriteExercises" to it.analytics.favoriteExercises.size,
                    "moodEntries" to it.analytics.moodHistory.count { mood ->
                        mood.date in startDate..endDate
                    }
                )
            } ?: emptyMap()
            
            Result.success(metrics)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // ==================== BATCH OPERATIONS ====================

    override suspend fun executeTransaction(operations: List<DatabaseRepository.DatabaseOperation>): Result<Unit> {
        return try {
            firestore.runTransaction { transaction ->
                operations.forEach { operation ->
                    when (operation) {
                        is DatabaseRepository.DatabaseOperation.CreateUser -> {
                            val userRef = firestore.collection(User.COLLECTION_NAME).document(operation.user.id)
                            transaction.set(userRef, operation.user.toFirebaseMap())
                        }
                        is DatabaseRepository.DatabaseOperation.UpdateUser -> {
                            val userRef = firestore.collection(User.COLLECTION_NAME).document(operation.user.id)
                            transaction.set(userRef, operation.user.toFirebaseMap())
                        }
                        is DatabaseRepository.DatabaseOperation.CreateMessage -> {
                            val messageRef = firestore.collection(ChatMessage.COLLECTION_NAME).document(operation.message.id)
                            transaction.set(messageRef, operation.message.toFirebaseMap())
                        }
                        is DatabaseRepository.DatabaseOperation.UpdateSubscription -> {
                            // Implementar cuando se añada userId a Subscription
                        }
                    }
                }
            }.await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
