import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView
} from 'react-native';
import { useSubscription } from '../contexts/SubscriptionContext';

const SubscriptionScreen = ({ navigation }) => {
  const { 
    products, 
    purchaseSubscription, 
    restorePurchases, 
    loading,
    isSubscriptionActive 
  } = useSubscription();
  
  const [selectedPlan, setSelectedPlan] = useState(null);

  const handlePurchase = async () => {
    if (!selectedPlan) {
      Alert.alert('Error', 'Por favor selecciona un plan');
      return;
    }

    try {
      await purchaseSubscription(selectedPlan);
      Alert.alert(
        'Éxito', 
        'Suscripción activada correctamente',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      Alert.alert('Error', 'No se pudo procesar la compra. Intenta nuevamente.');
    }
  };

  const handleRestorePurchases = async () => {
    try {
      await restorePurchases();
      Alert.alert('Éxito', 'Compras restauradas correctamente');
    } catch (error) {
      Alert.alert('Error', 'No se pudieron restaurar las compras');
    }
  };

  const getPlanDetails = (productId) => {
    switch (productId) {
      case 'mente_calma_monthly':
        return {
          title: 'Plan Mensual',
          price: '$9.99',
          period: 'mes',
          savings: null
        };
      case 'mente_calma_yearly':
        return {
          title: 'Plan Anual',
          price: '$79.99',
          period: 'año',
          savings: 'Ahorra 33%'
        };
      default:
        return {
          title: 'Plan Premium',
          price: 'N/A',
          period: 'mes',
          savings: null
        };
    }
  };

  const features = [
    '🧘‍♀️ Meditaciones guiadas ilimitadas',
    '🎵 Biblioteca completa de sonidos relajantes',
    '📱 Acceso sin conexión',
    '📊 Seguimiento de progreso detallado',
    '🌙 Sesiones para dormir mejor',
    '💭 Técnicas avanzadas de mindfulness',
    '🎯 Programas personalizados',
    '🔄 Sincronización entre dispositivos'
  ];

  if (isSubscriptionActive()) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.activeSubscriptionContainer}>
          <Text style={styles.activeTitle}>¡Suscripción Activa!</Text>
          <Text style={styles.activeSubtitle}>
            Disfruta de todas las funciones premium
          </Text>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>Volver</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <Text style={styles.title}>Desbloquea Todo el Potencial</Text>
          <Text style={styles.subtitle}>
            Accede a contenido premium y funciones exclusivas
          </Text>
        </View>

        {/* Features List */}
        <View style={styles.featuresContainer}>
          <Text style={styles.featuresTitle}>¿Qué incluye Premium?</Text>
          {features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>

        {/* Subscription Plans */}
        <View style={styles.plansContainer}>
          <Text style={styles.plansTitle}>Elige tu Plan</Text>
          
          {products.length > 0 ? (
            products.map((product) => {
              const planDetails = getPlanDetails(product.productId);
              const isSelected = selectedPlan === product.productId;
              
              return (
                <TouchableOpacity
                  key={product.productId}
                  style={[
                    styles.planCard,
                    isSelected && styles.planCardSelected
                  ]}
                  onPress={() => setSelectedPlan(product.productId)}
                >
                  <View style={styles.planHeader}>
                    <Text style={styles.planTitle}>{planDetails.title}</Text>
                    {planDetails.savings && (
                      <View style={styles.savingsTag}>
                        <Text style={styles.savingsText}>{planDetails.savings}</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.planPrice}>
                    {product.price || planDetails.price}
                    <Text style={styles.planPeriod}>/{planDetails.period}</Text>
                  </Text>
                  <View style={[
                    styles.radioButton,
                    isSelected && styles.radioButtonSelected
                  ]}>
                    {isSelected && <View style={styles.radioButtonInner} />}
                  </View>
                </TouchableOpacity>
              );
            })
          ) : (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Cargando planes...</Text>
            </View>
          )}
        </View>

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[
              styles.purchaseButton,
              (!selectedPlan || loading) && styles.purchaseButtonDisabled
            ]}
            onPress={handlePurchase}
            disabled={!selectedPlan || loading}
          >
            <Text style={styles.purchaseButtonText}>
              {loading ? 'Procesando...' : 'Suscribirse Ahora'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.restoreButton}
            onPress={handleRestorePurchases}
            disabled={loading}
          >
            <Text style={styles.restoreButtonText}>Restaurar Compras</Text>
          </TouchableOpacity>
        </View>

        {/* Terms */}
        <View style={styles.termsContainer}>
          <Text style={styles.termsText}>
            La suscripción se renovará automáticamente. Puedes cancelar en cualquier momento 
            desde la configuración de tu cuenta de Google Play.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: 'white',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  featuresContainer: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
  },
  featuresTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  featureItem: {
    marginBottom: 12,
  },
  featureText: {
    fontSize: 16,
    color: '#333',
  },
  plansContainer: {
    margin: 20,
  },
  plansTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  planCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    borderWidth: 2,
    borderColor: '#e1e5e9',
    flexDirection: 'row',
    alignItems: 'center',
  },
  planCardSelected: {
    borderColor: '#4A90E2',
    backgroundColor: '#f0f7ff',
  },
  planHeader: {
    flex: 1,
  },
  planTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  savingsTag: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  savingsText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  planPrice: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4A90E2',
    marginRight: 15,
  },
  planPeriod: {
    fontSize: 16,
    color: '#666',
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#ccc',
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonSelected: {
    borderColor: '#4A90E2',
  },
  radioButtonInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4A90E2',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  actionsContainer: {
    margin: 20,
  },
  purchaseButton: {
    backgroundColor: '#4A90E2',
    borderRadius: 15,
    padding: 18,
    alignItems: 'center',
    marginBottom: 15,
  },
  purchaseButtonDisabled: {
    backgroundColor: '#ccc',
  },
  purchaseButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  restoreButton: {
    alignItems: 'center',
    padding: 15,
  },
  restoreButtonText: {
    color: '#4A90E2',
    fontSize: 16,
  },
  termsContainer: {
    margin: 20,
    marginTop: 0,
  },
  termsText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    lineHeight: 18,
  },
  activeSubscriptionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  activeTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 10,
  },
  activeSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  backButton: {
    backgroundColor: '#4A90E2',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default SubscriptionScreen;
