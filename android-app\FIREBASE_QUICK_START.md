# 🚀 Firebase Quick Start - Mente en Calma Android

## ⚡ Configuración Rápida (5 minutos)

### **1. 📋 Prerrequisitos**
- [ ] Node.js instalado
- [ ] Firebase CLI: `npm install -g firebase-tools`
- [ ] Android Studio configurado
- [ ] Proyecto Firebase existente o crear uno nuevo

### **2. 🔥 Configuración Automática**

**En Windows:**
```bash
cd android-app
scripts\setup-firebase.bat
```

**En macOS/Linux:**
```bash
cd android-app
chmod +x scripts/setup-firebase.sh
./scripts/setup-firebase.sh
```

### **3. 📱 Configuración Manual (si prefieres)**

#### **A. Descargar google-services.json**
1. Ve a [Firebase Console](https://console.firebase.google.com/)
2. Selecciona tu proyecto
3. Añade app Android con package: `com.menteencalma.app`
4. Descarga `google-services.json`
5. Colócalo en: `android-app/app/google-services.json`

#### **B. Habilitar Servicios**
En Firebase Console, habilita:
- ✅ **Authentication** → Email/Password + Google Sign-In
- ✅ **Firestore Database** → Modo de prueba
- ✅ **Cloud Functions** → Plan Blaze (requerido)

#### **C. Configurar Authentication**
1. Ve a **Authentication → Sign-in method**
2. Habilita **Email/Password**
3. Habilita **Google Sign-In**
4. Añade SHA-1 certificate (obtener con `./gradlew signingReport`)

#### **D. Configurar Firestore**
1. Crea base de datos en modo de prueba
2. Despliega reglas: `firebase deploy --only firestore:rules`
3. Despliega índices: `firebase deploy --only firestore:indexes`

#### **E. Desplegar Cloud Functions**
```bash
cd firebase-functions
npm install
cd ..
firebase deploy --only functions
```

### **4. 🔧 Configuración Local**

Crea/actualiza `android-app/local.properties`:
```properties
# Firebase
firebase.project.id=tu-proyecto-id
firebase.region=us-central1

# Google Sign-In (obtener de Firebase Console)
google.signin.web.client.id=tu-web-client-id.apps.googleusercontent.com

# Gemini AI (obtener de Google AI Studio)
gemini.api.key=tu-gemini-api-key
```

### **5. ✅ Verificar Configuración**

#### **A. Compilar App**
```bash
cd android-app
./gradlew assembleDebug
```

#### **B. Probar Funcionalidades**
- [ ] Login con email/password
- [ ] Login con Google
- [ ] Crear perfil de usuario
- [ ] Enviar mensaje de chat
- [ ] Registrar estado de ánimo
- [ ] Generar recomendación
- [ ] Crear artículo

#### **C. Verificar en Firebase Console**
- [ ] Usuarios aparecen en Authentication
- [ ] Documentos se crean en Firestore
- [ ] Cloud Functions se ejecutan sin errores

### **6. 🐛 Solución de Problemas**

#### **Error: "Default FirebaseApp is not initialized"**
- Verificar que `google-services.json` está en `app/`
- Verificar que el plugin está aplicado en `build.gradle.kts`

#### **Error: "Google Sign-In failed"**
- Verificar SHA-1 certificate en Firebase Console
- Verificar Web Client ID en `local.properties`

#### **Error: "Cloud Functions not found"**
- Verificar que las funciones están desplegadas
- Verificar región en configuración

#### **Error: "Firestore permission denied"**
- Verificar reglas de Firestore
- Verificar que el usuario está autenticado

### **7. 📊 Monitoreo**

#### **A. Firebase Console**
- **Authentication**: Ver usuarios registrados
- **Firestore**: Ver datos almacenados
- **Functions**: Ver logs y métricas
- **Analytics**: Ver eventos de la app

#### **B. Logs Útiles**
```bash
# Ver logs de Cloud Functions
firebase functions:log

# Ver logs en tiempo real
firebase functions:log --follow

# Ver logs de función específica
firebase functions:log --only createUserProfile
```

### **8. 🚀 Despliegue a Producción**

#### **A. Configurar Reglas de Producción**
```javascript
// Firestore rules más estrictas
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth != null 
        && request.auth.uid == userId
        && request.auth.token.email_verified == true;
    }
  }
}
```

#### **B. Configurar Límites**
- Establecer cuotas de uso en Firebase Console
- Configurar alertas de facturación
- Monitorear métricas de rendimiento

#### **C. Optimizar Costos**
- Revisar uso de Cloud Functions
- Optimizar consultas de Firestore
- Configurar TTL para datos temporales

---

## 🎯 **Checklist Final**

- [ ] ✅ Firebase proyecto configurado
- [ ] ✅ google-services.json descargado
- [ ] ✅ Authentication habilitado
- [ ] ✅ Firestore configurado
- [ ] ✅ Cloud Functions desplegadas
- [ ] ✅ local.properties configurado
- [ ] ✅ App compila sin errores
- [ ] ✅ Login funciona
- [ ] ✅ Datos se guardan en Firestore
- [ ] ✅ Cloud Functions responden

## 🆘 **Soporte**

Si tienes problemas:
1. Revisa los logs: `firebase functions:log`
2. Verifica la configuración en Firebase Console
3. Comprueba que todos los servicios están habilitados
4. Verifica que las reglas de Firestore permiten las operaciones

**¡Tu app Android está lista para conectarse a Firebase!** 🎉
