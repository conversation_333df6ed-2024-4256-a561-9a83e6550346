package com.menteencalma.app.presentation.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ListenerRegistration
import com.google.firebase.firestore.Query
import com.menteencalma.app.domain.model.MoodEntry
import com.menteencalma.app.domain.model.User
import com.menteencalma.app.domain.repository.DatabaseRepository
import com.menteencalma.app.domain.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

data class MoodTrackerUiState(
    val currentUser: User? = null,
    val moodEntries: List<MoodEntry> = emptyList(),
    val isLoading: Boolean = true,
    val isSaving: Boolean = false,
    val errorMessage: String? = null,
    val saveSuccess: Boolean = false,
    
    // Current entry form
    val selectedMood: MoodEntry.MoodType = MoodEntry.MoodType.NEUTRAL,
    val intensity: Int = 5,
    val description: String = "",
    val selectedTags: List<String> = emptyList(),
    val selectedActivities: List<String> = emptyList(),
    
    // Analytics
    val weeklyAverage: Float = 0f,
    val monthlyAverage: Float = 0f,
    val moodTrend: String = "stable", // improving, declining, stable
    val streakDays: Int = 0,
    val totalEntries: Int = 0
)

@HiltViewModel
class MoodTrackerViewModel @Inject constructor(
    private val databaseRepository: DatabaseRepository,
    private val authRepository: AuthRepository,
    private val firestore: FirebaseFirestore
) : ViewModel() {

    private val _uiState = MutableStateFlow(MoodTrackerUiState())
    val uiState: StateFlow<MoodTrackerUiState> = _uiState.asStateFlow()

    private var moodEntriesListener: ListenerRegistration? = null

    init {
        loadCurrentUser()
        observeMoodEntries()
    }

    override fun onCleared() {
        super.onCleared()
        moodEntriesListener?.remove()
    }

    private fun loadCurrentUser() {
        viewModelScope.launch {
            try {
                val firebaseUser = authRepository.getCurrentUser()
                if (firebaseUser != null) {
                    val userResult = databaseRepository.getUser(firebaseUser.uid)
                    if (userResult.isSuccess) {
                        _uiState.value = _uiState.value.copy(currentUser = userResult.getOrNull())
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error loading user: ${e.message}"
                )
            }
        }
    }

    private fun observeMoodEntries() {
        viewModelScope.launch {
            try {
                val firebaseUser = authRepository.getCurrentUser()
                if (firebaseUser != null) {
                    observeMoodEntriesFlow(firebaseUser.uid).collect { entries ->
                        val analytics = calculateAnalytics(entries)
                        _uiState.value = _uiState.value.copy(
                            moodEntries = entries,
                            isLoading = false,
                            weeklyAverage = analytics.weeklyAverage,
                            monthlyAverage = analytics.monthlyAverage,
                            moodTrend = analytics.trend,
                            streakDays = analytics.streakDays,
                            totalEntries = entries.size
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error loading mood entries: ${e.message}",
                    isLoading = false
                )
            }
        }
    }

    private fun observeMoodEntriesFlow(userId: String): Flow<List<MoodEntry>> = callbackFlow {
        val listener = firestore.collection("users")
            .document(userId)
            .collection(MoodEntry.SUBCOLLECTION_NAME)
            .orderBy("timestamp", Query.Direction.DESCENDING)
            .limit(100) // Limit to last 100 entries for performance
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                val entries = snapshot?.documents?.mapNotNull { doc ->
                    try {
                        val data = doc.data ?: return@mapNotNull null
                        MoodEntry(
                            id = doc.id,
                            userId = data["userId"] as? String ?: "",
                            mood = MoodEntry.MoodType.valueOf(data["mood"] as? String ?: "NEUTRAL"),
                            intensity = (data["intensity"] as? Long)?.toInt() ?: 5,
                            description = data["description"] as? String ?: "",
                            timestamp = data["timestamp"] as? Long ?: 0L,
                            date = data["date"] as? String ?: "",
                            tags = (data["tags"] as? List<*>)?.mapNotNull { it as? String } ?: emptyList(),
                            activities = (data["activities"] as? List<*>)?.mapNotNull { it as? String } ?: emptyList(),
                            location = data["location"] as? String,
                            weather = data["weather"] as? String
                        )
                    } catch (e: Exception) {
                        null
                    }
                } ?: emptyList()

                trySend(entries)
            }

        moodEntriesListener = listener
        awaitClose { listener.remove() }
    }

    fun updateSelectedMood(mood: MoodEntry.MoodType) {
        _uiState.value = _uiState.value.copy(
            selectedMood = mood,
            intensity = when (mood) {
                MoodEntry.MoodType.VERY_SAD -> 2
                MoodEntry.MoodType.SAD -> 4
                MoodEntry.MoodType.NEUTRAL -> 5
                MoodEntry.MoodType.HAPPY -> 7
                MoodEntry.MoodType.VERY_HAPPY -> 9
            },
            saveSuccess = false
        )
    }

    fun updateIntensity(intensity: Int) {
        if (intensity in 1..10) {
            _uiState.value = _uiState.value.copy(
                intensity = intensity,
                selectedMood = MoodEntry.MoodType.fromIntensity(intensity),
                saveSuccess = false
            )
        }
    }

    fun updateDescription(description: String) {
        _uiState.value = _uiState.value.copy(
            description = description,
            saveSuccess = false
        )
    }

    fun toggleTag(tag: String) {
        val currentTags = _uiState.value.selectedTags.toMutableList()
        if (currentTags.contains(tag)) {
            currentTags.remove(tag)
        } else {
            currentTags.add(tag)
        }
        _uiState.value = _uiState.value.copy(
            selectedTags = currentTags,
            saveSuccess = false
        )
    }

    fun toggleActivity(activity: String) {
        val currentActivities = _uiState.value.selectedActivities.toMutableList()
        if (currentActivities.contains(activity)) {
            currentActivities.remove(activity)
        } else {
            currentActivities.add(activity)
        }
        _uiState.value = _uiState.value.copy(
            selectedActivities = currentActivities,
            saveSuccess = false
        )
    }

    fun saveMoodEntry() {
        val currentState = _uiState.value
        val currentUser = currentState.currentUser ?: return

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isSaving = true,
                    errorMessage = null,
                    saveSuccess = false
                )

                val now = System.currentTimeMillis()
                val dateFormatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                val date = dateFormatter.format(Date(now))

                val moodEntry = MoodEntry(
                    id = UUID.randomUUID().toString(),
                    userId = currentUser.id,
                    mood = currentState.selectedMood,
                    intensity = currentState.intensity,
                    description = currentState.description.trim(),
                    timestamp = now,
                    date = date,
                    tags = currentState.selectedTags,
                    activities = currentState.selectedActivities,
                    metadata = MoodEntry.MoodMetadata(
                        entryMethod = "manual",
                        previousMood = currentState.moodEntries.firstOrNull()?.mood,
                        moodTrend = currentState.moodTrend
                    )
                )

                // Validate entry
                val validationResult = moodEntry.validate()
                if (validationResult.isFailure) {
                    throw Exception("Invalid mood entry: ${validationResult.exceptionOrNull()?.message}")
                }

                // Save to Firestore
                firestore.collection("users")
                    .document(currentUser.id)
                    .collection(MoodEntry.SUBCOLLECTION_NAME)
                    .document(moodEntry.id)
                    .set(moodEntry.toFirestoreMap())
                    .await()

                // Reset form and show success
                _uiState.value = _uiState.value.copy(
                    isSaving = false,
                    saveSuccess = true,
                    description = "",
                    selectedTags = emptyList(),
                    selectedActivities = emptyList(),
                    intensity = 5,
                    selectedMood = MoodEntry.MoodType.NEUTRAL
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error saving mood entry: ${e.message}",
                    isSaving = false
                )
            }
        }
    }

    fun deleteMoodEntry(entryId: String) {
        val currentUser = _uiState.value.currentUser ?: return

        viewModelScope.launch {
            try {
                firestore.collection("users")
                    .document(currentUser.id)
                    .collection(MoodEntry.SUBCOLLECTION_NAME)
                    .document(entryId)
                    .delete()
                    .await()

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error deleting mood entry: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun clearSaveSuccess() {
        _uiState.value = _uiState.value.copy(saveSuccess = false)
    }

    private fun calculateAnalytics(entries: List<MoodEntry>): MoodAnalytics {
        if (entries.isEmpty()) {
            return MoodAnalytics()
        }

        val now = System.currentTimeMillis()
        val oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000)
        val oneMonthAgo = now - (30 * 24 * 60 * 60 * 1000)

        val weeklyEntries = entries.filter { it.timestamp >= oneWeekAgo }
        val monthlyEntries = entries.filter { it.timestamp >= oneMonthAgo }

        val weeklyAverage = if (weeklyEntries.isNotEmpty()) {
            weeklyEntries.map { it.intensity }.average().toFloat()
        } else 0f

        val monthlyAverage = if (monthlyEntries.isNotEmpty()) {
            monthlyEntries.map { it.intensity }.average().toFloat()
        } else 0f

        // Calculate trend
        val recentEntries = entries.take(7)
        val olderEntries = entries.drop(7).take(7)
        val trend = when {
            recentEntries.isEmpty() || olderEntries.isEmpty() -> "stable"
            recentEntries.map { it.intensity }.average() > olderEntries.map { it.intensity }.average() + 0.5 -> "improving"
            recentEntries.map { it.intensity }.average() < olderEntries.map { it.intensity }.average() - 0.5 -> "declining"
            else -> "stable"
        }

        // Calculate streak (consecutive days with entries)
        val dateFormatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val today = dateFormatter.format(Date(now))
        val entryDates = entries.map { it.date }.distinct().sorted().reversed()
        
        var streakDays = 0
        var currentDate = today
        for (date in entryDates) {
            if (date == currentDate) {
                streakDays++
                // Move to previous day
                val cal = Calendar.getInstance()
                cal.time = dateFormatter.parse(currentDate) ?: break
                cal.add(Calendar.DAY_OF_MONTH, -1)
                currentDate = dateFormatter.format(cal.time)
            } else {
                break
            }
        }

        return MoodAnalytics(
            weeklyAverage = weeklyAverage,
            monthlyAverage = monthlyAverage,
            trend = trend,
            streakDays = streakDays
        )
    }

    fun getChartData(): List<Pair<Long, Float>> {
        return _uiState.value.moodEntries
            .sortedBy { it.timestamp }
            .map { it.timestamp to it.intensity.toFloat() }
    }

    fun getEntriesForDate(date: String): List<MoodEntry> {
        return _uiState.value.moodEntries.filter { it.date == date }
    }
}

data class MoodAnalytics(
    val weeklyAverage: Float = 0f,
    val monthlyAverage: Float = 0f,
    val trend: String = "stable",
    val streakDays: Int = 0
)
