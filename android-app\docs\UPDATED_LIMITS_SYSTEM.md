# 🔒 Sistema de Límites Actualizado - Mente en Calma

## 📊 **Nuevos Límites Implementados**

### **🆓 Usuarios Gratuitos**
- ✅ **Chat con IA**: 5 mensajes por día
- ✅ **Recomendaciones**: 3 recomendaciones generales por día (no personalizadas)
- ✅ **Artículos**: 5 artículos por mes
- ✅ **Reset automático**: Contadores diarios se reinician cada día, mensual cada mes
- ✅ **Paywall integrado**: Mensajes específicos para cada límite alcanzado

### **💎 Usuarios Premium**
- ✅ **Chat con IA**: Ilimitado
- ✅ **Recomendaciones**: Ilimitadas y personalizadas
- ✅ **Artículos**: Ilimitados
- ✅ **Sin restricciones**: Acceso completo a todas las funcionalidades

## 🔧 **Cambios Técnicos Implementados**

### **📱 Package Name Actualizado**
```kotlin
// Antes: com.menteencalma.app
// Ahora: com.eligi.menteencalma
applicationId = "com.eligi.menteencalma"
```

### **🗄️ Estructura de Datos Actualizada**

#### **Perfil de Usuario**
```javascript
{
  // ... otros campos
  
  // Límites diarios
  dailyLimits: {
    chatMessages: 5,           // Reducido de 10 a 5
    recommendations: 3,        // Mantenido en 3
    lastResetDate: "2024-01-15"
  },
  
  // Nuevos límites mensuales
  monthlyLimits: {
    articles: 5,               // Nuevo: 5 artículos por mes
    lastResetMonth: "2024-01"  // Formato YYYY-MM
  },
  
  // Contadores diarios
  usageToday: {
    chatMessages: 0,
    recommendations: 0
    // articles removido (ahora es mensual)
  },
  
  // Nuevos contadores mensuales
  usageThisMonth: {
    articles: 0
  }
}
```

### **💬 Chat Function - Límites Actualizados**

#### **Límite Alcanzado**
```javascript
// Respuesta cuando se alcanzan 5 mensajes diarios
{
  success: false,
  error: 'USAGE_LIMIT_REACHED',
  message: 'Has alcanzado el límite diario de 5 mensajes de chat. Suscríbete para acceso ilimitado.',
  remainingMessages: 0,
  limitType: 'chat_daily',
  upgradeMessage: 'Con la suscripción Premium tendrás chat ilimitado con tu terapeuta IA.'
}
```

#### **Respuesta Exitosa**
```javascript
{
  success: true,
  response: "Respuesta del terapeuta IA...",
  remainingMessages: 3, // Mensajes restantes para usuarios gratuitos
  therapistName: "Aurora" // o "Alejandro"
}
```

### **💡 Recommendations Function - General vs Personalizada**

#### **Usuarios Gratuitos - Recomendaciones Generales**
```javascript
{
  success: true,
  recommendations: [
    {
      id: '1',
      title: 'Respiración 4-7-8',
      description: 'Técnica básica para reducir la ansiedad',
      category: 'Respiración',
      duration: '5 minutos',
      difficulty: 'Fácil',
      icon: '🫁',
      personalized: false  // Marcado como no personalizado
    }
  ],
  isPersonalized: false,
  recommendationType: 'general',
  remainingRecommendations: 2
}
```

#### **Usuarios Premium - Recomendaciones Personalizadas**
```javascript
{
  success: true,
  recommendations: [
    {
      id: '1',
      title: 'Ejercicio Personalizado para María',
      description: 'Técnica de respiración adaptada a tu perfil con Aurora',
      category: 'Respiración Personalizada',
      duration: '7 minutos',
      difficulty: 'Adaptado a ti',
      icon: '🫁',
      personalized: true,
      therapistNote: 'Recomendado especialmente por Aurora para tu situación actual.',
      ageAdapted: 'Adaptado para 28 años'
    }
  ],
  isPersonalized: true,
  recommendationType: 'personalized',
  remainingRecommendations: -1 // Ilimitado
}
```

#### **Límite de Recomendaciones Alcanzado**
```javascript
{
  success: false,
  error: 'USAGE_LIMIT_REACHED',
  message: 'Has alcanzado el límite diario de 3 recomendaciones. Suscríbete para recomendaciones personalizadas ilimitadas.',
  limitType: 'recommendations_daily',
  upgradeMessage: 'Con Premium obtienes recomendaciones personalizadas ilimitadas basadas en tu perfil y progreso.'
}
```

### **📚 Articles Function - Límite Mensual**

#### **Límite Mensual Alcanzado**
```javascript
{
  success: false,
  error: 'ARTICLE_LIMIT_REACHED',
  message: 'Has alcanzado el límite mensual de 5 artículos. Suscríbete para generar artículos ilimitados.',
  limitType: 'articles_monthly',
  remainingArticles: 0,
  upgradeMessage: 'Con Premium puedes generar artículos personalizados ilimitados sobre cualquier tema de bienestar mental.'
}
```

#### **Artículo Generado Exitosamente**
```javascript
{
  success: true,
  article: {
    id: "article_id",
    title: "Título del artículo",
    content: "Contenido completo...",
    // ... otros campos
  },
  remainingArticles: 3, // Artículos restantes este mes
  limitType: 'monthly',
  resetDate: '2024-02-01T00:00:00.000Z' // Próximo reset mensual
}
```

## 🎯 **Diferencias Clave por Tipo de Usuario**

### **🆓 Usuario Gratuito**
| Funcionalidad | Límite | Tipo | Reset |
|---------------|--------|------|-------|
| Chat con IA | 5 mensajes | Diario | 00:00 cada día |
| Recomendaciones | 3 generales | Diario | 00:00 cada día |
| Artículos | 5 artículos | Mensual | 1er día del mes |

### **💎 Usuario Premium**
| Funcionalidad | Límite | Personalización |
|---------------|--------|-----------------|
| Chat con IA | Ilimitado | Conversación completa |
| Recomendaciones | Ilimitadas | Basadas en perfil y progreso |
| Artículos | Ilimitados | Contenido personalizado |

## 🔄 **Sistema de Reset Automático**

### **Reset Diario (00:00 UTC)**
```javascript
// Se ejecuta automáticamente en cada función
const today = new Date().toISOString().split('T')[0];
if (userData.dailyLimits.lastResetDate !== today) {
  await db.collection('users').doc(userId).update({
    'usageToday.chatMessages': 0,
    'usageToday.recommendations': 0,
    'dailyLimits.lastResetDate': today
  });
}
```

### **Reset Mensual (1er día del mes)**
```javascript
// Se ejecuta automáticamente en la función de artículos
const currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM
if (userData.monthlyLimits.lastResetMonth !== currentMonth) {
  await db.collection('users').doc(userId).update({
    'usageThisMonth.articles': 0,
    'monthlyLimits.lastResetMonth': currentMonth
  });
}
```

## 📱 **Integración en la App Android**

### **Manejo de Límites en UI**
```kotlin
// En ChatViewModel
when (response.error) {
    "USAGE_LIMIT_REACHED" -> {
        when (response.limitType) {
            "chat_daily" -> showChatLimitDialog()
            "recommendations_daily" -> showRecommendationsLimitDialog()
            "articles_monthly" -> showArticlesLimitDialog()
        }
    }
}
```

### **Mostrar Contadores en UI**
```kotlin
// Mostrar mensajes restantes
Text("Mensajes restantes hoy: ${response.remainingMessages}")

// Mostrar tipo de recomendaciones
if (response.isPersonalized) {
    Text("Recomendaciones personalizadas")
} else {
    Text("Recomendaciones generales")
}

// Mostrar próximo reset para artículos
Text("Próximo reset: ${formatDate(response.resetDate)}")
```

## 🎨 **Mensajes de Paywall Mejorados**

### **Chat Limit**
```
"Has alcanzado el límite diario de 5 mensajes de chat. 

Con la suscripción Premium tendrás:
✅ Chat ilimitado con tu terapeuta IA
✅ Conversaciones más profundas y detalladas
✅ Acceso 24/7 sin restricciones

¿Te gustaría suscribirte?"
```

### **Recommendations Limit**
```
"Has alcanzado el límite diario de 3 recomendaciones.

Con Premium obtienes:
✅ Recomendaciones personalizadas ilimitadas
✅ Basadas en tu perfil y progreso
✅ Adaptadas a tu edad y preferencias
✅ Actualizadas según tu estado de ánimo

¿Quieres acceso completo?"
```

### **Articles Limit**
```
"Has alcanzado el límite mensual de 5 artículos.

Con Premium puedes:
✅ Generar artículos ilimitados
✅ Contenido personalizado para ti
✅ Temas específicos de tu interés
✅ Artículos adaptados a tu situación

¿Te interesa la suscripción?"
```

---

## ✅ **Resumen de Cambios**

1. **Package name** actualizado a `com.eligi.menteencalma`
2. **Chat límite** reducido de 10 a 5 mensajes diarios
3. **Recomendaciones** diferenciadas: generales (gratuito) vs personalizadas (premium)
4. **Artículos** cambiados de 2 diarios a 5 mensuales
5. **Estructura de datos** actualizada con límites mensuales
6. **Mensajes de paywall** mejorados y específicos
7. **Reset automático** implementado para diario y mensual
8. **Cloud Functions** actualizadas con nueva lógica

El sistema está completamente implementado y listo para desplegar! 🚀
