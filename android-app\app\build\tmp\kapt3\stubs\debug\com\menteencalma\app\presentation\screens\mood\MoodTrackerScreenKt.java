package com.menteencalma.app.presentation.screens.mood;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000t\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\u001a2\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\t\u0010\n\u001a0\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020\u00032\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0011H\u0003\u001a\u001e\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\u00032\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u0016H\u0003\u001a\u0012\u0010\u0017\u001a\u00020\u00012\b\b\u0002\u0010\u0018\u001a\u00020\u0019H\u0003\u001a.\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001e2\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00162\u0006\u0010 \u001a\u00020\u001eH\u0003\u001a,\u0010!\u001a\u00020\u00012\u0018\u0010\"\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020%\u0012\u0004\u0012\u00020\r0$0#2\b\b\u0002\u0010\u0018\u001a\u00020\u0019H\u0003\u001a\u00b6\u0001\u0010&\u001a\u00020\u00012\u0006\u0010\'\u001a\u00020\u001c2\u0006\u0010(\u001a\u00020\u00112\u0006\u0010)\u001a\u00020\u00032\f\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00030#2\f\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00030#2\u0006\u0010,\u001a\u00020\u001e2\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u00010.2\u0012\u0010/\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00010.2\u0012\u00100\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010.2\u0012\u00101\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010.2\u0012\u00102\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010.2\f\u00103\u001a\b\u0012\u0004\u0012\u00020\u00010\u0016H\u0003\u001a\u001e\u00104\u001a\u00020\u00012\u0006\u00105\u001a\u0002062\f\u00107\u001a\b\u0012\u0004\u0012\u00020\u00010\u0016H\u0003\u001a\u0012\u00108\u001a\u00020\u00012\b\b\u0002\u00109\u001a\u00020:H\u0007\u001a*\u0010;\u001a\u00020\u00012\f\u0010<\u001a\b\u0012\u0004\u0012\u0002060#2\u0012\u0010=\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010.H\u0003\u001a\u0010\u0010>\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\u0003H\u0003\u001a\u0015\u0010?\u001a\u00020\b2\u0006\u0010@\u001a\u00020\rH\u0002\u00a2\u0006\u0002\u0010A\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006B"}, d2 = {"AnalyticItem", "", "label", "", "value", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "color", "Landroidx/compose/ui/graphics/Color;", "AnalyticItem-g2O1Hgs", "(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/graphics/vector/ImageVector;J)V", "AnalyticsOverview", "weeklyAverage", "", "monthlyAverage", "trend", "streakDays", "", "totalEntries", "ErrorCard", "message", "onDismiss", "Lkotlin/Function0;", "LoadingState", "modifier", "Landroidx/compose/ui/Modifier;", "MoodButton", "mood", "Lcom/menteencalma/app/domain/model/MoodEntry$MoodType;", "isSelected", "", "onClick", "enabled", "MoodChart", "chartData", "", "Lkotlin/Pair;", "", "MoodEntryForm", "selectedMood", "intensity", "description", "selectedTags", "selectedActivities", "isSaving", "onMoodChange", "Lkotlin/Function1;", "onIntensityChange", "onDescriptionChange", "onTagToggle", "onActivityToggle", "onSave", "MoodEntryItem", "entry", "Lcom/menteencalma/app/domain/model/MoodEntry;", "onDelete", "MoodTrackerScreen", "moodTrackerViewModel", "Lcom/menteencalma/app/presentation/viewmodels/MoodTrackerViewModel;", "RecentEntriesSection", "entries", "onDeleteEntry", "SuccessCard", "getMoodColor", "average", "(F)J", "app_debug"})
public final class MoodTrackerScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void MoodTrackerScreen(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel moodTrackerViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void LoadingState(androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AnalyticsOverview(float weeklyAverage, float monthlyAverage, java.lang.String trend, int streakDays, int totalEntries) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MoodEntryForm(com.menteencalma.app.domain.model.MoodEntry.MoodType selectedMood, int intensity, java.lang.String description, java.util.List<java.lang.String> selectedTags, java.util.List<java.lang.String> selectedActivities, boolean isSaving, kotlin.jvm.functions.Function1<? super com.menteencalma.app.domain.model.MoodEntry.MoodType, kotlin.Unit> onMoodChange, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onIntensityChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onDescriptionChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTagToggle, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onActivityToggle, kotlin.jvm.functions.Function0<kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MoodButton(com.menteencalma.app.domain.model.MoodEntry.MoodType mood, boolean isSelected, kotlin.jvm.functions.Function0<kotlin.Unit> onClick, boolean enabled) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MoodChart(java.util.List<kotlin.Pair<java.lang.Long, java.lang.Float>> chartData, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void RecentEntriesSection(java.util.List<com.menteencalma.app.domain.model.MoodEntry> entries, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onDeleteEntry) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void MoodEntryItem(com.menteencalma.app.domain.model.MoodEntry entry, kotlin.jvm.functions.Function0<kotlin.Unit> onDelete) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ErrorCard(java.lang.String message, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void SuccessCard(java.lang.String message) {
    }
    
    private static final long getMoodColor(float average) {
        return 0L;
    }
}