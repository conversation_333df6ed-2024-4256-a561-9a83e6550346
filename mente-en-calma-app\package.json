{"name": "mente-en-calma-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "expo": "~53.0.11", "expo-auth-session": "^6.2.0", "expo-crypto": "^14.1.5", "expo-in-app-purchases": "^14.5.0", "expo-status-bar": "~2.2.3", "firebase": "^11.9.1", "react": "19.0.0", "react-native": "0.79.3", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-dom": "19.0.0", "react-native-web": "^0.20.0", "@expo/metro-runtime": "~5.0.4"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}