package com.menteencalma.app.presentation.screens.recommendations

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.menteencalma.app.data.service.CloudFunctionsService
import com.menteencalma.app.domain.model.Recommendation
import com.menteencalma.app.domain.model.User
import com.menteencalma.app.domain.repository.DatabaseRepository
import com.menteencalma.app.domain.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.UUID
import javax.inject.Inject

data class RecommendationsUiState(
    val currentUser: User? = null,
    val currentRecommendation: Recommendation? = null,
    val isLoading: Boolean = false,
    val isPersonalized: Boolean = false,
    val errorMessage: String? = null,
    val hasReachedLimit: Boolean = false,
    val recommendationHistory: List<Recommendation> = emptyList()
)

@HiltViewModel
class RecommendationsViewModel @Inject constructor(
    private val cloudFunctionsService: CloudFunctionsService,
    private val databaseRepository: DatabaseRepository,
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(RecommendationsUiState())
    val uiState: StateFlow<RecommendationsUiState> = _uiState.asStateFlow()

    init {
        loadCurrentUser()
        loadRecommendationHistory()
    }

    private fun loadCurrentUser() {
        viewModelScope.launch {
            try {
                val firebaseUser = authRepository.getCurrentUser()
                if (firebaseUser != null) {
                    val userResult = databaseRepository.getUser(firebaseUser.uid)
                    if (userResult.isSuccess) {
                        _uiState.value = _uiState.value.copy(currentUser = userResult.getOrNull())
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error loading user: ${e.message}"
                )
            }
        }
    }

    private fun loadRecommendationHistory() {
        viewModelScope.launch {
            try {
                val firebaseUser = authRepository.getCurrentUser()
                if (firebaseUser != null) {
                    // En una implementación real, cargarías el historial desde Firestore
                    // Por ahora, dejamos la lista vacía
                    _uiState.value = _uiState.value.copy(recommendationHistory = emptyList())
                }
            } catch (e: Exception) {
                // Log error but don't show to user for history loading
                println("Error loading recommendation history: ${e.message}")
            }
        }
    }

    fun getRecommendation(
        currentMood: String? = null,
        preferredCategory: String? = null
    ) {
        val currentUser = _uiState.value.currentUser ?: return

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                errorMessage = null,
                hasReachedLimit = false
            )

            try {
                val result = cloudFunctionsService.getPersonalizedRecommendation(
                    userId = currentUser.id,
                    currentMood = currentMood,
                    preferredCategory = preferredCategory
                )

                if (result.isSuccess) {
                    val response = result.getOrNull()!!

                    val recommendation = Recommendation(
                        id = UUID.randomUUID().toString(),
                        userId = currentUser.id,
                        title = response.title,
                        content = response.content,
                        isPersonalized = response.isPersonalized,
                        category = response.category,
                        difficulty = response.difficulty,
                        estimatedDuration = response.estimatedDuration,
                        tags = response.tags,
                        therapistId = response.therapistId,
                        createdAt = System.currentTimeMillis(),
                        metadata = Recommendation.RecommendationMetadata(
                            userMood = currentMood,
                            userGoals = emptyList(), // TODO: Obtener de user preferences
                            previousRecommendations = _uiState.value.recommendationHistory.map { it.id },
                            generationModel = "gpt-4",
                            confidence = 0.9f,
                            customData = response.metadata.mapValues { it.value.toString() }
                        )
                    )

                    // Guardar en historial local
                    val updatedHistory = _uiState.value.recommendationHistory.toMutableList()
                    updatedHistory.add(0, recommendation) // Añadir al principio
                    if (updatedHistory.size > 10) {
                        updatedHistory.removeAt(updatedHistory.size - 1) // Mantener solo las últimas 10
                    }

                    _uiState.value = _uiState.value.copy(
                        currentRecommendation = recommendation,
                        isPersonalized = response.isPersonalized,
                        isLoading = false,
                        recommendationHistory = updatedHistory
                    )

                    // Opcional: Guardar en Firestore para persistencia
                    saveRecommendationToFirestore(recommendation)
                } else {
                    val exception = result.exceptionOrNull()
                    if (exception is com.menteencalma.app.data.service.CloudFunctionException) {
                        when (exception.code) {
                            CloudFunctionsService.USAGE_LIMIT_REACHED -> {
                                _uiState.value = _uiState.value.copy(
                                    hasReachedLimit = true,
                                    isLoading = false
                                )
                            }
                            else -> {
                                _uiState.value = _uiState.value.copy(
                                    errorMessage = "Error: ${exception.message}",
                                    isLoading = false
                                )
                            }
                        }
                    } else {
                        _uiState.value = _uiState.value.copy(
                            errorMessage = "Error getting recommendation: ${exception?.message}",
                            isLoading = false
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Unexpected error: ${e.message}",
                    isLoading = false
                )
            }
        }
    }

    private suspend fun saveRecommendationToFirestore(recommendation: Recommendation) {
        try {
            // En una implementación real, guardarías en una subcolección de recomendaciones
            // Por ahora, solo logueamos
            println("Saving recommendation to Firestore: ${recommendation.title}")
        } catch (e: Exception) {
            println("Error saving recommendation: ${e.message}")
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun clearRecommendation() {
        _uiState.value = _uiState.value.copy(
            currentRecommendation = null,
            isPersonalized = false,
            hasReachedLimit = false
        )
    }

    fun isUserPremium(): Boolean {
        return _uiState.value.currentUser?.hasActiveSubscription() ?: false
    }

    fun getRecommendationsByCategory(category: String): List<Recommendation> {
        return _uiState.value.recommendationHistory.filter { it.category == category }
    }

    fun getFavoriteRecommendations(): List<Recommendation> {
        // En una implementación real, tendrías un campo "isFavorite" en Recommendation
        // Por ahora, retornamos las más recientes
        return _uiState.value.recommendationHistory.take(3)
    }
}
