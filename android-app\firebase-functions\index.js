const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

// Initialize Firestore
const db = admin.firestore();

/**
 * Cloud Function: Create User Profile
 * Called when user completes profile creation
 */
exports.createUserProfile = functions.https.onCall(async (data, context) => {
  // Verify authentication
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { displayName, age, therapistGender } = data;
  const userId = context.auth.uid;

  try {
    // Validate input
    if (!displayName || !therapistGender) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
    }

    // Create user profile
    const userProfile = {
      id: userId,
      email: context.auth.token.email,
      displayName: displayName.trim(),
      age: age || null,
      therapistGender: therapistGender,
      subscriptionStatus: 'free',
      subscriptionPlatform: null,
      subscriptionExpiresAt: null,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      
      // Usage limits for free tier
      dailyLimits: {
        chatMessages: 10,
        recommendations: 3,
        articles: 2,
        lastResetDate: new Date().toISOString().split('T')[0]
      },
      
      // Usage counters
      usageToday: {
        chatMessages: 0,
        recommendations: 0,
        articles: 0
      }
    };

    // Save to Firestore
    await db.collection('users').doc(userId).set(userProfile);

    // Generate personalized greeting
    const therapistName = therapistGender === 'aurora' ? 'Aurora' : 'Alejandro';
    const greeting = `¡Hola ${displayName}! Soy ${therapistName}, tu terapeuta de IA. Estoy aquí para acompañarte en tu camino hacia el bienestar mental. ¿En qué puedo ayudarte hoy?`;

    return {
      success: true,
      message: 'Profile created successfully',
      personalizedGreeting: greeting,
      therapistName: therapistName
    };

  } catch (error) {
    console.error('Error creating user profile:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create user profile');
  }
});

/**
 * Cloud Function: AI Chatbot Support
 * Handles chat messages with AI integration
 */
exports.aiChatbotSupport = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { message, conversationHistory } = data;
  const userId = context.auth.uid;

  try {
    // Get user profile
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User profile not found');
    }

    const userData = userDoc.data();
    
    // Check usage limits
    const today = new Date().toISOString().split('T')[0];
    if (userData.dailyLimits.lastResetDate !== today) {
      // Reset daily counters
      await db.collection('users').doc(userId).update({
        'usageToday.chatMessages': 0,
        'usageToday.recommendations': 0,
        'usageToday.articles': 0,
        'dailyLimits.lastResetDate': today
      });
      userData.usageToday.chatMessages = 0;
    }

    // Check if user has reached limit
    if (userData.subscriptionStatus === 'free' && userData.usageToday.chatMessages >= userData.dailyLimits.chatMessages) {
      return {
        success: false,
        error: 'USAGE_LIMIT_REACHED',
        message: 'Has alcanzado el límite diario de mensajes. Suscríbete para continuar.',
        remainingMessages: 0
      };
    }

    // Simulate AI response (replace with actual Gemini AI integration)
    const aiResponse = await generateAIResponse(message, userData, conversationHistory);

    // Increment usage counter
    await db.collection('users').doc(userId).update({
      'usageToday.chatMessages': admin.firestore.FieldValue.increment(1)
    });

    const remainingMessages = userData.subscriptionStatus === 'free' 
      ? Math.max(0, userData.dailyLimits.chatMessages - userData.usageToday.chatMessages - 1)
      : -1; // Unlimited for premium

    return {
      success: true,
      response: aiResponse,
      remainingMessages: remainingMessages,
      therapistName: userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro'
    };

  } catch (error) {
    console.error('Error in AI chatbot:', error);
    throw new functions.https.HttpsError('internal', 'Failed to process chat message');
  }
});

/**
 * Cloud Function: Personalized Recommendations
 * Generates personalized recommendations based on user profile
 */
exports.personalizedRecommendation = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const userId = context.auth.uid;

  try {
    // Get user profile
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User profile not found');
    }

    const userData = userDoc.data();

    // Check usage limits
    const today = new Date().toISOString().split('T')[0];
    if (userData.dailyLimits.lastResetDate !== today) {
      await db.collection('users').doc(userId).update({
        'usageToday.recommendations': 0,
        'dailyLimits.lastResetDate': today
      });
      userData.usageToday.recommendations = 0;
    }

    if (userData.subscriptionStatus === 'free' && userData.usageToday.recommendations >= userData.dailyLimits.recommendations) {
      return {
        success: false,
        error: 'USAGE_LIMIT_REACHED',
        message: 'Has alcanzado el límite diario de recomendaciones. Suscríbete para continuar.'
      };
    }

    // Generate personalized recommendations
    const recommendations = await generatePersonalizedRecommendations(userData);

    // Increment usage counter
    await db.collection('users').doc(userId).update({
      'usageToday.recommendations': admin.firestore.FieldValue.increment(1)
    });

    return {
      success: true,
      recommendations: recommendations,
      isPersonalized: true,
      remainingRecommendations: userData.subscriptionStatus === 'free' 
        ? Math.max(0, userData.dailyLimits.recommendations - userData.usageToday.recommendations - 1)
        : -1
    };

  } catch (error) {
    console.error('Error generating recommendations:', error);
    throw new functions.https.HttpsError('internal', 'Failed to generate recommendations');
  }
});

/**
 * Cloud Function: Generate Article
 * Generates and saves personalized articles
 */
exports.generateArticle = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { topic, category } = data;
  const userId = context.auth.uid;

  try {
    // Get user profile
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User profile not found');
    }

    const userData = userDoc.data();

    // Check usage limits
    const today = new Date().toISOString().split('T')[0];
    if (userData.dailyLimits.lastResetDate !== today) {
      await db.collection('users').doc(userId).update({
        'usageToday.articles': 0,
        'dailyLimits.lastResetDate': today
      });
      userData.usageToday.articles = 0;
    }

    if (userData.subscriptionStatus === 'free' && userData.usageToday.articles >= userData.dailyLimits.articles) {
      return {
        success: false,
        error: 'ARTICLE_LIMIT_REACHED',
        message: 'Has alcanzado el límite diario de artículos. Suscríbete para continuar.'
      };
    }

    // Generate article content
    const article = await generateArticleContent(topic, category, userData);

    // Save article to user's collection
    const articleRef = db.collection('users').doc(userId).collection('savedArticles').doc();
    await articleRef.set({
      ...article,
      id: articleRef.id,
      userId: userId,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Increment usage counter
    await db.collection('users').doc(userId).update({
      'usageToday.articles': admin.firestore.FieldValue.increment(1)
    });

    return {
      success: true,
      article: {
        ...article,
        id: articleRef.id
      },
      remainingArticles: userData.subscriptionStatus === 'free' 
        ? Math.max(0, userData.dailyLimits.articles - userData.usageToday.articles - 1)
        : -1
    };

  } catch (error) {
    console.error('Error generating article:', error);
    throw new functions.https.HttpsError('internal', 'Failed to generate article');
  }
});

// Helper Functions

async function generateAIResponse(message, userData, conversationHistory) {
  // TODO: Integrate with Gemini AI
  // For now, return a simulated response
  
  const therapistName = userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro';
  const responses = [
    `Entiendo cómo te sientes, ${userData.displayName}. Es completamente normal experimentar estas emociones.`,
    `Gracias por compartir eso conmigo. ¿Podrías contarme más sobre lo que te está preocupando?`,
    `Me parece que estás pasando por un momento difícil. Recuerda que estoy aquí para apoyarte.`,
    `Esa es una perspectiva muy válida. ¿Has considerado practicar algunas técnicas de respiración?`,
    `Es importante que reconozcas tus sentimientos. ¿Qué estrategias has usado antes que te han ayudado?`
  ];
  
  return responses[Math.floor(Math.random() * responses.length)];
}

async function generatePersonalizedRecommendations(userData) {
  // TODO: Implement AI-based recommendation generation
  // For now, return sample recommendations
  
  const baseRecommendations = [
    {
      id: '1',
      title: 'Ejercicio de Respiración Profunda',
      description: 'Técnica de 4-7-8 para reducir la ansiedad',
      category: 'Respiración',
      duration: '5 minutos',
      difficulty: 'Fácil',
      icon: '🫁'
    },
    {
      id: '2',
      title: 'Meditación de Atención Plena',
      description: 'Práctica guiada para el momento presente',
      category: 'Mindfulness',
      duration: '10 minutos',
      difficulty: 'Intermedio',
      icon: '🧘‍♀️'
    },
    {
      id: '3',
      title: 'Diario de Gratitud',
      description: 'Escribe 3 cosas por las que te sientes agradecido',
      category: 'Gratitud',
      duration: '5 minutos',
      difficulty: 'Fácil',
      icon: '📝'
    }
  ];
  
  return baseRecommendations;
}

async function generateArticleContent(topic, category, userData) {
  // TODO: Integrate with Gemini AI for article generation
  // For now, return a sample article
  
  return {
    title: `Entendiendo ${topic}: Una Guía Personal`,
    content: `# Entendiendo ${topic}

Hola ${userData.displayName}, este artículo ha sido creado especialmente para ti.

## Introducción

${topic} es un tema importante en el bienestar mental que merece nuestra atención y comprensión.

## Puntos Clave

1. **Reconocimiento**: El primer paso es reconocer y validar tus sentimientos.
2. **Comprensión**: Entender las causas y patrones detrás de tus experiencias.
3. **Acción**: Implementar estrategias prácticas para el manejo y mejora.

## Estrategias Recomendadas

- Práctica de mindfulness diaria
- Ejercicios de respiración
- Journaling reflexivo
- Actividad física regular

## Conclusión

Recuerda que el crecimiento personal es un proceso gradual. Sé paciente contigo mismo.

---
*Artículo generado por tu terapeuta IA ${userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro'}*`,
    category: category,
    topic: topic,
    readingTime: '5 min',
    tags: [topic, category, 'bienestar', 'salud mental']
  };
}
