const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

// Initialize Firestore
const db = admin.firestore();

/**
 * Cloud Function: Create User Profile
 * Called when user completes profile creation
 */
exports.createUserProfile = functions.https.onCall(async (data, context) => {
  // Verify authentication
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { displayName, age, therapistGender } = data;
  const userId = context.auth.uid;

  try {
    // Validate input
    if (!displayName || !therapistGender) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
    }

    // Create user profile
    const userProfile = {
      id: userId,
      email: context.auth.token.email,
      displayName: displayName.trim(),
      age: age || null,
      therapistGender: therapistGender,
      subscriptionStatus: 'free',
      subscriptionPlatform: null,
      subscriptionExpiresAt: null,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      
      // Usage limits for free tier
      dailyLimits: {
        chatMessages: 5,           // 5 mensajes de chat al día
        recommendations: 3,        // 3 recomendaciones generales al día
        lastResetDate: new Date().toISOString().split('T')[0]
      },

      // Monthly limits for free tier
      monthlyLimits: {
        articles: 5,               // 5 artículos al mes
        lastResetMonth: new Date().toISOString().substring(0, 7) // YYYY-MM format
      },

      // Usage counters
      usageToday: {
        chatMessages: 0,
        recommendations: 0
      },

      // Monthly usage counters
      usageThisMonth: {
        articles: 0
      }
    };

    // Save to Firestore
    await db.collection('users').doc(userId).set(userProfile);

    // Generate personalized greeting
    const therapistName = therapistGender === 'aurora' ? 'Aurora' : 'Alejandro';
    const greeting = `¡Hola ${displayName}! Soy ${therapistName}, tu terapeuta de IA. Estoy aquí para acompañarte en tu camino hacia el bienestar mental. ¿En qué puedo ayudarte hoy?`;

    return {
      success: true,
      message: 'Profile created successfully',
      personalizedGreeting: greeting,
      therapistName: therapistName
    };

  } catch (error) {
    console.error('Error creating user profile:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create user profile');
  }
});

/**
 * Cloud Function: AI Chatbot Support
 * Handles chat messages with AI integration
 */
exports.aiChatbotSupport = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { message, conversationHistory } = data;
  const userId = context.auth.uid;

  try {
    // Get user profile
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User profile not found');
    }

    const userData = userDoc.data();
    
    // Check usage limits
    const today = new Date().toISOString().split('T')[0];
    if (userData.dailyLimits.lastResetDate !== today) {
      // Reset daily counters
      await db.collection('users').doc(userId).update({
        'usageToday.chatMessages': 0,
        'usageToday.recommendations': 0,
        'dailyLimits.lastResetDate': today
      });
      userData.usageToday.chatMessages = 0;
    }

    // Check if user has reached chat limit (5 messages for free users)
    if (userData.subscriptionStatus === 'free' && userData.usageToday.chatMessages >= userData.dailyLimits.chatMessages) {
      return {
        success: false,
        error: 'USAGE_LIMIT_REACHED',
        message: 'Has alcanzado el límite diario de 5 mensajes de chat. Suscríbete para acceso ilimitado.',
        remainingMessages: 0,
        limitType: 'chat_daily',
        upgradeMessage: 'Con la suscripción Premium tendrás chat ilimitado con tu terapeuta IA.'
      };
    }

    // Simulate AI response (replace with actual Gemini AI integration)
    const aiResponse = await generateAIResponse(message, userData, conversationHistory);

    // Increment usage counter
    await db.collection('users').doc(userId).update({
      'usageToday.chatMessages': admin.firestore.FieldValue.increment(1)
    });

    const remainingMessages = userData.subscriptionStatus === 'free' 
      ? Math.max(0, userData.dailyLimits.chatMessages - userData.usageToday.chatMessages - 1)
      : -1; // Unlimited for premium

    return {
      success: true,
      response: aiResponse,
      remainingMessages: remainingMessages,
      therapistName: userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro'
    };

  } catch (error) {
    console.error('Error in AI chatbot:', error);
    throw new functions.https.HttpsError('internal', 'Failed to process chat message');
  }
});

/**
 * Cloud Function: Personalized Recommendations
 * Generates personalized recommendations based on user profile
 */
exports.personalizedRecommendation = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const userId = context.auth.uid;

  try {
    // Get user profile
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User profile not found');
    }

    const userData = userDoc.data();

    // Check usage limits
    const today = new Date().toISOString().split('T')[0];
    if (userData.dailyLimits.lastResetDate !== today) {
      await db.collection('users').doc(userId).update({
        'usageToday.recommendations': 0,
        'dailyLimits.lastResetDate': today
      });
      userData.usageToday.recommendations = 0;
    }

    if (userData.subscriptionStatus === 'free' && userData.usageToday.recommendations >= userData.dailyLimits.recommendations) {
      return {
        success: false,
        error: 'USAGE_LIMIT_REACHED',
        message: 'Has alcanzado el límite diario de 3 recomendaciones. Suscríbete para recomendaciones personalizadas ilimitadas.',
        limitType: 'recommendations_daily',
        upgradeMessage: 'Con Premium obtienes recomendaciones personalizadas ilimitadas basadas en tu perfil y progreso.'
      };
    }

    // Generate recommendations (general for free users, personalized for premium)
    const isPersonalized = userData.subscriptionStatus !== 'free';
    const recommendations = await generateRecommendations(userData, isPersonalized);

    // Increment usage counter
    await db.collection('users').doc(userId).update({
      'usageToday.recommendations': admin.firestore.FieldValue.increment(1)
    });

    return {
      success: true,
      recommendations: recommendations,
      isPersonalized: isPersonalized,
      recommendationType: isPersonalized ? 'personalized' : 'general',
      remainingRecommendations: userData.subscriptionStatus === 'free'
        ? Math.max(0, userData.dailyLimits.recommendations - userData.usageToday.recommendations - 1)
        : -1
    };

  } catch (error) {
    console.error('Error generating recommendations:', error);
    throw new functions.https.HttpsError('internal', 'Failed to generate recommendations');
  }
});

/**
 * Cloud Function: Generate Article
 * Generates and saves personalized articles
 */
exports.generateArticle = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { topic, category } = data;
  const userId = context.auth.uid;

  try {
    // Get user profile
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User profile not found');
    }

    const userData = userDoc.data();

    // Check monthly usage limits for articles
    const currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM format
    if (userData.monthlyLimits.lastResetMonth !== currentMonth) {
      // Reset monthly counters
      await db.collection('users').doc(userId).update({
        'usageThisMonth.articles': 0,
        'monthlyLimits.lastResetMonth': currentMonth
      });
      userData.usageThisMonth.articles = 0;
    }

    // Check if user has reached monthly article limit (5 articles for free users)
    if (userData.subscriptionStatus === 'free' && userData.usageThisMonth.articles >= userData.monthlyLimits.articles) {
      return {
        success: false,
        error: 'ARTICLE_LIMIT_REACHED',
        message: 'Has alcanzado el límite mensual de 5 artículos. Suscríbete para generar artículos ilimitados.',
        limitType: 'articles_monthly',
        remainingArticles: 0,
        upgradeMessage: 'Con Premium puedes generar artículos personalizados ilimitados sobre cualquier tema de bienestar mental.'
      };
    }

    // Generate article content
    const article = await generateArticleContent(topic, category, userData);

    // Save article to user's collection
    const articleRef = db.collection('users').doc(userId).collection('savedArticles').doc();
    await articleRef.set({
      ...article,
      id: articleRef.id,
      userId: userId,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Increment monthly usage counter
    await db.collection('users').doc(userId).update({
      'usageThisMonth.articles': admin.firestore.FieldValue.increment(1)
    });

    return {
      success: true,
      article: {
        ...article,
        id: articleRef.id
      },
      remainingArticles: userData.subscriptionStatus === 'free'
        ? Math.max(0, userData.monthlyLimits.articles - userData.usageThisMonth.articles - 1)
        : -1,
      limitType: 'monthly',
      resetDate: userData.subscriptionStatus === 'free'
        ? new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1).toISOString()
        : null
    };

  } catch (error) {
    console.error('Error generating article:', error);
    throw new functions.https.HttpsError('internal', 'Failed to generate article');
  }
});

// Helper Functions

async function generateAIResponse(message, userData, conversationHistory) {
  // TODO: Integrate with Gemini AI
  // For now, return a simulated response
  
  const therapistName = userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro';
  const responses = [
    `Entiendo cómo te sientes, ${userData.displayName}. Es completamente normal experimentar estas emociones.`,
    `Gracias por compartir eso conmigo. ¿Podrías contarme más sobre lo que te está preocupando?`,
    `Me parece que estás pasando por un momento difícil. Recuerda que estoy aquí para apoyarte.`,
    `Esa es una perspectiva muy válida. ¿Has considerado practicar algunas técnicas de respiración?`,
    `Es importante que reconozcas tus sentimientos. ¿Qué estrategias has usado antes que te han ayudado?`
  ];
  
  return responses[Math.floor(Math.random() * responses.length)];
}

async function generateRecommendations(userData, isPersonalized) {
  // TODO: Implement AI-based recommendation generation
  // For now, return sample recommendations

  if (isPersonalized) {
    // Premium users get personalized recommendations based on their profile
    const personalizedRecommendations = [
      {
        id: '1',
        title: `Ejercicio Personalizado para ${userData.displayName}`,
        description: `Técnica de respiración adaptada a tu perfil con ${userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro'}`,
        category: 'Respiración Personalizada',
        duration: '7 minutos',
        difficulty: 'Adaptado a ti',
        icon: '🫁',
        personalized: true,
        therapistNote: `Recomendado especialmente por ${userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro'} para tu situación actual.`
      },
      {
        id: '2',
        title: 'Meditación Guiada Personalizada',
        description: 'Sesión de mindfulness adaptada a tu edad y preferencias',
        category: 'Mindfulness Personalizado',
        duration: '12 minutos',
        difficulty: 'Personalizado',
        icon: '🧘‍♀️',
        personalized: true,
        ageAdapted: userData.age ? `Adaptado para ${userData.age} años` : 'Adaptado a tu perfil'
      },
      {
        id: '3',
        title: 'Diario de Progreso Personal',
        description: 'Reflexión guiada basada en tu historial de ánimo',
        category: 'Autoconocimiento',
        duration: '8 minutos',
        difficulty: 'Personalizado',
        icon: '📝',
        personalized: true
      }
    ];

    return personalizedRecommendations;
  } else {
    // Free users get general recommendations
    const generalRecommendations = [
      {
        id: '1',
        title: 'Respiración 4-7-8',
        description: 'Técnica básica para reducir la ansiedad',
        category: 'Respiración',
        duration: '5 minutos',
        difficulty: 'Fácil',
        icon: '🫁',
        personalized: false
      },
      {
        id: '2',
        title: 'Meditación Básica',
        description: 'Introducción al mindfulness',
        category: 'Mindfulness',
        duration: '10 minutos',
        difficulty: 'Principiante',
        icon: '🧘‍♀️',
        personalized: false
      },
      {
        id: '3',
        title: 'Lista de Gratitud',
        description: 'Escribe 3 cosas positivas del día',
        category: 'Gratitud',
        duration: '5 minutos',
        difficulty: 'Fácil',
        icon: '📝',
        personalized: false
      }
    ];

    return generalRecommendations;
  }
}

async function generateArticleContent(topic, category, userData) {
  // TODO: Integrate with Gemini AI for article generation
  // For now, return a sample article
  
  return {
    title: `Entendiendo ${topic}: Una Guía Personal`,
    content: `# Entendiendo ${topic}

Hola ${userData.displayName}, este artículo ha sido creado especialmente para ti.

## Introducción

${topic} es un tema importante en el bienestar mental que merece nuestra atención y comprensión.

## Puntos Clave

1. **Reconocimiento**: El primer paso es reconocer y validar tus sentimientos.
2. **Comprensión**: Entender las causas y patrones detrás de tus experiencias.
3. **Acción**: Implementar estrategias prácticas para el manejo y mejora.

## Estrategias Recomendadas

- Práctica de mindfulness diaria
- Ejercicios de respiración
- Journaling reflexivo
- Actividad física regular

## Conclusión

Recuerda que el crecimiento personal es un proceso gradual. Sé paciente contigo mismo.

---
*Artículo generado por tu terapeuta IA ${userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro'}*`,
    category: category,
    topic: topic,
    readingTime: '5 min',
    tags: [topic, category, 'bienestar', 'salud mental']
  };
}
