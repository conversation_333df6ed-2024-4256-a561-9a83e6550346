const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

// Initialize Firestore
const db = admin.firestore();

/**
 * Cloud Function: Create User Profile
 * Called when user completes profile creation
 */
exports.createUserProfile = functions.https.onCall(async (data, context) => {
  // Verify authentication
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { displayName, age, therapistGender } = data;
  const userId = context.auth.uid;

  try {
    // Validate input
    if (!displayName || !therapistGender) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
    }

    // Create user profile
    const userProfile = {
      id: userId,
      email: context.auth.token.email,
      displayName: displayName.trim(),
      age: age || null,
      therapistGender: therapistGender,
      subscriptionStatus: 'free',
      subscriptionPlatform: null,
      subscriptionExpiresAt: null,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      
      // Usage limits for free tier
      dailyLimits: {
        chatMessages: 5,           // 5 mensajes de chat al día
        recommendations: 3,        // 3 recomendaciones generales al día
        lastResetDate: new Date().toISOString().split('T')[0]
      },

      // Monthly limits for free tier
      monthlyLimits: {
        articles: 5,               // 5 artículos al mes
        lastResetMonth: new Date().toISOString().substring(0, 7) // YYYY-MM format
      },

      // Usage counters
      usageToday: {
        chatMessages: 0,
        recommendations: 0
      },

      // Monthly usage counters
      usageThisMonth: {
        articles: 0
      }
    };

    // Save to Firestore
    await db.collection('users').doc(userId).set(userProfile);

    // Generate personalized greeting
    const therapistName = therapistGender === 'aurora' ? 'Aurora' : 'Alejandro';
    const greeting = `¡Hola ${displayName}! Soy ${therapistName}, tu terapeuta de IA. Estoy aquí para acompañarte en tu camino hacia el bienestar mental. ¿En qué puedo ayudarte hoy?`;

    return {
      success: true,
      message: 'Profile created successfully',
      personalizedGreeting: greeting,
      therapistName: therapistName
    };

  } catch (error) {
    console.error('Error creating user profile:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create user profile');
  }
});

/**
 * Cloud Function: AI Chatbot Support
 * Handles chat messages with AI integration
 */
exports.aiChatbotSupport = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { message, conversationHistory } = data;
  const userId = context.auth.uid;

  try {
    // Get user profile
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User profile not found');
    }

    const userData = userDoc.data();
    
    // Check usage limits
    const today = new Date().toISOString().split('T')[0];
    if (userData.dailyLimits.lastResetDate !== today) {
      // Reset daily counters
      await db.collection('users').doc(userId).update({
        'usageToday.chatMessages': 0,
        'usageToday.recommendations': 0,
        'dailyLimits.lastResetDate': today
      });
      userData.usageToday.chatMessages = 0;
    }

    // Check if user has reached chat limit (5 messages for free users)
    if (userData.subscriptionStatus === 'free' && userData.usageToday.chatMessages >= userData.dailyLimits.chatMessages) {
      return {
        success: false,
        error: 'USAGE_LIMIT_REACHED',
        message: 'Has alcanzado el límite diario de 5 mensajes de chat. Suscríbete para acceso ilimitado.',
        remainingMessages: 0,
        limitType: 'chat_daily',
        upgradeMessage: 'Con la suscripción Premium tendrás chat ilimitado con tu terapeuta IA.'
      };
    }

    // Simulate AI response (replace with actual Gemini AI integration)
    const aiResponse = await generateAIResponse(message, userData, conversationHistory);

    // Increment usage counter
    await db.collection('users').doc(userId).update({
      'usageToday.chatMessages': admin.firestore.FieldValue.increment(1)
    });

    const remainingMessages = userData.subscriptionStatus === 'free' 
      ? Math.max(0, userData.dailyLimits.chatMessages - userData.usageToday.chatMessages - 1)
      : -1; // Unlimited for premium

    return {
      success: true,
      response: aiResponse,
      remainingMessages: remainingMessages,
      therapistName: userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro'
    };

  } catch (error) {
    console.error('Error in AI chatbot:', error);
    throw new functions.https.HttpsError('internal', 'Failed to process chat message');
  }
});

/**
 * Cloud Function: Personalized Recommendations
 * Generates personalized recommendations based on user profile
 */
exports.personalizedRecommendation = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const userId = context.auth.uid;

  try {
    // Get user profile
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User profile not found');
    }

    const userData = userDoc.data();

    // Check usage limits
    const today = new Date().toISOString().split('T')[0];
    if (userData.dailyLimits.lastResetDate !== today) {
      await db.collection('users').doc(userId).update({
        'usageToday.recommendations': 0,
        'dailyLimits.lastResetDate': today
      });
      userData.usageToday.recommendations = 0;
    }

    if (userData.subscriptionStatus === 'free' && userData.usageToday.recommendations >= userData.dailyLimits.recommendations) {
      return {
        success: false,
        error: 'USAGE_LIMIT_REACHED',
        message: 'Has alcanzado el límite diario de 3 recomendaciones. Suscríbete para recomendaciones personalizadas ilimitadas.',
        limitType: 'recommendations_daily',
        upgradeMessage: 'Con Premium obtienes recomendaciones personalizadas ilimitadas basadas en tu perfil y progreso.'
      };
    }

    // Generate recommendations (general for free users, personalized for premium)
    const isPersonalized = userData.subscriptionStatus !== 'free';
    const recommendations = await generateRecommendations(userData, isPersonalized);

    // Increment usage counter
    await db.collection('users').doc(userId).update({
      'usageToday.recommendations': admin.firestore.FieldValue.increment(1)
    });

    return {
      success: true,
      recommendations: recommendations,
      isPersonalized: isPersonalized,
      recommendationType: isPersonalized ? 'personalized' : 'general',
      remainingRecommendations: userData.subscriptionStatus === 'free'
        ? Math.max(0, userData.dailyLimits.recommendations - userData.usageToday.recommendations - 1)
        : -1
    };

  } catch (error) {
    console.error('Error generating recommendations:', error);
    throw new functions.https.HttpsError('internal', 'Failed to generate recommendations');
  }
});

/**
 * Cloud Function: Generate Article
 * Generates and saves personalized articles
 */
exports.generateArticle = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { topic, category } = data;
  const userId = context.auth.uid;

  try {
    // Get user profile
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User profile not found');
    }

    const userData = userDoc.data();

    // Check monthly usage limits for articles
    const currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM format
    if (userData.monthlyLimits.lastResetMonth !== currentMonth) {
      // Reset monthly counters
      await db.collection('users').doc(userId).update({
        'usageThisMonth.articles': 0,
        'monthlyLimits.lastResetMonth': currentMonth
      });
      userData.usageThisMonth.articles = 0;
    }

    // Check if user has reached monthly article limit (5 articles for free users)
    if (userData.subscriptionStatus === 'free' && userData.usageThisMonth.articles >= userData.monthlyLimits.articles) {
      return {
        success: false,
        error: 'ARTICLE_LIMIT_REACHED',
        message: 'Has alcanzado el límite mensual de 5 artículos. Suscríbete para generar artículos ilimitados.',
        limitType: 'articles_monthly',
        remainingArticles: 0,
        upgradeMessage: 'Con Premium puedes generar artículos personalizados ilimitados sobre cualquier tema de bienestar mental.'
      };
    }

    // Generate article content
    const article = await generateArticleContent(topic, category, userData);

    // Save article to user's collection
    const articleRef = db.collection('users').doc(userId).collection('savedArticles').doc();
    await articleRef.set({
      ...article,
      id: articleRef.id,
      userId: userId,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Increment monthly usage counter
    await db.collection('users').doc(userId).update({
      'usageThisMonth.articles': admin.firestore.FieldValue.increment(1)
    });

    return {
      success: true,
      article: {
        ...article,
        id: articleRef.id
      },
      remainingArticles: userData.subscriptionStatus === 'free'
        ? Math.max(0, userData.monthlyLimits.articles - userData.usageThisMonth.articles - 1)
        : -1,
      limitType: 'monthly',
      resetDate: userData.subscriptionStatus === 'free'
        ? new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1).toISOString()
        : null
    };

  } catch (error) {
    console.error('Error generating article:', error);
    throw new functions.https.HttpsError('internal', 'Failed to generate article');
  }
});

// Helper Functions

async function generateAIResponse(message, userData, conversationHistory) {
  // TODO: Integrate with Gemini AI
  // For now, return a simulated response
  
  const therapistName = userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro';
  const responses = [
    `Entiendo cómo te sientes, ${userData.displayName}. Es completamente normal experimentar estas emociones.`,
    `Gracias por compartir eso conmigo. ¿Podrías contarme más sobre lo que te está preocupando?`,
    `Me parece que estás pasando por un momento difícil. Recuerda que estoy aquí para apoyarte.`,
    `Esa es una perspectiva muy válida. ¿Has considerado practicar algunas técnicas de respiración?`,
    `Es importante que reconozcas tus sentimientos. ¿Qué estrategias has usado antes que te han ayudado?`
  ];
  
  return responses[Math.floor(Math.random() * responses.length)];
}

// Helper function to limit text to specific word count
function limitWords(text, maxWords) {
  const words = text.split(' ');
  if (words.length <= maxWords) {
    return text;
  }
  return words.slice(0, maxWords).join(' ') + '...';
}

// Helper function to count words
function countWords(text) {
  return text.split(' ').filter(word => word.length > 0).length;
}

async function generateRecommendations(userData, isPersonalized) {
  // TODO: Implement AI-based recommendation generation
  // For now, return sample recommendations with word limits

  if (isPersonalized) {
    // Premium users get personalized recommendations based on their profile
    const personalizedRecommendations = [
      {
        id: '1',
        title: `Ejercicio Personalizado para ${userData.displayName}`,
        description: limitWords(`Técnica de respiración 4-7-8 adaptada específicamente a tu perfil y necesidades actuales. ${userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro'} ha diseñado esta práctica considerando tu edad, preferencias y historial de ánimo. Inhala por 4 segundos, mantén por 7, exhala por 8. Repite 4 veces cuando sientas ansiedad o estrés. Esta técnica activa tu sistema nervioso parasimpático, reduciendo cortisol y promoviendo calma. Ideal para practicar antes de dormir o en momentos de tensión. Tu progreso se adapta automáticamente según tus respuestas.`, 80),
        category: 'Respiración Personalizada',
        duration: '7 minutos',
        difficulty: 'Adaptado a ti',
        icon: '🫁',
        personalized: true,
        therapistNote: limitWords(`Recomendado especialmente por ${userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro'} para tu situación actual. Esta técnica se ha seleccionado basándose en tu perfil, edad y patrones de uso de la aplicación.`, 80),
        wordCount: countWords(`Técnica de respiración 4-7-8 adaptada específicamente a tu perfil y necesidades actuales. ${userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro'} ha diseñado esta práctica considerando tu edad, preferencias y historial de ánimo.`)
      },
      {
        id: '2',
        title: 'Meditación Guiada Personalizada',
        description: limitWords(`Sesión de mindfulness diseñada específicamente para tu edad y preferencias personales. Esta meditación incorpora elementos de tu historial de ánimo y progreso en la aplicación. Comenzarás con respiración consciente, seguido de un escaneo corporal adaptado a tu nivel de experiencia. La duración y intensidad se ajustan automáticamente según tu perfil. Incluye visualizaciones personalizadas y afirmaciones basadas en tus objetivos de bienestar. Perfecta para desarrollar autoconciencia y reducir ansiedad de manera sostenible y efectiva.`, 80),
        category: 'Mindfulness Personalizado',
        duration: '12 minutos',
        difficulty: 'Personalizado',
        icon: '🧘‍♀️',
        personalized: true,
        ageAdapted: userData.age ? `Adaptado para ${userData.age} años` : 'Adaptado a tu perfil',
        wordCount: countWords(`Sesión de mindfulness diseñada específicamente para tu edad y preferencias personales. Esta meditación incorpora elementos de tu historial de ánimo y progreso en la aplicación.`)
      },
      {
        id: '3',
        title: 'Diario de Progreso Personal',
        description: limitWords(`Reflexión guiada basada en tu historial de ánimo y patrones de comportamiento únicos. Este ejercicio de journaling está diseñado para ayudarte a identificar patrones, celebrar logros y establecer metas realistas. Incluye preguntas personalizadas según tu terapeuta IA preferido y tu progreso actual. Te guiará a través de una reflexión estructurada sobre tus emociones, pensamientos y experiencias recientes. Ideal para desarrollar autoconocimiento y mantener un registro de tu crecimiento personal y bienestar mental.`, 80),
        category: 'Autoconocimiento',
        duration: '8 minutos',
        difficulty: 'Personalizado',
        icon: '📝',
        personalized: true,
        wordCount: countWords(`Reflexión guiada basada en tu historial de ánimo y patrones de comportamiento únicos. Este ejercicio de journaling está diseñado para ayudarte a identificar patrones.`)
      }
    ];

    return personalizedRecommendations;
  } else {
    // Free users get general recommendations (limited to 80 words)
    const generalRecommendations = [
      {
        id: '1',
        title: 'Respiración 4-7-8',
        description: limitWords(`Técnica básica y efectiva para reducir la ansiedad y el estrés de manera inmediata. Inhala por la nariz contando hasta 4, mantén la respiración contando hasta 7, exhala por la boca contando hasta 8. Repite este ciclo 4 veces. Esta práctica activa tu sistema nervioso parasimpático, reduciendo los niveles de cortisol y promoviendo una sensación de calma. Es especialmente útil antes de dormir o en momentos de tensión. No requiere experiencia previa y puede practicarse en cualquier lugar.`, 80),
        category: 'Respiración',
        duration: '5 minutos',
        difficulty: 'Fácil',
        icon: '🫁',
        personalized: false,
        wordCount: countWords(`Técnica básica y efectiva para reducir la ansiedad y el estrés de manera inmediata. Inhala por la nariz contando hasta 4, mantén la respiración contando hasta 7.`)
      },
      {
        id: '2',
        title: 'Meditación Básica',
        description: limitWords(`Introducción al mindfulness y la meditación para principiantes. Siéntate cómodamente, cierra los ojos y enfócate en tu respiración natural. Cuando tu mente divague, gentilmente regresa tu atención a la respiración sin juzgarte. Esta práctica desarrolla la capacidad de observar pensamientos y emociones sin reaccionar automáticamente. Comienza con 5-10 minutos diarios. Los beneficios incluyen reducción del estrés, mejor concentración y mayor autoconciencia. Es la base para desarrollar una práctica de mindfulness más profunda y sostenible.`, 80),
        category: 'Mindfulness',
        duration: '10 minutos',
        difficulty: 'Principiante',
        icon: '🧘‍♀️',
        personalized: false,
        wordCount: countWords(`Introducción al mindfulness y la meditación para principiantes. Siéntate cómodamente, cierra los ojos y enfócate en tu respiración natural.`)
      },
      {
        id: '3',
        title: 'Lista de Gratitud',
        description: limitWords(`Ejercicio simple pero poderoso para mejorar el bienestar mental y la perspectiva positiva. Cada día, escribe tres cosas específicas por las que te sientes agradecido. Pueden ser grandes o pequeñas: desde una conversación agradable hasta un logro personal. Sé específico en tus descripciones y reflexiona sobre por qué cada elemento es significativo. Esta práctica entrena tu cerebro para notar aspectos positivos de la vida, reduciendo pensamientos negativos y mejorando el estado de ánimo general de manera consistente y duradera.`, 80),
        category: 'Gratitud',
        duration: '5 minutos',
        difficulty: 'Fácil',
        icon: '📝',
        personalized: false,
        wordCount: countWords(`Ejercicio simple pero poderoso para mejorar el bienestar mental y la perspectiva positiva. Cada día, escribe tres cosas específicas por las que te sientes agradecido.`)
      }
    ];

    return generalRecommendations;
  }
}

async function generateArticleContent(topic, category, userData) {
  // TODO: Integrate with Gemini AI for article generation
  // For now, return a sample article with word limit (120 words)

  const isPersonalized = userData.subscriptionStatus !== 'free';
  const therapistName = userData.therapistGender === 'aurora' ? 'Aurora' : 'Alejandro';

  // Generate content based on user type
  let articleContent;

  if (isPersonalized) {
    // Premium users get personalized articles
    articleContent = `Hola ${userData.displayName}, este artículo sobre ${topic} ha sido creado específicamente para ti por ${therapistName}.

**Comprensión Personal**: ${topic} es particularmente relevante para tu situación actual. Basándome en tu perfil y progreso, he identificado estrategias específicas que pueden beneficiarte.

**Estrategias Adaptadas**:
• Técnicas de respiración personalizadas para tu nivel de ansiedad
• Ejercicios de mindfulness adaptados a tu edad (${userData.age || 'tu perfil'})
• Journaling reflexivo basado en tus patrones de ánimo

**Plan de Acción**: Implementa estas estrategias gradualmente. Tu progreso se monitoreará automáticamente para ajustar las recomendaciones.

**Reflexión**: El crecimiento personal es único para cada persona. Confía en tu proceso y celebra pequeños logros.

*Artículo personalizado por ${therapistName} para ${userData.displayName}*`;
  } else {
    // Free users get general articles
    articleContent = `**Entendiendo ${topic}**: Una guía esencial para el bienestar mental.

**Introducción**: ${topic} es un aspecto fundamental del bienestar mental que afecta a muchas personas. Comprender sus características te ayudará a desarrollar estrategias efectivas.

**Puntos Clave**:
• **Reconocimiento**: Identifica señales y patrones en tu experiencia
• **Comprensión**: Aprende sobre las causas y factores contribuyentes
• **Acción**: Implementa técnicas prácticas de manejo

**Estrategias Generales**:
• Práctica diaria de mindfulness (10 minutos)
• Ejercicios de respiración profunda
• Journaling reflexivo
• Actividad física regular
• Conexión social saludable

**Conclusión**: El manejo efectivo de ${topic} requiere práctica constante y paciencia. Cada pequeño paso cuenta en tu journey de bienestar.

*Artículo educativo de Mente en Calma*`;
  }

  // Ensure content is limited to 120 words
  const limitedContent = limitWords(articleContent, 120);
  const wordCount = countWords(limitedContent);

  return {
    title: isPersonalized ?
      `${topic}: Guía Personalizada para ${userData.displayName}` :
      `Entendiendo ${topic}: Guía Esencial`,
    content: limitedContent,
    category: category,
    topic: topic,
    readingTime: '2 min',
    wordCount: wordCount,
    isPersonalized: isPersonalized,
    therapistName: isPersonalized ? therapistName : null,
    tags: [topic, category, 'bienestar', 'salud mental'],
    contentType: isPersonalized ? 'personalized' : 'general',
    generatedBy: isPersonalized ? therapistName : 'Mente en Calma',
    maxWords: 120,
    actualWords: wordCount
  };
}
