package com.menteencalma.app.domain.repository;

/**
 * Repositorio para operaciones de autenticación
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u0004\u0018\u00010\u0003H\u00a6@\u00a2\u0006\u0002\u0010\u0004J$\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\n\u0010\u000bJ,\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00030\u00062\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\r\u001a\u00020\tH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u000e\u0010\u000fJ$\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00030\u00062\u0006\u0010\u0011\u001a\u00020\tH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0012\u0010\u000bJ\u000e\u0010\u0013\u001a\u00020\u0007H\u00a6@\u00a2\u0006\u0002\u0010\u0004J,\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00030\u00062\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\r\u001a\u00020\tH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0015\u0010\u000f\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0016"}, d2 = {"Lcom/menteencalma/app/domain/repository/AuthRepository;", "", "getCurrentUser", "Lcom/google/firebase/auth/FirebaseUser;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "sendPasswordResetEmail", "Lkotlin/Result;", "", "email", "", "sendPasswordResetEmail-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "signInWithEmail", "password", "signInWithEmail-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "signInWithGoogle", "idToken", "signInWithGoogle-gIAlu-s", "signOut", "signUpWithEmail", "signUpWithEmail-0E7RQCE", "app_debug"})
public abstract interface AuthRepository {
    
    /**
     * Obtiene el usuario actual autenticado
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCurrentUser(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.google.firebase.auth.FirebaseUser> $completion);
    
    /**
     * Cierra la sesión del usuario actual
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object signOut(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}