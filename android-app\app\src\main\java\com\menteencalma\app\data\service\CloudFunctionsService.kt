package com.menteencalma.app.data.service

import com.google.firebase.functions.FirebaseFunctions
import com.google.firebase.functions.HttpsCallableResult
import com.menteencalma.app.domain.model.ChatMessage
import com.menteencalma.app.domain.model.GeneratedArticle
import com.menteencalma.app.domain.model.Recommendation
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Servicio para llamar a Cloud Functions de Firebase
 */
@Singleton
class CloudFunctionsService @Inject constructor(
    private val functions: FirebaseFunctions,
    private val mockService: MockCloudFunctionsService
) {

    companion object {
        // Flag para usar mock en desarrollo
        private const val USE_MOCK = true // Cambiar a false para usar Cloud Functions reales
    }

    companion object {
        // Nombres de las Cloud Functions
        private const val AI_CHATBOT_SUPPORT = "aiChatbotSupport"
        private const val PERSONALIZED_RECOMMENDATION = "personalizedRecommendation"
        private const val GENERATE_ARTICLE = "generateArticle"
        private const val CREATE_USER_PROFILE = "createUserProfile"
        
        // Códigos de error
        const val USAGE_LIMIT_REACHED = "USAGE_LIMIT_REACHED"
        const val ARTICLE_LIMIT_REACHED = "ARTICLE_LIMIT_REACHED"
        const val SUBSCRIPTION_REQUIRED = "SUBSCRIPTION_REQUIRED"
        const val INVALID_INPUT = "INVALID_INPUT"
        const val INTERNAL_ERROR = "INTERNAL_ERROR"
    }

    /**
     * Llama a la Cloud Function aiChatbotSupport
     */
    suspend fun sendChatMessage(
        userId: String,
        message: String,
        therapistId: String,
        conversationHistory: List<ChatMessage> = emptyList()
    ): Result<ChatbotResponse> {
        return if (USE_MOCK) {
            // Usar servicio mock para desarrollo
            val historyMap = conversationHistory.takeLast(10).map { msg ->
                mapOf(
                    "content" to msg.content,
                    "type" to msg.type.name,
                    "timestamp" to msg.timestamp
                )
            }
            mockService.sendChatMessage(userId, message, therapistId, historyMap)
        } else {
            // Usar Cloud Functions reales
            try {
                val data = hashMapOf(
                    "userId" to userId,
                    "message" to message,
                    "therapistId" to therapistId,
                    "conversationHistory" to conversationHistory.takeLast(10).map { msg ->
                        mapOf(
                            "content" to msg.content,
                            "type" to msg.type.name,
                            "timestamp" to msg.timestamp
                        )
                    }
                )

                val result = functions
                    .getHttpsCallable(AI_CHATBOT_SUPPORT)
                    .call(data)
                    .await()

                val response = parseChatbotResponse(result)
                Result.success(response)
            } catch (e: Exception) {
                Result.failure(parseCloudFunctionError(e))
            }
        }
    }

    /**
     * Llama a la Cloud Function personalizedRecommendation
     */
    suspend fun getPersonalizedRecommendation(
        userId: String,
        currentMood: String? = null,
        preferredCategory: String? = null
    ): Result<RecommendationResponse> {
        return if (USE_MOCK) {
            // Usar servicio mock para desarrollo
            mockService.getPersonalizedRecommendation(userId, currentMood, preferredCategory)
        } else {
            // Usar Cloud Functions reales
            try {
                val data = hashMapOf(
                    "userId" to userId,
                    "currentMood" to currentMood,
                    "preferredCategory" to preferredCategory
                )

                val result = functions
                    .getHttpsCallable(PERSONALIZED_RECOMMENDATION)
                    .call(data)
                    .await()

                val response = parseRecommendationResponse(result)
                Result.success(response)
            } catch (e: Exception) {
                Result.failure(parseCloudFunctionError(e))
            }
        }
    }

    /**
     * Llama a la Cloud Function generateArticle
     */
    suspend fun generateArticle(
        userId: String,
        topic: String,
        difficulty: String = "beginner",
        length: String = "medium"
    ): Result<ArticleResponse> {
        return if (USE_MOCK) {
            // Usar servicio mock para desarrollo
            mockService.generateArticle(userId, topic, difficulty, length)
        } else {
            // Usar Cloud Functions reales
            try {
                val data = hashMapOf(
                    "userId" to userId,
                    "topic" to topic,
                    "difficulty" to difficulty,
                    "length" to length
                )

                val result = functions
                    .getHttpsCallable(GENERATE_ARTICLE)
                    .call(data)
                    .await()

                val response = parseArticleResponse(result)
                Result.success(response)
            } catch (e: Exception) {
                Result.failure(parseCloudFunctionError(e))
            }
        }
    }

    /**
     * Llama a la Cloud Function createUserProfile
     */
    suspend fun createUserProfile(userId: String): Result<UserProfileResponse> {
        return if (USE_MOCK) {
            // Usar servicio mock para desarrollo
            mockService.createUserProfile(userId)
        } else {
            // Usar Cloud Functions reales
            try {
                val data = hashMapOf("userId" to userId)

                val result = functions
                    .getHttpsCallable(CREATE_USER_PROFILE)
                    .call(data)
                    .await()

                val response = parseUserProfileResponse(result)
                Result.success(response)
            } catch (e: Exception) {
                Result.failure(parseCloudFunctionError(e))
            }
        }
    }

    private fun parseChatbotResponse(result: HttpsCallableResult): ChatbotResponse {
        val data = result.data as? Map<String, Any> ?: throw Exception("Invalid response format")
        
        return ChatbotResponse(
            message = data["message"] as? String ?: "",
            therapistId = data["therapistId"] as? String ?: "",
            sentiment = data["sentiment"] as? String,
            confidence = (data["confidence"] as? Number)?.toFloat() ?: 0f,
            topics = (data["topics"] as? List<*>)?.mapNotNull { it as? String } ?: emptyList(),
            sessionId = data["sessionId"] as? String,
            metadata = data["metadata"] as? Map<String, Any> ?: emptyMap()
        )
    }

    private fun parseRecommendationResponse(result: HttpsCallableResult): RecommendationResponse {
        val data = result.data as? Map<String, Any> ?: throw Exception("Invalid response format")
        
        return RecommendationResponse(
            title = data["title"] as? String ?: "",
            content = data["content"] as? String ?: "",
            isPersonalized = data["isPersonalized"] as? Boolean ?: false,
            category = data["category"] as? String ?: "",
            difficulty = data["difficulty"] as? String ?: "beginner",
            estimatedDuration = (data["estimatedDuration"] as? Number)?.toInt() ?: 0,
            tags = (data["tags"] as? List<*>)?.mapNotNull { it as? String } ?: emptyList(),
            therapistId = data["therapistId"] as? String ?: "",
            metadata = data["metadata"] as? Map<String, Any> ?: emptyMap()
        )
    }

    private fun parseArticleResponse(result: HttpsCallableResult): ArticleResponse {
        val data = result.data as? Map<String, Any> ?: throw Exception("Invalid response format")
        
        return ArticleResponse(
            title = data["title"] as? String ?: "",
            content = data["content"] as? String ?: "",
            summary = data["summary"] as? String ?: "",
            imageUrl = data["imageUrl"] as? String,
            estimatedReadTime = (data["estimatedReadTime"] as? Number)?.toInt() ?: 0,
            tags = (data["tags"] as? List<*>)?.mapNotNull { it as? String } ?: emptyList(),
            metadata = data["metadata"] as? Map<String, Any> ?: emptyMap()
        )
    }

    private fun parseUserProfileResponse(result: HttpsCallableResult): UserProfileResponse {
        val data = result.data as? Map<String, Any> ?: throw Exception("Invalid response format")
        
        return UserProfileResponse(
            personalizedGreeting = data["personalizedGreeting"] as? String ?: "",
            success = data["success"] as? Boolean ?: false,
            message = data["message"] as? String ?: ""
        )
    }

    private fun parseCloudFunctionError(exception: Exception): CloudFunctionException {
        val message = exception.message ?: "Unknown error"
        
        // Parsear códigos de error específicos
        val errorCode = when {
            message.contains("USAGE_LIMIT_REACHED") -> USAGE_LIMIT_REACHED
            message.contains("ARTICLE_LIMIT_REACHED") -> ARTICLE_LIMIT_REACHED
            message.contains("SUBSCRIPTION_REQUIRED") -> SUBSCRIPTION_REQUIRED
            message.contains("INVALID_INPUT") -> INVALID_INPUT
            else -> INTERNAL_ERROR
        }
        
        return CloudFunctionException(
            code = errorCode,
            message = message,
            originalException = exception
        )
    }
}

/**
 * Respuesta del chatbot de IA
 */
data class ChatbotResponse(
    val message: String,
    val therapistId: String,
    val sentiment: String? = null,
    val confidence: Float = 0f,
    val topics: List<String> = emptyList(),
    val sessionId: String? = null,
    val metadata: Map<String, Any> = emptyMap()
)

/**
 * Respuesta de recomendación personalizada
 */
data class RecommendationResponse(
    val title: String,
    val content: String,
    val isPersonalized: Boolean,
    val category: String,
    val difficulty: String,
    val estimatedDuration: Int,
    val tags: List<String>,
    val therapistId: String,
    val metadata: Map<String, Any> = emptyMap()
)

/**
 * Respuesta de artículo generado
 */
data class ArticleResponse(
    val title: String,
    val content: String,
    val summary: String,
    val imageUrl: String? = null,
    val estimatedReadTime: Int,
    val tags: List<String>,
    val metadata: Map<String, Any> = emptyMap()
)

/**
 * Respuesta de creación de perfil
 */
data class UserProfileResponse(
    val personalizedGreeting: String,
    val success: Boolean,
    val message: String
)

/**
 * Excepción específica para errores de Cloud Functions
 */
class CloudFunctionException(
    val code: String,
    message: String,
    val originalException: Exception
) : Exception(message)
