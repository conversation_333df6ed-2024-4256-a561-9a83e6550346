package com.menteencalma.app.presentation.screens.disclaimer;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0003\u001a\b\u0010\u0002\u001a\u00020\u0001H\u0003\u001a\b\u0010\u0003\u001a\u00020\u0001H\u0003\u001a\b\u0010\u0004\u001a\u00020\u0001H\u0003\u001a\b\u0010\u0005\u001a\u00020\u0001H\u0003\u001a\u0016\u0010\u0006\u001a\u00020\u00012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0007\u001a\b\u0010\t\u001a\u00020\u0001H\u0003\u001a\b\u0010\n\u001a\u00020\u0001H\u0003\u001a\b\u0010\u000b\u001a\u00020\u0001H\u0003\u00a8\u0006\f"}, d2 = {"AILimitationsSection", "", "AIToolSection", "ContactSection", "DisclaimerContent", "DisclaimerHeader", "DisclaimerScreen", "onNavigateBack", "Lkotlin/Function0;", "EmergencySection", "NotProfessionalHelpSection", "UserResponsibilitySection", "app_debug"})
public final class DisclaimerScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void DisclaimerScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void DisclaimerHeader() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void DisclaimerContent() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AIToolSection() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void NotProfessionalHelpSection() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AILimitationsSection() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void UserResponsibilitySection() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void EmergencySection() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ContactSection() {
    }
}