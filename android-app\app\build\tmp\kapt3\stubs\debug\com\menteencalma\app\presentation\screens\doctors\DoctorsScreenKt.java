package com.menteencalma.app.presentation.screens.doctors;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0003\u001a\b\u0010\u0002\u001a\u00020\u0001H\u0003\u001a\b\u0010\u0003\u001a\u00020\u0001H\u0003\u001a\b\u0010\u0004\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0005\u001a\u00020\u0001H\u0003\u001a\u0010\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\bH\u0003\u001a\b\u0010\t\u001a\u00020\u0001H\u0003\u00a8\u0006\n"}, d2 = {"ComingSoonCard", "", "ContactCard", "DoctorsHeader", "DoctorsScreen", "EmergencyResourcesCard", "FeatureRow", "feature", "Lcom/menteencalma/app/presentation/screens/doctors/FeatureItem;", "FeaturesPreview", "app_debug"})
public final class DoctorsScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void DoctorsScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void DoctorsHeader() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ComingSoonCard() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void FeaturesPreview() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void FeatureRow(com.menteencalma.app.presentation.screens.doctors.FeatureItem feature) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ContactCard() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void EmergencyResourcesCard() {
    }
}