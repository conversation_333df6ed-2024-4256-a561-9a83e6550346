# Pantallas de Autenticación - Mente en Calma

Este documento describe la implementación completa de las pantallas de Login/Registro y Creación de Perfil para la aplicación Android "Mente en Calma".

## 🔐 LoginScreen

### Diseño y Funcionalidad

La `LoginScreen` replica el diseño web con las siguientes características:

#### **Estructura Visual**
- **Logo y título** de la aplicación en la parte superior
- **Pestañas** para alternar entre "Iniciar Sesión" y "Crear Cuenta"
- **Formularios** específicos para cada pestaña
- **Botón de Google** para autenticación social
- **Manejo de errores** con mensajes informativos

#### **Pestañas Implementadas**

##### 1. Pestaña "Iniciar Sesión"
```kotlin
// Campos del formulario
- Email (con validación)
- Contraseña (con toggle de visibilidad)
- Botón "Iniciar Sesión"
- Botón "Continuar con Google"
```

##### 2. Pestaña "Crear Cuenta"
```kotlin
// Campos del formulario
- Email (con validación)
- Contraseña (con toggle de visibilidad)
- Confirmar Contraseña (con validación de coincidencia)
- Botón "Crear Cuenta"
- Botón "Continuar con Google"
```

### LoginViewModel

El `LoginViewModel` maneja toda la lógica de autenticación:

#### **Estados de UI**
```kotlin
data class LoginUiState(
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val isLoginSuccessful: Boolean = false,
    val needsProfile: Boolean = false
)
```

#### **Funciones Principales**

##### `signInWithEmail(email: String, password: String)`
- Valida formato de email
- Valida que la contraseña no esté vacía
- Llama a `AuthRepository.signInWithEmail()`
- Verifica si el usuario tiene perfil en Firestore
- Navega según el resultado

##### `signUpWithEmail(email: String, password: String, confirmPassword: String)`
- Valida formato de email
- Valida longitud mínima de contraseña (6 caracteres)
- Valida que las contraseñas coincidan
- Llama a `AuthRepository.signUpWithEmail()`
- Siempre navega a crear perfil tras registro exitoso

##### `signInWithGoogle(idToken: String)`
- Maneja autenticación con Google
- Verifica existencia de perfil
- Navega según el estado del usuario

#### **Manejo de Errores**
```kotlin
private fun getErrorMessage(exception: Throwable?): String {
    return when (exception?.message) {
        "The email address is badly formatted." -> "El formato del email no es válido"
        "There is no user record corresponding to this identifier." -> "No existe una cuenta con este email"
        "The password is invalid or the user does not have a password." -> "Contraseña incorrecta"
        "The email address is already in use by another account." -> "Ya existe una cuenta con este email"
        // ... más casos
        else -> exception?.message ?: "Error desconocido"
    }
}
```

### Navegación desde LoginScreen

```kotlin
// Observar cambios en el estado para navegar
LaunchedEffect(uiState.isLoginSuccessful) {
    if (uiState.isLoginSuccessful) {
        onLoginSuccess() // Navega a la app principal
    }
}

LaunchedEffect(uiState.needsProfile) {
    if (uiState.needsProfile) {
        onNavigateToCreateProfile() // Navega a crear perfil
    }
}
```

## 👤 CreateProfileScreen

### Diseño y Funcionalidad

La `CreateProfileScreen` implementa un formulario vertical completo:

#### **Estructura Visual**
- **Header** con icono de perfil y descripción
- **Formulario** con validaciones en tiempo real
- **Selecciones** interactivas para género y terapeuta
- **Botón de guardar** con indicador de carga

#### **Campos del Formulario**

##### 1. Nombre (Obligatorio)
```kotlin
OutlinedTextField(
    value = uiState.displayName,
    onValueChange = onDisplayNameChange,
    label = { Text("Nombre *") },
    placeholder = { Text("¿Cómo te llamas?") }
)
```

##### 2. Edad (Opcional)
```kotlin
OutlinedTextField(
    value = uiState.age,
    onValueChange = { /* Solo números, máximo 3 dígitos */ },
    label = { Text("Edad (opcional)") },
    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
)
```

##### 3. Género (Opcional)
```kotlin
// Opciones disponibles:
- Masculino
- Femenino  
- Otro
- Prefiero no decir
```

##### 4. Terapeuta (Obligatorio)
```kotlin
// Opciones disponibles:
- Psicóloga Aurora: "Especialista en terapia cognitivo-conductual y mindfulness"
- Psicólogo Alejandro: "Experto en psicología positiva y gestión emocional"
```

### CreateProfileViewModel

#### **Estados de UI**
```kotlin
data class CreateProfileUiState(
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val isProfileCreated: Boolean = false,
    val displayName: String = "",
    val age: String = "",
    val gender: String = "",
    val therapistGender: String = ""
)
```

#### **Validaciones Implementadas**
```kotlin
fun createProfile() {
    // 1. Nombre obligatorio y mínimo 2 caracteres
    if (currentState.displayName.isBlank()) {
        _uiState.value = currentState.copy(
            errorMessage = "El nombre es obligatorio"
        )
        return
    }
    
    // 2. Edad válida si se proporciona (13-120 años)
    if (currentState.age.isNotBlank()) {
        val ageInt = currentState.age.toIntOrNull()
        if (ageInt == null || ageInt < 13 || ageInt > 120) {
            _uiState.value = currentState.copy(
                errorMessage = "Por favor ingresa una edad válida (13-120 años)"
            )
            return
        }
    }
    
    // 3. Terapeuta obligatorio
    if (currentState.therapistGender.isBlank()) {
        _uiState.value = currentState.copy(
            errorMessage = "Por favor selecciona tu terapeuta preferido"
        )
        return
    }
}
```

#### **Proceso de Creación de Perfil**

La función `createProfile()` ejecuta los siguientes pasos:

##### 1. Actualizar displayName en Firebase Auth
```kotlin
val profileUpdates = userProfileChangeRequest {
    displayName = currentState.displayName
}
firebaseUser.updateProfile(profileUpdates).await()
```

##### 2. Crear documento en Firestore
```kotlin
val user = User(
    id = firebaseUser.uid,
    email = firebaseUser.email ?: "",
    displayName = currentState.displayName,
    age = if (currentState.age.isNotBlank()) currentState.age.toIntOrNull() else null,
    gender = if (currentState.gender.isNotBlank()) currentState.gender else null,
    therapistGender = currentState.therapistGender,
    subscriptionStatus = "free",
    createdAt = System.currentTimeMillis(),
    updatedAt = System.currentTimeMillis()
)

val createResult = userRepository.createUserProfile(user)
```

##### 3. Llamar a Cloud Function
```kotlin
private suspend fun callCreateUserProfileFunction(userId: String) {
    try {
        val data = hashMapOf("userId" to userId)
        
        functions
            .getHttpsCallable("createUserProfile")
            .call(data)
            .await()
    } catch (e: Exception) {
        // Log el error pero no fallar la creación del perfil
        println("Error calling createUserProfile function: ${e.message}")
        throw e
    }
}
```

### Componentes UI Personalizados

#### **TherapistCard**
```kotlin
@Composable
private fun TherapistCard(
    name: String,
    description: String,
    value: String,
    isSelected: Boolean,
    onSelected: (String) -> Unit,
    isError: Boolean = false
)
```

Características:
- **Diseño de tarjeta** con bordes y colores dinámicos
- **RadioButton** integrado
- **Información del terapeuta** con nombre y descripción
- **Estados visuales** para selección y error

#### **GenderSelection**
```kotlin
@Composable
private fun GenderSelection(
    selectedGender: String,
    onGenderSelected: (String) -> Unit
)
```

Características:
- **Lista de opciones** con RadioButtons
- **Selección única** entre las opciones disponibles
- **Diseño consistente** con el resto del formulario

## 🔄 Flujo de Navegación Completo

```mermaid
graph TD
    A[SplashScreen] --> B{¿Usuario autenticado?}
    B -->|No| C[LoginScreen]
    B -->|Sí| D{¿Tiene perfil?}
    D -->|No| E[CreateProfileScreen]
    D -->|Sí| F[ChatScreen - App Principal]
    
    C --> G[Login exitoso]
    C --> H[Registro exitoso]
    G --> I{¿Tiene perfil?}
    I -->|Sí| F
    I -->|No| E
    H --> E
    E --> J[Perfil creado]
    J --> F
```

## 🛠️ Configuración Técnica

### Dependencias Requeridas

```kotlin
// Firebase
implementation(platform("com.google.firebase:firebase-bom:32.7.1"))
implementation("com.google.firebase:firebase-auth-ktx")
implementation("com.google.firebase:firebase-firestore-ktx")
implementation("com.google.firebase:firebase-functions-ktx")

// Compose
implementation("androidx.compose.material3:material3")
implementation("androidx.compose.material:material-icons-extended")

// Navigation
implementation("androidx.navigation:navigation-compose:2.7.6")
implementation("androidx.hilt:hilt-navigation-compose:1.1.0")

// Hilt
implementation("com.google.dagger:hilt-android:2.48")
kapt("com.google.dagger:hilt-compiler:2.48")
```

### Configuración de Firebase

1. **Añadir google-services.json** al directorio `app/`
2. **Habilitar Authentication** en Firebase Console
3. **Configurar Firestore** con las reglas de seguridad apropiadas
4. **Desplegar Cloud Function** `createUserProfile`

### Estructura de Datos en Firestore

```javascript
// Colección: users
{
  "uid": {
    "id": "uid",
    "email": "<EMAIL>",
    "displayName": "Juan Pérez",
    "age": 25,
    "gender": "male",
    "therapistGender": "aurora",
    "subscriptionStatus": "free",
    "personalizedGreeting": "¡Hola Juan! Soy Aurora...",
    "createdAt": 1234567890,
    "updatedAt": 1234567890
  }
}
```

## 🧪 Testing

### Casos de Prueba Recomendados

#### LoginScreen
- [ ] Validación de email inválido
- [ ] Validación de contraseña vacía
- [ ] Manejo de errores de Firebase Auth
- [ ] Navegación correcta tras login exitoso
- [ ] Navegación a crear perfil tras registro

#### CreateProfileScreen
- [ ] Validación de nombre obligatorio
- [ ] Validación de edad fuera de rango
- [ ] Validación de terapeuta obligatorio
- [ ] Creación exitosa de perfil
- [ ] Llamada a Cloud Function
- [ ] Navegación tras creación exitosa

## 📱 Experiencia de Usuario

### Características UX Implementadas

1. **Feedback Visual**
   - Indicadores de carga durante operaciones
   - Mensajes de error claros y específicos
   - Estados de validación en tiempo real

2. **Navegación Intuitiva**
   - Pestañas claras en LoginScreen
   - Botón de retroceso en CreateProfileScreen
   - Navegación automática basada en estado

3. **Accesibilidad**
   - Content descriptions para iconos
   - Contraste adecuado de colores
   - Tamaños de texto legibles

4. **Responsive Design**
   - Scroll vertical para pantallas pequeñas
   - Espaciado consistente
   - Adaptación a diferentes tamaños de pantalla

---

**Implementación completa** de las pantallas de autenticación para Mente en Calma con Firebase Authentication y Firestore.
