package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.menteencalma.app.MenteEnCalmaApplication",
    rootPackage = "com.menteencalma.app",
    originatingRoot = "com.menteencalma.app.MenteEnCalmaApplication",
    originatingRootPackage = "com.menteencalma.app",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "MenteEnCalmaApplication",
    originatingRootSimpleNames = "MenteEnCalmaApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_menteencalma_app_MenteEnCalmaApplication {
}
