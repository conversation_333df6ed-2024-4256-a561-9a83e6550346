#!/bin/bash

# Firebase Setup Script for Mente en Calma Android App
# This script helps set up Firebase configuration

echo "🔥 Firebase Setup for Mente en Calma Android App"
echo "================================================"

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI not found. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

echo "✅ Firebase CLI found"

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "🔐 Please log in to Firebase:"
    firebase login
fi

echo "✅ Firebase authentication verified"

# List available projects
echo ""
echo "📋 Available Firebase projects:"
firebase projects:list

echo ""
read -p "Enter your Firebase project ID: " PROJECT_ID

if [ -z "$PROJECT_ID" ]; then
    echo "❌ Project ID cannot be empty"
    exit 1
fi

# Set the project
firebase use $PROJECT_ID

if [ $? -ne 0 ]; then
    echo "❌ Failed to set Firebase project. Please check the project ID."
    exit 1
fi

echo "✅ Firebase project set to: $PROJECT_ID"

# Check if google-services.json exists
if [ ! -f "app/google-services.json" ]; then
    echo ""
    echo "⚠️  google-services.json not found!"
    echo "Please download it from Firebase Console and place it in app/google-services.json"
    echo ""
    echo "Steps:"
    echo "1. Go to Firebase Console: https://console.firebase.google.com/"
    echo "2. Select your project: $PROJECT_ID"
    echo "3. Go to Project Settings > General"
    echo "4. In 'Your apps' section, find the Android app"
    echo "5. Download google-services.json"
    echo "6. Place it in: android-app/app/google-services.json"
    echo ""
    read -p "Press Enter when you have placed the google-services.json file..."
    
    if [ ! -f "app/google-services.json" ]; then
        echo "❌ google-services.json still not found. Please add it and run this script again."
        exit 1
    fi
fi

echo "✅ google-services.json found"

# Install Cloud Functions dependencies
echo ""
echo "📦 Installing Cloud Functions dependencies..."
cd firebase-functions
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Cloud Functions dependencies"
    exit 1
fi

cd ..
echo "✅ Cloud Functions dependencies installed"

# Deploy Firestore rules and indexes
echo ""
echo "🔒 Deploying Firestore rules and indexes..."
firebase deploy --only firestore:rules,firestore:indexes

if [ $? -ne 0 ]; then
    echo "❌ Failed to deploy Firestore rules and indexes"
    exit 1
fi

echo "✅ Firestore rules and indexes deployed"

# Deploy Cloud Functions
echo ""
read -p "Do you want to deploy Cloud Functions now? (y/n): " DEPLOY_FUNCTIONS

if [ "$DEPLOY_FUNCTIONS" = "y" ] || [ "$DEPLOY_FUNCTIONS" = "Y" ]; then
    echo "☁️  Deploying Cloud Functions..."
    firebase deploy --only functions
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to deploy Cloud Functions"
        exit 1
    fi
    
    echo "✅ Cloud Functions deployed"
else
    echo "⏭️  Skipping Cloud Functions deployment"
    echo "You can deploy them later with: firebase deploy --only functions"
fi

# Enable required APIs
echo ""
echo "🔧 Required Firebase services to enable:"
echo "1. Authentication (Email/Password + Google Sign-In)"
echo "2. Firestore Database"
echo "3. Cloud Functions"
echo "4. Analytics (optional)"
echo "5. Cloud Messaging (optional)"
echo ""
echo "Please enable these services in Firebase Console:"
echo "https://console.firebase.google.com/project/$PROJECT_ID"

# Create local.properties template
echo ""
echo "📝 Creating local.properties template..."
cat > local.properties << EOF
# Firebase Configuration
firebase.project.id=$PROJECT_ID
firebase.region=us-central1

# Google Sign-In Web Client ID
# Get this from Firebase Console > Authentication > Sign-in method > Google
google.signin.web.client.id=YOUR_WEB_CLIENT_ID.apps.googleusercontent.com

# API Keys (for Cloud Functions)
# Get Gemini API key from Google AI Studio
gemini.api.key=YOUR_GEMINI_API_KEY

# Development settings
debug.mode=true
EOF

echo "✅ local.properties template created"
echo ""
echo "⚠️  Please update local.properties with your actual values:"
echo "- google.signin.web.client.id (from Firebase Console)"
echo "- gemini.api.key (from Google AI Studio)"

# Final instructions
echo ""
echo "🎉 Firebase setup completed!"
echo ""
echo "Next steps:"
echo "1. Enable Authentication in Firebase Console"
echo "2. Create Firestore Database"
echo "3. Update local.properties with real values"
echo "4. Test the Android app"
echo ""
echo "Useful commands:"
echo "- Start emulators: firebase emulators:start"
echo "- Deploy functions: firebase deploy --only functions"
echo "- View logs: firebase functions:log"
echo ""
echo "Firebase Console: https://console.firebase.google.com/project/$PROJECT_ID"
