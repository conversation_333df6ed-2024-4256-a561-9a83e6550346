@app/src/main/java/com/menteencalma/app/domain/model/MoodEntry.ktYapp/src/main/java/com/menteencalma/app/presentation/screens/articles/ArticlesViewModel.ktRapp/src/main/java/com/menteencalma/app/presentation/screens/splash/SplashScreen.ktJapp/src/main/java/com/menteencalma/app/domain/repository/AuthRepository.kt[app/src/main/java/com/menteencalma/app/presentation/screens/subscription/SubscribeScreen.ktGapp/src/main/java/com/menteencalma/app/domain/model/GeneratedArticle.kt8app/src/main/java/com/menteencalma/app/ui/theme/Color.ktUapp/src/main/java/com/menteencalma/app/presentation/screens/mood/MoodTrackerScreen.ktZapp/src/main/java/com/menteencalma/app/presentation/screens/disclaimer/DisclaimerScreen.ktNapp/src/main/java/com/menteencalma/app/domain/repository/DatabaseRepository.ktLapp/src/main/java/com/menteencalma/app/data/repository/AuthRepositoryImpl.kt6app/src/main/java/com/menteencalma/app/di/AppModule.ktUapp/src/main/java/com/menteencalma/app/data/repository/MonitoredDatabaseRepository.ktVapp/src/main/java/com/menteencalma/app/presentation/screens/articles/ArticlesScreen.kt8app/src/main/java/com/menteencalma/app/ui/theme/Theme.ktLapp/src/main/java/com/menteencalma/app/data/service/CloudFunctionsService.ktKapp/src/main/java/com/menteencalma/app/navigation/MenteEnCalmaNavigation.ktQapp/src/main/java/com/menteencalma/app/presentation/screens/chat/ChatViewModel.kt7app/src/main/java/com/menteencalma/app/ui/theme/Type.ktVapp/src/main/java/com/menteencalma/app/presentation/viewmodels/MoodTrackerViewModel.ktUapp/src/main/java/com/menteencalma/app/presentation/components/BottomNavigationBar.ktEapp/src/main/java/com/menteencalma/app/domain/model/Recommendation.ktgapp/src/main/java/com/menteencalma/app/presentation/screens/recommendations/RecommendationsViewModel.ktQapp/src/main/java/com/menteencalma/app/data/migration/DatabaseMigrationService.ktPapp/src/main/java/com/menteencalma/app/presentation/viewmodels/LoginViewModel.ktTapp/src/main/java/com/menteencalma/app/presentation/screens/doctors/DoctorsScreen.kt;app/src/main/java/com/menteencalma/app/navigation/Screen.ktWapp/src/main/java/com/menteencalma/app/presentation/screens/auth/CreateProfileScreen.ktBapp/src/main/java/com/menteencalma/app/domain/model/ChatMessage.ktJapp/src/main/java/com/menteencalma/app/domain/repository/UserRepository.ktNapp/src/main/java/com/menteencalma/app/presentation/screens/chat/ChatScreen.ktdapp/src/main/java/com/menteencalma/app/presentation/screens/recommendations/RecommendationsScreen.kt]app/src/main/java/com/menteencalma/app/presentation/viewmodels/DatabaseMonitoringViewModel.ktLapp/src/main/java/com/menteencalma/app/data/repository/UserRepositoryImpl.ktOapp/src/main/java/com/menteencalma/app/presentation/viewmodels/AuthViewModel.ktWapp/src/main/java/com/menteencalma/app/presentation/screens/profile/ProfileViewModel.ktSapp/src/main/java/com/menteencalma/app/data/monitoring/DatabaseMonitoringService.ktRapp/src/main/java/com/menteencalma/app/presentation/viewmodels/ProfileViewModel.kt]app/src/main/java/com/menteencalma/app/presentation/screens/admin/DatabaseMonitoringScreen.ktTapp/src/main/java/com/menteencalma/app/presentation/screens/profile/ProfileScreen.ktTapp/src/main/java/com/menteencalma/app/data/repository/FirebaseDatabaseRepository.kt>app/src/main/java/com/menteencalma/app/domain/model/Article.kt;app/src/main/java/com/menteencalma/app/domain/model/User.ktPapp/src/main/java/com/menteencalma/app/data/service/MockCloudFunctionsService.ktXapp/src/main/java/com/menteencalma/app/presentation/viewmodels/CreateProfileViewModel.kt[app/src/main/java/com/menteencalma/app/presentation/screens/articles/ArticleDetailScreen.ktOapp/src/main/java/com/menteencalma/app/presentation/screens/auth/LoginScreen.ktAapp/src/main/java/com/menteencalma/app/MenteEnCalmaApplication.kt@app/src/main/java/com/menteencalma/app/domain/model/AuthState.kt6app/src/main/java/com/menteencalma/app/MainActivity.ktTapp/src/main/java/com/menteencalma/app/presentation/viewmodels/SubscribeViewModel.kt                                                                                  