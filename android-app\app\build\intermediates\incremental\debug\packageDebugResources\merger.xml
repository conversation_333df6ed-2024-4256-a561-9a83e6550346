<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="B:\Mente en calma\android-app\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="B:\Mente en calma\android-app\app\src\main\res"><file path="B:\Mente en calma\android-app\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Mente en Calma</string><string name="app_subtitle">Encuentra tu paz interior</string><string name="nav_chat">Chat</string><string name="nav_recommendations">Recomendaciones</string><string name="nav_articles">Artículos</string><string name="nav_mood_tracker">Ánimo</string><string name="nav_doctors">Doctores</string><string name="nav_profile">Perfil</string><string name="chat_title">Chat Terapéutico</string><string name="chat_description">Conversa con nuestro asistente de IA especializado en bienestar mental</string><string name="recommendations_title">Recomendaciones</string><string name="recommendations_description">Descubre actividades y ejercicios personalizados para tu bienestar</string><string name="articles_title">Artículos</string><string name="articles_description">Lee contenido especializado sobre salud mental y bienestar</string><string name="profile_title">Mi Perfil</string><string name="profile_description">Gestiona tu cuenta y configuración personal</string><string name="login_title">Iniciar Sesión</string><string name="create_profile_title">Crear Perfil</string><string name="subscribe_title">Suscripción Premium</string><string name="email_label">Email</string><string name="password_label">Contraseña</string><string name="confirm_password_label">Confirmar Contraseña</string><string name="sign_in_button">Iniciar Sesión</string><string name="sign_up_button">Crear Cuenta</string><string name="continue_with_google">Continuar con Google</string><string name="sign_in_tab">Iniciar Sesión</string><string name="sign_up_tab">Crear Cuenta</string><string name="complete_profile">Completa tu perfil</string><string name="personalize_experience">Ayúdanos a personalizar tu experiencia</string><string name="name_label">Nombre *</string><string name="name_placeholder">¿Cómo te llamas?</string><string name="age_label">Edad (opcional)</string><string name="age_placeholder">Ej: 25</string><string name="gender_label">Género (opcional)</string><string name="therapist_label">Elige tu terapeuta *</string><string name="save_and_continue">Guardar y Continuar</string><string name="creating_profile">Creando perfil...</string><string name="therapist_aurora">Psicóloga Aurora</string><string name="therapist_aurora_description">Especialista en terapia cognitivo-conductual y mindfulness</string><string name="therapist_alejandro">Psicólogo Alejandro</string><string name="therapist_alejandro_description">Experto en psicología positiva y gestión emocional</string><string name="gender_male">Masculino</string><string name="gender_female">Femenino</string><string name="gender_other">Otro</string><string name="gender_prefer_not_to_say">Prefiero no decir</string><string name="loading">Cargando...</string><string name="error">Error</string><string name="retry">Reintentar</string><string name="cancel">Cancelar</string><string name="ok">Aceptar</string><string name="back">Volver</string><string name="logo_description">Logo de Mente en Calma</string><string name="menu_description">Menú</string><string name="settings_description">Configuración</string></file><file name="ic_launcher_background" path="B:\Mente en calma\android-app\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="B:\Mente en calma\android-app\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher_legacy" path="B:\Mente en calma\android-app\app\src\main\res\drawable\ic_launcher_legacy.xml" qualifiers="" type="drawable"/><file name="ic_launcher_temp" path="B:\Mente en calma\android-app\app\src\main\res\drawable\ic_launcher_temp.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="B:\Mente en calma\android-app\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="B:\Mente en calma\android-app\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file path="B:\Mente en calma\android-app\app\src\main\res\values\colors.xml" qualifiers=""><color name="md_theme_light_primary">#6750A4</color><color name="md_theme_light_onPrimary">#FFFFFF</color><color name="md_theme_light_primaryContainer">#EADDFF</color><color name="md_theme_light_onPrimaryContainer">#21005D</color><color name="md_theme_light_secondary">#625B71</color><color name="md_theme_light_onSecondary">#FFFFFF</color><color name="md_theme_light_secondaryContainer">#E8DEF8</color><color name="md_theme_light_onSecondaryContainer">#1D192B</color><color name="md_theme_light_tertiary">#7D5260</color><color name="md_theme_light_onTertiary">#FFFFFF</color><color name="md_theme_light_tertiaryContainer">#FFD8E4</color><color name="md_theme_light_onTertiaryContainer">#31111D</color><color name="md_theme_light_error">#BA1A1A</color><color name="md_theme_light_onError">#FFFFFF</color><color name="md_theme_light_errorContainer">#FFDAD6</color><color name="md_theme_light_onErrorContainer">#410002</color><color name="md_theme_light_outline">#79747E</color><color name="md_theme_light_outlineVariant">#CAC4D0</color><color name="md_theme_light_surface">#FFFBFE</color><color name="md_theme_light_onSurface">#1C1B1F</color><color name="md_theme_light_surfaceVariant">#E7E0EC</color><color name="md_theme_light_onSurfaceVariant">#49454F</color><color name="md_theme_light_inverseSurface">#313033</color><color name="md_theme_light_inverseOnSurface">#F4EFF4</color><color name="md_theme_light_inversePrimary">#D0BCFF</color><color name="md_theme_dark_primary">#D0BCFF</color><color name="md_theme_dark_onPrimary">#381E72</color><color name="md_theme_dark_primaryContainer">#4F378B</color><color name="md_theme_dark_onPrimaryContainer">#EADDFF</color><color name="md_theme_dark_secondary">#CCC2DC</color><color name="md_theme_dark_onSecondary">#332D41</color><color name="md_theme_dark_secondaryContainer">#4A4458</color><color name="md_theme_dark_onSecondaryContainer">#E8DEF8</color><color name="md_theme_dark_tertiary">#EFB8C8</color><color name="md_theme_dark_onTertiary">#492532</color><color name="md_theme_dark_tertiaryContainer">#633B48</color><color name="md_theme_dark_onTertiaryContainer">#FFD8E4</color><color name="md_theme_dark_error">#FFB4AB</color><color name="md_theme_dark_onError">#690005</color><color name="md_theme_dark_errorContainer">#93000A</color><color name="md_theme_dark_onErrorContainer">#FFDAD6</color><color name="md_theme_dark_outline">#938F99</color><color name="md_theme_dark_outlineVariant">#49454F</color><color name="md_theme_dark_surface">#1C1B1F</color><color name="md_theme_dark_onSurface">#E6E1E5</color><color name="md_theme_dark_surfaceVariant">#49454F</color><color name="md_theme_dark_onSurfaceVariant">#CAC4D0</color><color name="md_theme_dark_inverseSurface">#E6E1E5</color><color name="md_theme_dark_inverseOnSurface">#313033</color><color name="md_theme_dark_inversePrimary">#6750A4</color><color name="primary_color">#6750A4</color><color name="secondary_color">#625B71</color><color name="background_color">#FFFBFE</color><color name="surface_color">#FFFBFE</color><color name="error_color">#BA1A1A</color><color name="white">#FFFFFF</color><color name="black">#000000</color><color name="transparent">#00000000</color></file><file path="B:\Mente en calma\android-app\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.MenteEnCalma" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryDark">@color/primary_color</item>
        <item name="colorAccent">@color/secondary_color</item>

        
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>

        
        <item name="android:textColorPrimary">@color/black</item>
        <item name="android:textColorSecondary">@color/secondary_color</item>

        
        <item name="android:statusBarColor">@color/primary_color</item>
        <item name="android:windowLightStatusBar" ns1:targetApi="m">false</item>
    </style><style name="Theme.MenteEnCalma" parent="Base.Theme.MenteEnCalma"/><style name="Theme.MenteEnCalma.Splash" parent="Theme.MenteEnCalma">
        <item name="android:windowBackground">@color/primary_color</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style></file><file path="B:\Mente en calma\android-app\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.MenteEnCalma" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_dark_primary</item>
        <item name="colorPrimaryDark">@color/md_theme_dark_primaryContainer</item>
        <item name="colorAccent">@color/md_theme_dark_secondary</item>

        
        <item name="android:colorBackground">@color/md_theme_dark_surface</item>
        <item name="colorSurface">@color/md_theme_dark_surface</item>

        
        <item name="android:textColorPrimary">@color/md_theme_dark_onSurface</item>
        <item name="android:textColorSecondary">@color/md_theme_dark_onSurfaceVariant</item>

        
        <item name="android:statusBarColor">@color/md_theme_dark_surface</item>
        <item name="android:windowLightStatusBar" ns1:targetApi="m">false</item>
    </style></file><file name="backup_rules" path="B:\Mente en calma\android-app\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="B:\Mente en calma\android-app\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="B:\Mente en calma\android-app\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="B:\Mente en calma\android-app\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="B:\Mente en calma\android-app\app\build\generated\res\resValues\debug"/><source path="B:\Mente en calma\android-app\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="B:\Mente en calma\android-app\app\build\generated\res\resValues\debug"/><source path="B:\Mente en calma\android-app\app\build\generated\res\processDebugGoogleServices"><file path="B:\Mente en calma\android-app\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">672730092753-f78777pqg4cc841dqqmhl231skai11u2.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">672730092753</string><string name="google_api_key" translatable="false">AIzaSyCkryWWUOHPnNKNVQDvH1rrfq_ZEwGhc8w</string><string name="google_app_id" translatable="false">1:672730092753:android:5712be3bea24f685f561f0</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyCkryWWUOHPnNKNVQDvH1rrfq_ZEwGhc8w</string><string name="google_storage_bucket" translatable="false">tu-salud-mental-bed3b.firebasestorage.app</string><string name="project_id" translatable="false">tu-salud-mental-bed3b</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>