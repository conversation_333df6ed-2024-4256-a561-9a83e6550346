@echo off
setlocal enabledelayedexpansion

echo 🔥 Firebase Setup for Mente en Calma Android App
echo ================================================

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Firebase CLI not found. Please install it first:
    echo npm install -g firebase-tools
    pause
    exit /b 1
)

echo ✅ Firebase CLI found

REM Check if user is logged in
firebase projects:list >nul 2>&1
if errorlevel 1 (
    echo 🔐 Please log in to Firebase:
    firebase login
)

echo ✅ Firebase authentication verified

REM List available projects
echo.
echo 📋 Available Firebase projects:
firebase projects:list

echo.
set /p PROJECT_ID="Enter your Firebase project ID: "

if "%PROJECT_ID%"=="" (
    echo ❌ Project ID cannot be empty
    pause
    exit /b 1
)

REM Set the project
firebase use %PROJECT_ID%

if errorlevel 1 (
    echo ❌ Failed to set Firebase project. Please check the project ID.
    pause
    exit /b 1
)

echo ✅ Firebase project set to: %PROJECT_ID%

REM Check if google-services.json exists
if not exist "app\google-services.json" (
    echo.
    echo ⚠️  google-services.json not found!
    echo Please download it from Firebase Console and place it in app\google-services.json
    echo.
    echo Steps:
    echo 1. Go to Firebase Console: https://console.firebase.google.com/
    echo 2. Select your project: %PROJECT_ID%
    echo 3. Go to Project Settings ^> General
    echo 4. In 'Your apps' section, find the Android app
    echo 5. Download google-services.json
    echo 6. Place it in: android-app\app\google-services.json
    echo.
    pause
    
    if not exist "app\google-services.json" (
        echo ❌ google-services.json still not found. Please add it and run this script again.
        pause
        exit /b 1
    )
)

echo ✅ google-services.json found

REM Install Cloud Functions dependencies
echo.
echo 📦 Installing Cloud Functions dependencies...
cd firebase-functions
call npm install

if errorlevel 1 (
    echo ❌ Failed to install Cloud Functions dependencies
    cd ..
    pause
    exit /b 1
)

cd ..
echo ✅ Cloud Functions dependencies installed

REM Deploy Firestore rules and indexes
echo.
echo 🔒 Deploying Firestore rules and indexes...
firebase deploy --only firestore:rules,firestore:indexes

if errorlevel 1 (
    echo ❌ Failed to deploy Firestore rules and indexes
    pause
    exit /b 1
)

echo ✅ Firestore rules and indexes deployed

REM Deploy Cloud Functions
echo.
set /p DEPLOY_FUNCTIONS="Do you want to deploy Cloud Functions now? (y/n): "

if /i "%DEPLOY_FUNCTIONS%"=="y" (
    echo ☁️  Deploying Cloud Functions...
    firebase deploy --only functions
    
    if errorlevel 1 (
        echo ❌ Failed to deploy Cloud Functions
        pause
        exit /b 1
    )
    
    echo ✅ Cloud Functions deployed
) else (
    echo ⏭️  Skipping Cloud Functions deployment
    echo You can deploy them later with: firebase deploy --only functions
)

REM Enable required APIs
echo.
echo 🔧 Required Firebase services to enable:
echo 1. Authentication (Email/Password + Google Sign-In)
echo 2. Firestore Database
echo 3. Cloud Functions
echo 4. Analytics (optional)
echo 5. Cloud Messaging (optional)
echo.
echo Please enable these services in Firebase Console:
echo https://console.firebase.google.com/project/%PROJECT_ID%

REM Create local.properties template
echo.
echo 📝 Creating local.properties template...
(
echo # Firebase Configuration
echo firebase.project.id=%PROJECT_ID%
echo firebase.region=us-central1
echo.
echo # Google Sign-In Web Client ID
echo # Get this from Firebase Console ^> Authentication ^> Sign-in method ^> Google
echo google.signin.web.client.id=YOUR_WEB_CLIENT_ID.apps.googleusercontent.com
echo.
echo # API Keys (for Cloud Functions)
echo # Get Gemini API key from Google AI Studio
echo gemini.api.key=YOUR_GEMINI_API_KEY
echo.
echo # Development settings
echo debug.mode=true
) > local.properties

echo ✅ local.properties template created
echo.
echo ⚠️  Please update local.properties with your actual values:
echo - google.signin.web.client.id (from Firebase Console)
echo - gemini.api.key (from Google AI Studio)

REM Final instructions
echo.
echo 🎉 Firebase setup completed!
echo.
echo Next steps:
echo 1. Enable Authentication in Firebase Console
echo 2. Create Firestore Database
echo 3. Update local.properties with real values
echo 4. Test the Android app
echo.
echo Useful commands:
echo - Start emulators: firebase emulators:start
echo - Deploy functions: firebase deploy --only functions
echo - View logs: firebase functions:log
echo.
echo Firebase Console: https://console.firebase.google.com/project/%PROJECT_ID%

pause
