package com.menteencalma.app.domain.model

import kotlinx.serialization.Serializable

/**
 * Modelo para artículos generados por IA
 */
@Serializable
data class GeneratedArticle(
    val id: String = "",
    val userId: String = "",
    val title: String = "",
    val content: String = "",
    val topic: String = "",
    val imageUrl: String? = null,
    val summary: String = "",
    val estimatedReadTime: Int = 0, // en minutos
    val tags: List<String> = emptyList(),
    val isSaved: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val savedAt: Long? = null,
    
    // Metadata para analytics y mejora del modelo
    val metadata: ArticleMetadata? = null
) {
    
    @Serializable
    data class ArticleMetadata(
        val generationModel: String = "gpt-4",
        val generationTime: Long = 0, // ms
        val wordCount: Int = 0,
        val readabilityScore: Float = 0.0f,
        val sentiment: String? = null, // positive, negative, neutral
        val topics: List<String> = emptyList(),
        val userSubscriptionStatus: String = "free",
        val customData: Map<String, String> = emptyMap()
    )
    
    companion object {
        const val COLLECTION_NAME = "generated_articles"
        const val SAVED_ARTICLES_SUBCOLLECTION = "savedArticles"
        
        // Límites por tipo de usuario
        const val FREE_USER_DAILY_LIMIT = 3
        const val PREMIUM_USER_DAILY_LIMIT = 20
        const val PREMIUM_PLUS_USER_DAILY_LIMIT = -1 // Ilimitado
        
        // Temas sugeridos
        val SUGGESTED_TOPICS = listOf(
            "Técnicas de respiración para la ansiedad",
            "Mindfulness para principiantes",
            "Cómo manejar el estrés laboral",
            "Meditación antes de dormir",
            "Autoestima y confianza personal",
            "Gestión de emociones difíciles",
            "Relaciones saludables",
            "Hábitos de autocuidado",
            "Superando la procrastinación",
            "Gratitud y pensamiento positivo"
        )
    }
    
    /**
     * Calcula el tiempo estimado de lectura
     */
    fun calculateReadTime(): Int {
        val wordsPerMinute = 200
        val wordCount = content.split("\\s+".toRegex()).size
        return maxOf(1, wordCount / wordsPerMinute)
    }
    
    /**
     * Valida el artículo generado
     */
    fun validate(): Result<Unit> {
        return when {
            id.isBlank() -> Result.failure(IllegalArgumentException("Article ID cannot be blank"))
            userId.isBlank() -> Result.failure(IllegalArgumentException("User ID cannot be blank"))
            title.isBlank() -> Result.failure(IllegalArgumentException("Title cannot be blank"))
            content.isBlank() -> Result.failure(IllegalArgumentException("Content cannot be blank"))
            topic.isBlank() -> Result.failure(IllegalArgumentException("Topic cannot be blank"))
            else -> Result.success(Unit)
        }
    }
    
    /**
     * Convierte a formato para guardar en Firestore
     */
    fun toFirestoreMap(): Map<String, Any?> = mapOf(
        "id" to id,
        "userId" to userId,
        "title" to title,
        "content" to content,
        "topic" to topic,
        "imageUrl" to imageUrl,
        "summary" to summary,
        "estimatedReadTime" to estimatedReadTime,
        "tags" to tags,
        "isSaved" to isSaved,
        "createdAt" to createdAt,
        "savedAt" to savedAt,
        "metadata" to metadata?.let { meta ->
            mapOf(
                "generationModel" to meta.generationModel,
                "generationTime" to meta.generationTime,
                "wordCount" to meta.wordCount,
                "readabilityScore" to meta.readabilityScore,
                "sentiment" to meta.sentiment,
                "topics" to meta.topics,
                "userSubscriptionStatus" to meta.userSubscriptionStatus,
                "customData" to meta.customData
            )
        }
    )
}
