package com.menteencalma.app.domain.model

/**
 * Estados de autenticación de la aplicación
 */
sealed class AuthState {
    /**
     * Estado inicial mientras se verifica la autenticación
     */
    object Loading : AuthState()
    
    /**
     * Usuario no autenticado - debe ir a login
     */
    object Unauthenticated : AuthState()
    
    /**
     * Usuario autenticado pero sin perfil - debe crear perfil
     */
    object NeedsProfile : AuthState()
    
    /**
     * Usuario completamente autenticado con perfil
     */
    object Authenticated : AuthState()
    
    /**
     * Error en el proceso de autenticación
     */
    data class Error(val message: String) : AuthState()
}
