package com.menteencalma.app.presentation.viewmodels;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0016\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BW\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\b\b\u0002\u0010\t\u001a\u00020\u0005\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b\u0012\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\u0002\u0010\u000eJ\u000b\u0010\u0019\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J\u000f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u000b0\rH\u00c6\u0003J[\u0010 \u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\b\b\u0002\u0010\t\u001a\u00020\u00052\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b2\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\rH\u00c6\u0001J\u0013\u0010!\u001a\u00020\u00052\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010#\u001a\u00020$H\u00d6\u0001J\t\u0010%\u001a\u00020\bH\u00d6\u0001R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0015R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0015R\u0011\u0010\t\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0015R\u0013\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018\u00a8\u0006&"}, d2 = {"Lcom/menteencalma/app/presentation/viewmodels/SubscribeUiState;", "", "currentUser", "Lcom/menteencalma/app/domain/model/User;", "isLoading", "", "isPurchasing", "errorMessage", "", "purchaseSuccess", "selectedPlan", "Lcom/menteencalma/app/presentation/viewmodels/SubscriptionPlan;", "availablePlans", "", "(Lcom/menteencalma/app/domain/model/User;ZZLjava/lang/String;ZLcom/menteencalma/app/presentation/viewmodels/SubscriptionPlan;Ljava/util/List;)V", "getAvailablePlans", "()Ljava/util/List;", "getCurrentUser", "()Lcom/menteencalma/app/domain/model/User;", "getErrorMessage", "()Ljava/lang/String;", "()Z", "getPurchaseSuccess", "getSelectedPlan", "()Lcom/menteencalma/app/presentation/viewmodels/SubscriptionPlan;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class SubscribeUiState {
    @org.jetbrains.annotations.Nullable()
    private final com.menteencalma.app.domain.model.User currentUser = null;
    private final boolean isLoading = false;
    private final boolean isPurchasing = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    private final boolean purchaseSuccess = false;
    @org.jetbrains.annotations.Nullable()
    private final com.menteencalma.app.presentation.viewmodels.SubscriptionPlan selectedPlan = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.menteencalma.app.presentation.viewmodels.SubscriptionPlan> availablePlans = null;
    
    public SubscribeUiState(@org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.User currentUser, boolean isLoading, boolean isPurchasing, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, boolean purchaseSuccess, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.presentation.viewmodels.SubscriptionPlan selectedPlan, @org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.presentation.viewmodels.SubscriptionPlan> availablePlans) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.User getCurrentUser() {
        return null;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isPurchasing() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    public final boolean getPurchaseSuccess() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.presentation.viewmodels.SubscriptionPlan getSelectedPlan() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.presentation.viewmodels.SubscriptionPlan> getAvailablePlans() {
        return null;
    }
    
    public SubscribeUiState() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.User component1() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.presentation.viewmodels.SubscriptionPlan component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.presentation.viewmodels.SubscriptionPlan> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.presentation.viewmodels.SubscribeUiState copy(@org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.User currentUser, boolean isLoading, boolean isPurchasing, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, boolean purchaseSuccess, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.presentation.viewmodels.SubscriptionPlan selectedPlan, @org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.presentation.viewmodels.SubscriptionPlan> availablePlans) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}