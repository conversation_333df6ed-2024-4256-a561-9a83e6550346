package com.menteencalma.app.domain.repository

import com.menteencalma.app.domain.model.User
import com.menteencalma.app.domain.model.ChatMessage
import com.menteencalma.app.domain.model.Subscription
import com.menteencalma.app.domain.model.Article
import kotlinx.coroutines.flow.Flow

/**
 * Abstracción de la base de datos para facilitar migraciones futuras
 * Implementaciones: FirebaseRepository, SupabaseRepository, PostgreSQLRepository
 */
interface DatabaseRepository {
    
    // ==================== USER OPERATIONS ====================
    
    /**
     * Obtiene un usuario por ID
     */
    suspend fun getUser(userId: String): Result<User?>
    
    /**
     * Crea un nuevo usuario
     */
    suspend fun createUser(user: User): Result<Unit>
    
    /**
     * Actualiza un usuario existente
     */
    suspend fun updateUser(user: User): Result<Unit>
    
    /**
     * Observa cambios en un usuario en tiempo real
     */
    fun observeUser(userId: String): Flow<User?>
    
    /**
     * Busca usuarios con filtros complejos
     */
    suspend fun searchUsers(
        ageRange: IntRange? = null,
        gender: String? = null,
        therapistPreference: String? = null,
        subscriptionStatus: String? = null,
        limit: Int = 50
    ): Result<List<User>>
    
    // ==================== CHAT OPERATIONS ====================
    
    /**
     * Obtiene mensajes de chat de un usuario
     */
    suspend fun getChatMessages(
        userId: String,
        limit: Int = 50,
        lastMessageId: String? = null
    ): Result<List<ChatMessage>>
    
    /**
     * Envía un mensaje de chat
     */
    suspend fun sendChatMessage(message: ChatMessage): Result<Unit>
    
    /**
     * Observa mensajes de chat en tiempo real
     */
    fun observeChatMessages(userId: String): Flow<List<ChatMessage>>
    
    /**
     * Marca mensajes como leídos
     */
    suspend fun markMessagesAsRead(userId: String, messageIds: List<String>): Result<Unit>
    
    // ==================== SUBSCRIPTION OPERATIONS ====================
    
    /**
     * Obtiene la suscripción de un usuario
     */
    suspend fun getSubscription(userId: String): Result<Subscription?>
    
    /**
     * Actualiza el estado de suscripción
     */
    suspend fun updateSubscription(subscription: Subscription): Result<Unit>
    
    /**
     * Obtiene estadísticas de suscripciones (para analytics)
     */
    suspend fun getSubscriptionStats(
        startDate: Long,
        endDate: Long
    ): Result<Map<String, Any>>
    
    // ==================== CONTENT OPERATIONS ====================
    
    /**
     * Obtiene artículos con paginación
     */
    suspend fun getArticles(
        category: String? = null,
        isPremium: Boolean? = null,
        limit: Int = 20,
        offset: Int = 0
    ): Result<List<Article>>
    
    /**
     * Obtiene un artículo específico
     */
    suspend fun getArticle(articleId: String): Result<Article?>
    
    /**
     * Registra que un usuario leyó un artículo
     */
    suspend fun recordArticleRead(userId: String, articleId: String): Result<Unit>
    
    // ==================== ANALYTICS OPERATIONS ====================
    
    /**
     * Registra un evento de analytics
     */
    suspend fun logEvent(
        eventName: String,
        parameters: Map<String, Any>
    ): Result<Unit>
    
    /**
     * Obtiene métricas de uso
     */
    suspend fun getUsageMetrics(
        userId: String,
        startDate: Long,
        endDate: Long
    ): Result<Map<String, Any>>
    
    // ==================== BATCH OPERATIONS ====================
    
    /**
     * Ejecuta múltiples operaciones en una transacción
     */
    suspend fun executeTransaction(operations: List<DatabaseOperation>): Result<Unit>
    
    /**
     * Operación de base de datos para transacciones
     */
    sealed class DatabaseOperation {
        data class CreateUser(val user: User) : DatabaseOperation()
        data class UpdateUser(val user: User) : DatabaseOperation()
        data class CreateMessage(val message: ChatMessage) : DatabaseOperation()
        data class UpdateSubscription(val subscription: Subscription) : DatabaseOperation()
    }
}
