package com.menteencalma.app.presentation.screens.articles;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00008\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\t\u001a2\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\bH\u0003\u001a\u0016\u0010\t\u001a\u00020\u00012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u000bH\u0003\u001a\b\u0010\f\u001a\u00020\u0001H\u0003\u001a4\u0010\r\u001a\u00020\u00012\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\u000f\u001a\u00020\u0010H\u0007\u001a\u001e\u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\u00062\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\u000bH\u0003\u001a4\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00032\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\u000b2\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00010\u000bH\u0003\u001a,\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00162\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\u000b2\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\u000bH\u0003\u001a>\u0010\u001d\u001a\u00020\u00012\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00160\u00052\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\b2\u0012\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\bH\u0003\u00a8\u0006\u001f"}, d2 = {"ArticleGenerationCard", "", "isGenerating", "", "suggestedTopics", "", "", "onGenerateArticle", "Lkotlin/Function1;", "ArticlePaywallCard", "onNavigateToSubscribe", "Lkotlin/Function0;", "ArticlesHeader", "ArticlesScreen", "onNavigateToArticleDetail", "articlesViewModel", "Lcom/menteencalma/app/presentation/screens/articles/ArticlesViewModel;", "ErrorCard", "message", "onDismiss", "GeneratedArticleCard", "article", "Lcom/menteencalma/app/domain/model/GeneratedArticle;", "isSaving", "onSaveArticle", "onClearArticle", "SavedArticleCard", "onDeleteArticle", "onNavigateToDetail", "SavedArticlesSection", "articles", "app_debug"})
public final class ArticlesScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void ArticlesScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onNavigateToArticleDetail, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSubscribe, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.presentation.screens.articles.ArticlesViewModel articlesViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ArticlesHeader() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ArticleGenerationCard(boolean isGenerating, java.util.List<java.lang.String> suggestedTopics, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onGenerateArticle) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ArticlePaywallCard(kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSubscribe) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void GeneratedArticleCard(com.menteencalma.app.domain.model.GeneratedArticle article, boolean isSaving, kotlin.jvm.functions.Function0<kotlin.Unit> onSaveArticle, kotlin.jvm.functions.Function0<kotlin.Unit> onClearArticle) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void SavedArticlesSection(java.util.List<com.menteencalma.app.domain.model.GeneratedArticle> articles, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onDeleteArticle, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onNavigateToDetail) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void SavedArticleCard(com.menteencalma.app.domain.model.GeneratedArticle article, kotlin.jvm.functions.Function0<kotlin.Unit> onDeleteArticle, kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToDetail) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ErrorCard(java.lang.String message, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
}