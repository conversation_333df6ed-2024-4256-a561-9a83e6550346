<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.Theme.MenteEnCalma" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_dark_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_dark_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_dark_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_dark_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_dark_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_dark_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_dark_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_dark_onTertiaryContainer</item>
        <item name="colorError">@color/md_theme_dark_error</item>
        <item name="colorOnError">@color/md_theme_dark_onError</item>
        <item name="colorErrorContainer">@color/md_theme_dark_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_dark_onErrorContainer</item>
        <item name="colorOutline">@color/md_theme_dark_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_dark_outlineVariant</item>
        <item name="colorSurface">@color/md_theme_dark_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_onSurfaceVariant</item>
        <item name="colorSurfaceInverse">@color/md_theme_dark_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_dark_inverseOnSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_dark_inversePrimary</item>
    </style>
    <style name="Base.Theme.SplashScreen.DayNight" parent="Base.Theme.SplashScreen"/>
</resources>