# 🔥 Guía de Configuración Firebase para Android

## 📋 **Pasos de Configuración Completa**

### **1. 🏗️ Configuración del Proyecto Firebase**

#### **A. Crear/Acceder al Proyecto**
1. Ve a [Firebase Console](https://console.firebase.google.com/)
2. Selecciona tu proyecto "Mente en Calma" existente
3. Si no existe, crea un nuevo proyecto:
   - Nombre: "Mente en Calma"
   - ID del proyecto: `mente-en-calma-[random]`
   - Ubicación: tu región preferida

#### **B. Añadir App Android**
1. En el dashboard, haz clic en **"Agregar app" → Android**
2. Completa la información:
   ```
   Nombre del paquete Android: com.menteencalma.app
   Alias de la app: Mente en Calma Android
   Certificado SHA-1: (opcional por ahora)
   ```

#### **C. Descargar google-services.json**
1. Descarga el archivo `google-services.json`
2. **IMPORTANTE:** Colócalo en `android-app/app/google-services.json`
3. Elimina el archivo template: `google-services.json.template`

### **2. 🔐 Configurar Authentication**

#### **A. Habilitar Proveedores de Autenticación**
1. Ve a **Authentication → Sign-in method**
2. Habilita los siguientes proveedores:

**Email/Password:**
```
✅ Habilitar Email/Password
✅ Permitir crear cuentas
✅ Verificación de email (opcional)
```

**Google Sign-In:**
```
✅ Habilitar Google
📧 Email de soporte: <EMAIL>
🔑 SHA-1: (obtener del proyecto Android)
```

#### **B. Obtener SHA-1 Certificate**
Para Google Sign-In, necesitas el SHA-1:

**En Windows:**
```bash
cd android-app
./gradlew signingReport
```

**En macOS/Linux:**
```bash
cd android-app
./gradlew signingReport
```

Busca la línea que dice `SHA1:` y copia ese valor a Firebase.

### **3. 🗄️ Configurar Firestore Database**

#### **A. Crear Base de Datos**
1. Ve a **Firestore Database**
2. Haz clic en **"Crear base de datos"**
3. Selecciona **"Comenzar en modo de prueba"** (por ahora)
4. Elige la ubicación más cercana a tus usuarios

#### **B. Configurar Reglas de Seguridad**
Reemplaza las reglas por defecto con estas:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Allow access to subcollections
      match /{document=**} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // Public read access for app configuration (if needed)
    match /config/{document} {
      allow read: if true;
      allow write: if false; // Only admins can write
    }
  }
}
```

#### **C. Crear Índices Necesarios**
Ve a **Firestore → Índices** y crea estos índices compuestos:

**Para Chat Messages:**
```
Colección: users/{userId}/chatMessages
Campos: timestamp (Descendente), userId (Ascendente)
```

**Para Mood Entries:**
```
Colección: users/{userId}/moodEntries  
Campos: date (Descendente), timestamp (Descendente)
```

**Para Articles:**
```
Colección: users/{userId}/savedArticles
Campos: createdAt (Descendente), category (Ascendente)
```

### **4. ⚡ Configurar Cloud Functions**

#### **A. Habilitar Cloud Functions**
1. Ve a **Functions** en Firebase Console
2. Haz clic en **"Comenzar"**
3. Selecciona la ubicación (misma que Firestore)

#### **B. Funciones Necesarias para la App**
Necesitarás implementar estas Cloud Functions:

```javascript
// functions/index.js
const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

// 1. Create User Profile
exports.createUserProfile = functions.https.onCall(async (data, context) => {
  // Implementar lógica de creación de perfil
  // Retornar saludo personalizado
});

// 2. AI Chatbot Support  
exports.aiChatbotSupport = functions.https.onCall(async (data, context) => {
  // Integración con Gemini AI
  // Manejo de límites de uso
  // Retornar respuesta de IA
});

// 3. Personalized Recommendations
exports.personalizedRecommendation = functions.https.onCall(async (data, context) => {
  // Generar recomendaciones basadas en perfil
  // Retornar contenido personalizado
});

// 4. Generate Article
exports.generateArticle = functions.https.onCall(async (data, context) => {
  // Generar artículo con IA
  // Guardar en savedArticles
  // Manejar límites de generación
});
```

### **5. 📊 Configurar Analytics**

#### **A. Habilitar Google Analytics**
1. Ve a **Analytics** en Firebase Console
2. Haz clic en **"Habilitar Google Analytics"**
3. Selecciona o crea una cuenta de Analytics
4. Configura la propiedad para la app

#### **B. Eventos Personalizados**
La app ya está configurada para enviar estos eventos:
- `user_login`
- `profile_created`
- `chat_message_sent`
- `article_generated`
- `mood_entry_saved`
- `subscription_purchased`

### **6. 🔔 Configurar Cloud Messaging (Opcional)**

#### **A. Habilitar FCM**
1. Ve a **Cloud Messaging**
2. La configuración básica ya está incluida en google-services.json

#### **B. Configurar Notificaciones**
Para notificaciones push futuras:
```kotlin
// Ya incluido en el proyecto
implementation("com.google.firebase:firebase-messaging-ktx")
```

### **7. 🧪 Configurar Remote Config (Opcional)**

#### **A. Habilitar Remote Config**
1. Ve a **Remote Config**
2. Crea estos parámetros:

```json
{
  "chat_daily_limit": 10,
  "article_daily_limit": 2,
  "recommendation_daily_limit": 3,
  "enable_mood_tracking": true,
  "enable_doctors_section": false,
  "subscription_prices": {
    "monthly": "9.99",
    "annual": "79.99"
  }
}
```

### **8. 🔒 Configurar App Check (Recomendado)**

#### **A. Habilitar App Check**
1. Ve a **App Check**
2. Registra tu app Android
3. Selecciona **Play Integrity** como proveedor
4. Habilita **"Enforce"** en producción

### **9. ✅ Verificar Configuración**

#### **A. Checklist de Configuración**
- [ ] google-services.json descargado y colocado
- [ ] Authentication habilitado (Email + Google)
- [ ] Firestore creado con reglas de seguridad
- [ ] Índices de Firestore creados
- [ ] Cloud Functions habilitadas
- [ ] Analytics configurado
- [ ] SHA-1 añadido para Google Sign-In

#### **B. Probar la Conexión**
1. Compila la app: `./gradlew assembleDebug`
2. Instala en dispositivo/emulador
3. Verifica logs en Firebase Console
4. Prueba login con Google
5. Verifica que se crean documentos en Firestore

### **10. 🚀 Configuración de Producción**

#### **A. Reglas de Firestore para Producción**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth != null 
        && request.auth.uid == userId
        && request.auth.token.email_verified == true;
      
      match /{document=**} {
        allow read, write: if request.auth != null 
          && request.auth.uid == userId
          && request.auth.token.email_verified == true;
      }
    }
  }
}
```

#### **B. Configurar Límites y Cuotas**
1. Ve a **Usage and billing**
2. Configura alertas de uso
3. Establece límites de gasto
4. Monitorea métricas de uso

### **11. 🔧 Variables de Entorno**

#### **A. Configuración Local**
Crea `android-app/local.properties`:
```properties
# Firebase
firebase.project.id=mente-en-calma-xxxxx
firebase.region=us-central1

# Google Sign-In
google.signin.web.client.id=tu-web-client-id.apps.googleusercontent.com

# API Keys (para Cloud Functions)
gemini.api.key=tu-gemini-api-key
```

#### **B. Configuración de Build**
En `build.gradle.kts`:
```kotlin
android {
    defaultConfig {
        // Leer desde local.properties
        val properties = Properties()
        properties.load(project.rootProject.file("local.properties").inputStream())
        
        buildConfigField("String", "FIREBASE_PROJECT_ID", "\"${properties.getProperty("firebase.project.id")}\"")
        buildConfigField("String", "WEB_CLIENT_ID", "\"${properties.getProperty("google.signin.web.client.id")}\"")
    }
}
```

---

## 🎯 **Próximos Pasos**

1. **Descargar google-services.json** y colocarlo en la app
2. **Configurar Authentication** con Email y Google
3. **Crear Firestore Database** con reglas de seguridad
4. **Implementar Cloud Functions** para IA y lógica de backend
5. **Probar la app** con Firebase conectado
6. **Configurar producción** con reglas estrictas

Una vez completados estos pasos, la app estará completamente conectada a Firebase y lista para usar todas sus funcionalidades.
