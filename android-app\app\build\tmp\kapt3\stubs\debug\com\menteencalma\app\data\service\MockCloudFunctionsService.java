package com.menteencalma.app.data.service;

/**
 * Implementación mock de CloudFunctionsService para desarrollo y testing
 * Simula las respuestas de las Cloud Functions reales
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\u0004\b\u0007\u0018\u0000 &2\u00020\u0001:\u0001&B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J$\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0006\u001a\u00020\u0007H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\b\u0010\tJ\u0016\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\u000b2\u0006\u0010\f\u001a\u00020\u0007H\u0002J@\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u00042\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\u000f\u001a\u00020\u00072\b\b\u0002\u0010\u0010\u001a\u00020\u00072\b\b\u0002\u0010\u0011\u001a\u00020\u0007H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0012\u0010\u0013J \u0010\u0014\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00072\u0006\u0010\u0010\u001a\u00020\u00072\u0006\u0010\u0011\u001a\u00020\u0007H\u0002J\u0018\u0010\u0015\u001a\u00020\u00162\u0006\u0010\f\u001a\u00020\u00072\u0006\u0010\u0017\u001a\u00020\u0007H\u0002J\u001c\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u00072\b\u0010\u001b\u001a\u0004\u0018\u00010\u0007H\u0002J<\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00190\u00042\u0006\u0010\u0006\u001a\u00020\u00072\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u0007H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001d\u0010\u001eJ\u0006\u0010\u001f\u001a\u00020 JP\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00160\u00042\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\f\u001a\u00020\u00072\u0006\u0010\u0017\u001a\u00020\u00072\u001a\b\u0002\u0010\"\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010#0\u000bH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b$\u0010%\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\'"}, d2 = {"Lcom/menteencalma/app/data/service/MockCloudFunctionsService;", "", "()V", "createUserProfile", "Lkotlin/Result;", "Lcom/menteencalma/app/data/service/UserProfileResponse;", "userId", "", "createUserProfile-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "extractTopics", "", "message", "generateArticle", "Lcom/menteencalma/app/data/service/ArticleResponse;", "topic", "difficulty", "length", "generateArticle-yxL6bBk", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateMockArticle", "generateMockChatResponse", "Lcom/menteencalma/app/data/service/ChatbotResponse;", "therapistId", "generateMockRecommendation", "Lcom/menteencalma/app/data/service/RecommendationResponse;", "currentMood", "preferredCategory", "getPersonalizedRecommendation", "getPersonalizedRecommendation-BWLJW6A", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "resetCounters", "", "sendChatMessage", "conversationHistory", "", "sendChatMessage-yxL6bBk", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public final class MockCloudFunctionsService {
    private static int chatMessagesCount = 0;
    private static int recommendationsCount = 0;
    private static int articlesCount = 0;
    private static final int FREE_CHAT_LIMIT = 10;
    private static final int FREE_RECOMMENDATIONS_LIMIT = 3;
    private static final int FREE_ARTICLES_LIMIT = 2;
    @org.jetbrains.annotations.NotNull()
    public static final com.menteencalma.app.data.service.MockCloudFunctionsService.Companion Companion = null;
    
    @javax.inject.Inject()
    public MockCloudFunctionsService() {
        super();
    }
    
    private final com.menteencalma.app.data.service.ChatbotResponse generateMockChatResponse(java.lang.String message, java.lang.String therapistId) {
        return null;
    }
    
    private final com.menteencalma.app.data.service.RecommendationResponse generateMockRecommendation(java.lang.String currentMood, java.lang.String preferredCategory) {
        return null;
    }
    
    private final com.menteencalma.app.data.service.ArticleResponse generateMockArticle(java.lang.String topic, java.lang.String difficulty, java.lang.String length) {
        return null;
    }
    
    private final java.util.List<java.lang.String> extractTopics(java.lang.String message) {
        return null;
    }
    
    /**
     * Resetea los contadores para testing
     */
    public final void resetCounters() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/menteencalma/app/data/service/MockCloudFunctionsService$Companion;", "", "()V", "FREE_ARTICLES_LIMIT", "", "FREE_CHAT_LIMIT", "FREE_RECOMMENDATIONS_LIMIT", "articlesCount", "chatMessagesCount", "recommendationsCount", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}