-- Merging decision tree log ---
manifest
ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:2:1-40:12
INJECTED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:2:1-40:12
INJECTED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:2:1-40:12
INJECTED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:2:1-40:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fc483614f664893c46ce351931db2801\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b3c6a0f093a3413786e6550e4cf0d88\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.10\transforms\29dea2d3651b2dddc7d07947185c1383\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c509f50afaa1cb6cfb82bf346447866c\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f970a537c60f8006f45ec19f7980fcf\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\3003fbd4f32647ec23be108081684ba5\transformed\play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.android.billingclient:billing-ktx:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\658f12a736932d07849764a7d0cd2ae4\transformed\billing-ktx-6.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:2:1-30:12
MERGED from [com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d4bdf51ef125aab1e5f2b0dcbac28f90\transformed\firebase-firestore-ktx-24.10.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3f21787bdcfa92bb39b79036212d2c2\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\18f6b06b981057aa8d8f9a8bff10e49c\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9a56cb3b61adac66c36616e519a18d61\transformed\play-services-location-19.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\316a63a31dcb075550a6a1c29016c7d4\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\0eb584644ee2de7f40b061feda878225\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\6684fd1e7690da02823ff433f45d20f7\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\81c1b1018490bdee112bce6191256899\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\72209053ee1b34e957159e0f58c4428f\transformed\navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\98bcb59e84dea2106bcefc907e055731\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\10431135e35bb26cf600dcef30b5aa76\transformed\coil-compose-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\901dfde333e8bb8e2ca014559f272a7f\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\56ab4b41bca4add758a5aa0d9a7a4f36\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\dcc31a35fa5a21b44b365c47f63f7843\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5db65aaa5e0d83625e1369b82a7cca62\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\97af6ae8d491bc527677c3e33a54358c\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5bc9dd71eb8b236a1c190f6f3eacfeeb\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\06cd13d35c289a192e87c5d4224c1a62\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6a33d6a3f68bafbdfe39cbb5d953e4b6\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca611de3937d8b7fad81038213315e33\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\7a13454ba2fbbfc786f7d09cbf0ef843\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\35e020ef9ae7a0d6b891c1cc3ea1f32c\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e3a3a10d241036db8c54dfef906875f5\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\a821b2500146dff1eccfc5967c6fb839\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d24ed18319813a5841292fea80021443\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\aaebd912f0799410cdf7a0a3a37301e8\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\10ae2552bf3cbf45dc9cc601ea39674f\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\509461b3db90194c9948ab40ba752372\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9155ca02ca7bd1c143c6e7bd16ef392\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d60095cfcd5f6da5b72a1052224dd592\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9078f28263f278452faf1cbc8e846522\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\b269d4c19ab9d1b3baa498ab4d0bb27d\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\97295848abff15d4482bb7727176c544\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\4d7488f5f4b07e81288f72ffcdf248b0\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2bde487c913499284c236f80bc181e05\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\3d9a7801f6683606bdfbc1f167eb6309\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b58d55f61545280adacf5a08c9b5c0b0\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bb89cd74a840ab5ff3ac5b6c7e9c5408\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2b36b5d49667b1f5fb3b9dd09bf95436\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\512c1d5216e2bf9639236f47d50e208e\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6d7bf17fe288a51bad85f2e6af6d39a\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\47d882b02ff06b4aab905988600b4821\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d47f20572e1adda04011113fd6054442\transformed\coil-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d260b924aea8be51d43edb0c005cc39b\transformed\coil-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\df5a889645fb5170e23f0cfa48fe0b2f\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\7f6f800ad5296de25d337499ccadfe5c\transformed\lifecycle-runtime-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e3c5b2dd8e4008e6832993c3898af51e\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\487d9d546f93a868a294671b4ac8156f\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\0e61f1e27521a3f08f298c3d6f706b39\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\67eb6d65f32a6286b4e12edbbbe6f5f3\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db33cfb2e58b6d3432243990065bd6a0\transformed\recaptcha-18.4.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1383efd3ff4539c4362bd9a7d57930f0\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\40d6984fd9fdd7db264707aa328773a1\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ac5c0276ae1cb12910b4de426173a319\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2c225e36e678b3641037af90bbe97d1b\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\106bf95208e61cff9e66c9de1db2da17\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\195a38af753262eae38e18830cf6a511\transformed\integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5743639ee258961f6df81f7fb972606d\transformed\play-services-cloud-messaging-16.0.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\a657928917c1ff7074676b07f49471ad\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5d797600981b7fb1829f88f3c8e18912\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\50b11c157f6bc0b5cfa91f7926b53130\transformed\play-services-places-placereport-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\c51f665236df190337eeb2d018edd037\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3cfb89e1f8b79699007d84b341f8e86d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6861ee731554d62c8662abd4f32adf60\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db0b4bc7df706335ac7f6ab9c56811f4\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\4d223db05fd24be12ff04f297ed14cfc\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\29951bf4da308271f736c57a0fca3232\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\186d95920cb0375cbda9abfb0e88b6a0\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a04b49163da1600d972bf21491b8b3fd\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a0791668227f899d135f97a096fb9892\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b64f629af1514ef0774823edfb48a5bc\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ec6bd2faa3e0a998fc0a19c7dd0c22ed\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\50ee6f3e6ca87ad54db36714ad7f176c\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\670bd2a17c8315afc98f75380e87d019\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\97458d4479675195e7cc5b231cb1a770\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9d8220d97db23c7cdad1f764f40cfcc2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c99688c16cf354c20a51ad8077cb67ad\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b1e49370ed9ee03b5df4273fd102a11\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fe70c33ffb9ed1b0fcd252e78cfc91b6\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\31143679d6038d7caacae1d009776bad\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ef2d14478626cad1274bcb320ea7353a\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bf9401a9baa88989d109c42b57f152f4\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b74b83955a82f92010a14f1e0ffd712\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\fc4b3adfcd7a310cde732e8460f5be27\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b9c213bb02ec4577fb312ead61d48fa9\transformed\transport-api-3.0.0\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\64f4d6b2a08f902b0fb0339d225cf748\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\424969147400a74365fabb16a0493ab4\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b50092f697a54e57b5b4addf47300be1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3c08e2954c0b005d7d9514a258acdb1d\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\0dad3ce32b912d3ad6c2ae2d9e21d723\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\7b9f67bcf8cd4b588a2628a4da09e3ab\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\31283cbfe3d64097c8f5b7e6f4f4d976\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.10\transforms\4f4e04ea3b5ce5dce7cef890ce47e9ca\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\d770a627ff7a74c800110608654bbbab\transformed\grpc-android-1.57.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ced696821e2f20ee9b481b688367e7f4\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:9:5-67
MERGED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:9:5-67
MERGED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db33cfb2e58b6d3432243990065bd6a0\transformed\recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db33cfb2e58b6d3432243990065bd6a0\transformed\recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5743639ee258961f6df81f7fb972606d\transformed\play-services-cloud-messaging-16.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5743639ee258961f6df81f7fb972606d\transformed\play-services-cloud-messaging-16.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db33cfb2e58b6d3432243990065bd6a0\transformed\recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db33cfb2e58b6d3432243990065bd6a0\transformed\recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5743639ee258961f6df81f7fb972606d\transformed\play-services-cloud-messaging-16.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5743639ee258961f6df81f7fb972606d\transformed\play-services-cloud-messaging-16.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\d770a627ff7a74c800110608654bbbab\transformed\grpc-android-1.57.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\d770a627ff7a74c800110608654bbbab\transformed\grpc-android-1.57.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:7:22-76
application
ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:9:5-38:19
INJECTED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:9:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\3003fbd4f32647ec23be108081684ba5\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\3003fbd4f32647ec23be108081684ba5\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:18:5-28:19
MERGED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:18:5-28:19
MERGED from [com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d4bdf51ef125aab1e5f2b0dcbac28f90\transformed\firebase-firestore-ktx-24.10.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d4bdf51ef125aab1e5f2b0dcbac28f90\transformed\firebase-firestore-ktx-24.10.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3f21787bdcfa92bb39b79036212d2c2\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3f21787bdcfa92bb39b79036212d2c2\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\18f6b06b981057aa8d8f9a8bff10e49c\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\18f6b06b981057aa8d8f9a8bff10e49c\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9a56cb3b61adac66c36616e519a18d61\transformed\play-services-location-19.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9a56cb3b61adac66c36616e519a18d61\transformed\play-services-location-19.0.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\10ae2552bf3cbf45dc9cc601ea39674f\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\10ae2552bf3cbf45dc9cc601ea39674f\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9078f28263f278452faf1cbc8e846522\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9078f28263f278452faf1cbc8e846522\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1383efd3ff4539c4362bd9a7d57930f0\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1383efd3ff4539c4362bd9a7d57930f0\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ac5c0276ae1cb12910b4de426173a319\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ac5c0276ae1cb12910b4de426173a319\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\106bf95208e61cff9e66c9de1db2da17\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\106bf95208e61cff9e66c9de1db2da17\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\195a38af753262eae38e18830cf6a511\transformed\integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\195a38af753262eae38e18830cf6a511\transformed\integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5d797600981b7fb1829f88f3c8e18912\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5d797600981b7fb1829f88f3c8e18912\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\50b11c157f6bc0b5cfa91f7926b53130\transformed\play-services-places-placereport-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\50b11c157f6bc0b5cfa91f7926b53130\transformed\play-services-places-placereport-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\c51f665236df190337eeb2d018edd037\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\c51f665236df190337eeb2d018edd037\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3cfb89e1f8b79699007d84b341f8e86d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3cfb89e1f8b79699007d84b341f8e86d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6861ee731554d62c8662abd4f32adf60\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6861ee731554d62c8662abd4f32adf60\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db0b4bc7df706335ac7f6ab9c56811f4\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db0b4bc7df706335ac7f6ab9c56811f4\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\31143679d6038d7caacae1d009776bad\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\31143679d6038d7caacae1d009776bad\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\64f4d6b2a08f902b0fb0339d225cf748\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\64f4d6b2a08f902b0fb0339d225cf748\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:17:9-35
	android:label
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:15:9-41
	android:fullBackupContent
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:13:9-54
	android:roundIcon
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:16:9-57
	tools:targetApi
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:19:9-29
	android:icon
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:14:9-52
	android:allowBackup
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:11:9-35
	android:theme
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:18:9-50
	android:dataExtractionRules
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:12:9-65
	android:name
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:10:9-48
activity#com.menteencalma.app.MainActivity
ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:22:9-30:20
	android:exported
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:24:13-36
	android:theme
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:25:13-61
	android:name
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:23:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:26:13-29:29
action#android.intent.action.MAIN
ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:27:17-69
	android:name
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:27:25-66
category#android.intent.category.LAUNCHER
ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:28:17-77
	android:name
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:28:27-74
meta-data#com.google.android.play.billingclient.version
ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:33:9-36:45
MERGED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:19:9-21:37
MERGED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:19:9-21:37
	tools:replace
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:36:13-42
	android:value
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:35:13-34
		REJECTED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:21:13-34
	android:name
		ADDED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:34:13-73
uses-sdk
INJECTED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml
INJECTED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fc483614f664893c46ce351931db2801\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fc483614f664893c46ce351931db2801\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b3c6a0f093a3413786e6550e4cf0d88\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b3c6a0f093a3413786e6550e4cf0d88\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.10\transforms\29dea2d3651b2dddc7d07947185c1383\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.10\transforms\29dea2d3651b2dddc7d07947185c1383\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c509f50afaa1cb6cfb82bf346447866c\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c509f50afaa1cb6cfb82bf346447866c\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f970a537c60f8006f45ec19f7980fcf\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f970a537c60f8006f45ec19f7980fcf\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\3003fbd4f32647ec23be108081684ba5\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\3003fbd4f32647ec23be108081684ba5\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.android.billingclient:billing-ktx:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\658f12a736932d07849764a7d0cd2ae4\transformed\billing-ktx-6.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.billingclient:billing-ktx:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\658f12a736932d07849764a7d0cd2ae4\transformed\billing-ktx-6.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d4bdf51ef125aab1e5f2b0dcbac28f90\transformed\firebase-firestore-ktx-24.10.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d4bdf51ef125aab1e5f2b0dcbac28f90\transformed\firebase-firestore-ktx-24.10.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3f21787bdcfa92bb39b79036212d2c2\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3f21787bdcfa92bb39b79036212d2c2\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\18f6b06b981057aa8d8f9a8bff10e49c\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\18f6b06b981057aa8d8f9a8bff10e49c\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9a56cb3b61adac66c36616e519a18d61\transformed\play-services-location-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9a56cb3b61adac66c36616e519a18d61\transformed\play-services-location-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\316a63a31dcb075550a6a1c29016c7d4\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\316a63a31dcb075550a6a1c29016c7d4\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\0eb584644ee2de7f40b061feda878225\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\0eb584644ee2de7f40b061feda878225\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\6684fd1e7690da02823ff433f45d20f7\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\6684fd1e7690da02823ff433f45d20f7\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\81c1b1018490bdee112bce6191256899\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\81c1b1018490bdee112bce6191256899\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\72209053ee1b34e957159e0f58c4428f\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\72209053ee1b34e957159e0f58c4428f\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\98bcb59e84dea2106bcefc907e055731\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\98bcb59e84dea2106bcefc907e055731\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\10431135e35bb26cf600dcef30b5aa76\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\10431135e35bb26cf600dcef30b5aa76\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\901dfde333e8bb8e2ca014559f272a7f\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\901dfde333e8bb8e2ca014559f272a7f\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\56ab4b41bca4add758a5aa0d9a7a4f36\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\56ab4b41bca4add758a5aa0d9a7a4f36\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\dcc31a35fa5a21b44b365c47f63f7843\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\dcc31a35fa5a21b44b365c47f63f7843\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5db65aaa5e0d83625e1369b82a7cca62\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5db65aaa5e0d83625e1369b82a7cca62\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\97af6ae8d491bc527677c3e33a54358c\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\97af6ae8d491bc527677c3e33a54358c\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5bc9dd71eb8b236a1c190f6f3eacfeeb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5bc9dd71eb8b236a1c190f6f3eacfeeb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\06cd13d35c289a192e87c5d4224c1a62\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\06cd13d35c289a192e87c5d4224c1a62\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6a33d6a3f68bafbdfe39cbb5d953e4b6\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6a33d6a3f68bafbdfe39cbb5d953e4b6\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca611de3937d8b7fad81038213315e33\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca611de3937d8b7fad81038213315e33\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\7a13454ba2fbbfc786f7d09cbf0ef843\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\7a13454ba2fbbfc786f7d09cbf0ef843\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\35e020ef9ae7a0d6b891c1cc3ea1f32c\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\35e020ef9ae7a0d6b891c1cc3ea1f32c\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e3a3a10d241036db8c54dfef906875f5\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e3a3a10d241036db8c54dfef906875f5\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\a821b2500146dff1eccfc5967c6fb839\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\a821b2500146dff1eccfc5967c6fb839\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d24ed18319813a5841292fea80021443\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d24ed18319813a5841292fea80021443\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\aaebd912f0799410cdf7a0a3a37301e8\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\aaebd912f0799410cdf7a0a3a37301e8\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\10ae2552bf3cbf45dc9cc601ea39674f\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\10ae2552bf3cbf45dc9cc601ea39674f\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\509461b3db90194c9948ab40ba752372\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\509461b3db90194c9948ab40ba752372\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9155ca02ca7bd1c143c6e7bd16ef392\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9155ca02ca7bd1c143c6e7bd16ef392\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d60095cfcd5f6da5b72a1052224dd592\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d60095cfcd5f6da5b72a1052224dd592\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9078f28263f278452faf1cbc8e846522\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9078f28263f278452faf1cbc8e846522\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\b269d4c19ab9d1b3baa498ab4d0bb27d\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\b269d4c19ab9d1b3baa498ab4d0bb27d\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\97295848abff15d4482bb7727176c544\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\97295848abff15d4482bb7727176c544\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\4d7488f5f4b07e81288f72ffcdf248b0\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\4d7488f5f4b07e81288f72ffcdf248b0\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2bde487c913499284c236f80bc181e05\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2bde487c913499284c236f80bc181e05\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\3d9a7801f6683606bdfbc1f167eb6309\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\3d9a7801f6683606bdfbc1f167eb6309\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b58d55f61545280adacf5a08c9b5c0b0\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b58d55f61545280adacf5a08c9b5c0b0\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bb89cd74a840ab5ff3ac5b6c7e9c5408\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bb89cd74a840ab5ff3ac5b6c7e9c5408\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2b36b5d49667b1f5fb3b9dd09bf95436\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2b36b5d49667b1f5fb3b9dd09bf95436\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\512c1d5216e2bf9639236f47d50e208e\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\512c1d5216e2bf9639236f47d50e208e\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6d7bf17fe288a51bad85f2e6af6d39a\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6d7bf17fe288a51bad85f2e6af6d39a\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\47d882b02ff06b4aab905988600b4821\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\47d882b02ff06b4aab905988600b4821\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d47f20572e1adda04011113fd6054442\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d47f20572e1adda04011113fd6054442\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d260b924aea8be51d43edb0c005cc39b\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d260b924aea8be51d43edb0c005cc39b\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\df5a889645fb5170e23f0cfa48fe0b2f\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\df5a889645fb5170e23f0cfa48fe0b2f\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\7f6f800ad5296de25d337499ccadfe5c\transformed\lifecycle-runtime-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\7f6f800ad5296de25d337499ccadfe5c\transformed\lifecycle-runtime-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e3c5b2dd8e4008e6832993c3898af51e\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e3c5b2dd8e4008e6832993c3898af51e\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\487d9d546f93a868a294671b4ac8156f\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\487d9d546f93a868a294671b4ac8156f\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\0e61f1e27521a3f08f298c3d6f706b39\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\0e61f1e27521a3f08f298c3d6f706b39\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\67eb6d65f32a6286b4e12edbbbe6f5f3\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\67eb6d65f32a6286b4e12edbbbe6f5f3\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db33cfb2e58b6d3432243990065bd6a0\transformed\recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db33cfb2e58b6d3432243990065bd6a0\transformed\recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1383efd3ff4539c4362bd9a7d57930f0\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1383efd3ff4539c4362bd9a7d57930f0\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\40d6984fd9fdd7db264707aa328773a1\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\40d6984fd9fdd7db264707aa328773a1\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ac5c0276ae1cb12910b4de426173a319\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ac5c0276ae1cb12910b4de426173a319\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2c225e36e678b3641037af90bbe97d1b\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2c225e36e678b3641037af90bbe97d1b\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\106bf95208e61cff9e66c9de1db2da17\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\106bf95208e61cff9e66c9de1db2da17\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\195a38af753262eae38e18830cf6a511\transformed\integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\195a38af753262eae38e18830cf6a511\transformed\integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5743639ee258961f6df81f7fb972606d\transformed\play-services-cloud-messaging-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5743639ee258961f6df81f7fb972606d\transformed\play-services-cloud-messaging-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\a657928917c1ff7074676b07f49471ad\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\a657928917c1ff7074676b07f49471ad\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5d797600981b7fb1829f88f3c8e18912\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5d797600981b7fb1829f88f3c8e18912\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\50b11c157f6bc0b5cfa91f7926b53130\transformed\play-services-places-placereport-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\50b11c157f6bc0b5cfa91f7926b53130\transformed\play-services-places-placereport-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\c51f665236df190337eeb2d018edd037\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\c51f665236df190337eeb2d018edd037\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3cfb89e1f8b79699007d84b341f8e86d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3cfb89e1f8b79699007d84b341f8e86d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6861ee731554d62c8662abd4f32adf60\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6861ee731554d62c8662abd4f32adf60\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db0b4bc7df706335ac7f6ab9c56811f4\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db0b4bc7df706335ac7f6ab9c56811f4\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\4d223db05fd24be12ff04f297ed14cfc\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\4d223db05fd24be12ff04f297ed14cfc\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\29951bf4da308271f736c57a0fca3232\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\29951bf4da308271f736c57a0fca3232\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\186d95920cb0375cbda9abfb0e88b6a0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\186d95920cb0375cbda9abfb0e88b6a0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a04b49163da1600d972bf21491b8b3fd\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a04b49163da1600d972bf21491b8b3fd\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a0791668227f899d135f97a096fb9892\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a0791668227f899d135f97a096fb9892\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b64f629af1514ef0774823edfb48a5bc\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b64f629af1514ef0774823edfb48a5bc\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ec6bd2faa3e0a998fc0a19c7dd0c22ed\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ec6bd2faa3e0a998fc0a19c7dd0c22ed\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\50ee6f3e6ca87ad54db36714ad7f176c\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\50ee6f3e6ca87ad54db36714ad7f176c\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\670bd2a17c8315afc98f75380e87d019\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\670bd2a17c8315afc98f75380e87d019\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\97458d4479675195e7cc5b231cb1a770\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\97458d4479675195e7cc5b231cb1a770\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9d8220d97db23c7cdad1f764f40cfcc2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9d8220d97db23c7cdad1f764f40cfcc2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c99688c16cf354c20a51ad8077cb67ad\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c99688c16cf354c20a51ad8077cb67ad\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b1e49370ed9ee03b5df4273fd102a11\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b1e49370ed9ee03b5df4273fd102a11\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fe70c33ffb9ed1b0fcd252e78cfc91b6\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fe70c33ffb9ed1b0fcd252e78cfc91b6\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\31143679d6038d7caacae1d009776bad\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\31143679d6038d7caacae1d009776bad\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ef2d14478626cad1274bcb320ea7353a\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ef2d14478626cad1274bcb320ea7353a\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bf9401a9baa88989d109c42b57f152f4\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bf9401a9baa88989d109c42b57f152f4\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b74b83955a82f92010a14f1e0ffd712\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b74b83955a82f92010a14f1e0ffd712\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\fc4b3adfcd7a310cde732e8460f5be27\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\fc4b3adfcd7a310cde732e8460f5be27\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b9c213bb02ec4577fb312ead61d48fa9\transformed\transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b9c213bb02ec4577fb312ead61d48fa9\transformed\transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\64f4d6b2a08f902b0fb0339d225cf748\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\64f4d6b2a08f902b0fb0339d225cf748\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\424969147400a74365fabb16a0493ab4\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\424969147400a74365fabb16a0493ab4\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b50092f697a54e57b5b4addf47300be1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b50092f697a54e57b5b4addf47300be1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3c08e2954c0b005d7d9514a258acdb1d\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3c08e2954c0b005d7d9514a258acdb1d\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\0dad3ce32b912d3ad6c2ae2d9e21d723\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\0dad3ce32b912d3ad6c2ae2d9e21d723\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\7b9f67bcf8cd4b588a2628a4da09e3ab\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\7b9f67bcf8cd4b588a2628a4da09e3ab\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\31283cbfe3d64097c8f5b7e6f4f4d976\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\31283cbfe3d64097c8f5b7e6f4f4d976\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.10\transforms\4f4e04ea3b5ce5dce7cef890ce47e9ca\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.10\transforms\4f4e04ea3b5ce5dce7cef890ce47e9ca\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\d770a627ff7a74c800110608654bbbab\transformed\grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\d770a627ff7a74c800110608654bbbab\transformed\grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ced696821e2f20ee9b481b688367e7f4\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ced696821e2f20ee9b481b688367e7f4\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d4bdf51ef125aab1e5f2b0dcbac28f90\transformed\firebase-firestore-ktx-24.10.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d4bdf51ef125aab1e5f2b0dcbac28f90\transformed\firebase-firestore-ktx-24.10.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:10:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:9:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar
ADDED from [com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:12:17-119
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
uses-permission#com.android.vending.BILLING
ADDED from [com.android.billingclient:billing-ktx:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\658f12a736932d07849764a7d0cd2ae4\transformed\billing-ktx-6.1.0\AndroidManifest.xml:9:5-67
MERGED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:10:5-67
MERGED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:10:5-67
	android:name
		ADDED from [com.android.billingclient:billing-ktx:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\658f12a736932d07849764a7d0cd2ae4\transformed\billing-ktx-6.1.0\AndroidManifest.xml:9:22-64
queries
ADDED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:12:5-16:15
intent#action:name:com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:13:9-15:18
action#com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:14:13-91
	android:name
		ADDED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:14:21-88
activity#com.android.billingclient.api.ProxyBillingActivity
ADDED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:23:9-27:75
	android:exported
		ADDED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:26:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:25:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:24:13-78
meta-data#com.google.firebase.components:com.google.firebase.functions.ktx.FirebaseFunctionsLegacyRegistrar
ADDED from [com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
meta-data#com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar
ADDED from [com.google.firebase:firebase-firestore-ktx:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d4bdf51ef125aab1e5f2b0dcbac28f90\transformed\firebase-firestore-ktx-24.10.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d4bdf51ef125aab1e5f2b0dcbac28f90\transformed\firebase-firestore-ktx-24.10.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d4bdf51ef125aab1e5f2b0dcbac28f90\transformed\firebase-firestore-ktx-24.10.1\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.functions.FirebaseFunctionsKtxRegistrar
ADDED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:16:17-122
meta-data#com.google.firebase.components:com.google.firebase.functions.FunctionsRegistrar
ADDED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:19:17-111
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:21:17-111
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5743639ee258961f6df81f7fb972606d\transformed\play-services-cloud-messaging-16.0.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5743639ee258961f6df81f7fb972606d\transformed\play-services-cloud-messaging-16.0.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3cfb89e1f8b79699007d84b341f8e86d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3cfb89e1f8b79699007d84b341f8e86d\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2fc279c04a5e9883f604c0ae3de200f0\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
property
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5743639ee258961f6df81f7fb972606d\transformed\play-services-cloud-messaging-16.0.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5743639ee258961f6df81f7fb972606d\transformed\play-services-cloud-messaging-16.0.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:26:22-79
meta-data#com.google.firebase.components:com.google.firebase.iid.Registrar
ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\10ae2552bf3cbf45dc9cc601ea39674f\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\10ae2552bf3cbf45dc9cc601ea39674f\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\10ae2552bf3cbf45dc9cc601ea39674f\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9078f28263f278452faf1cbc8e846522\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9078f28263f278452faf1cbc8e846522\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9078f28263f278452faf1cbc8e846522\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\64f4d6b2a08f902b0fb0339d225cf748\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\64f4d6b2a08f902b0fb0339d225cf748\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db33cfb2e58b6d3432243990065bd6a0\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db33cfb2e58b6d3432243990065bd6a0\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d2d7761414972602d1b8641502e86e46\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.eligi.menteencalma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.eligi.menteencalma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
