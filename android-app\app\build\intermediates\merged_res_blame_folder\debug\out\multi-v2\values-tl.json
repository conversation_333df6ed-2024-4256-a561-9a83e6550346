{"logs": [{"outputFile": "com.menteencalma.app-mergeDebugResources-3:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\98bcb59e84dea2106bcefc907e055731\\transformed\\material3-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,301,419,540,639,739,856,1003,1130,1280,1365,1464,1559,1657,1778,1916,2020,2167,2315,2462,2632,2770,2893,3018,3143,3239,3338,3463,3598,3705,3809,3922,4067,4216,4332,4438,4514,4614,4711,4800,4889,4996,5076,5160,5260,5364,5464,5570,5658,5770,5875,5985,6104,6184,6291", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "174,296,414,535,634,734,851,998,1125,1275,1360,1459,1554,1652,1773,1911,2015,2162,2310,2457,2627,2765,2888,3013,3138,3234,3333,3458,3593,3700,3804,3917,4062,4211,4327,4433,4509,4609,4706,4795,4884,4991,5071,5155,5255,5359,5459,5565,5653,5765,5870,5980,6099,6179,6286,6381"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4273,4397,4519,4637,4758,4857,4957,5074,5221,5348,5498,5583,5682,5777,5875,5996,6134,6238,6385,6533,6680,6850,6988,7111,7236,7361,7457,7556,7681,7816,7923,8027,8140,8285,8434,8550,8656,8732,8832,8929,9018,9107,9214,9294,9378,9478,9582,9682,9788,9876,9988,10093,10203,10322,10402,10509", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "4392,4514,4632,4753,4852,4952,5069,5216,5343,5493,5578,5677,5772,5870,5991,6129,6233,6380,6528,6675,6845,6983,7106,7231,7356,7452,7551,7676,7811,7918,8022,8135,8280,8429,8545,8651,8727,8827,8924,9013,9102,9209,9289,9373,9473,9577,9677,9783,9871,9983,10088,10198,10317,10397,10504,10599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d60095cfcd5f6da5b72a1052224dd592\\transformed\\ui-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,387,489,579,661,753,845,929,999,1068,1155,1241,1312,1390,1456", "endColumns": "98,85,96,101,89,81,91,91,83,69,68,86,85,70,77,65,126", "endOffsets": "199,285,382,484,574,656,748,840,924,994,1063,1150,1236,1307,1385,1451,1578"}, "to": {"startLines": "9,10,30,31,32,36,37,94,95,96,97,98,99,100,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "839,938,3489,3586,3688,4099,4181,10604,10696,10780,10850,10919,11006,11092,11264,11342,11408", "endColumns": "98,85,96,101,89,81,91,91,83,69,68,86,85,70,77,65,126", "endOffsets": "933,1019,3581,3683,3773,4176,4268,10691,10775,10845,10914,11001,11087,11158,11337,11403,11530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\b7c5e14f30f524dc22cc8925fd453a32\\transformed\\core-1.12.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,405,502,609,717,11163", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "197,299,400,497,604,712,834,11259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\0a2f82cac04b16c6074a060829451b01\\transformed\\play-services-basement-18.2.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2083", "endColumns": "144", "endOffsets": "2223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ca611de3937d8b7fad81038213315e33\\transformed\\foundation-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,91", "endOffsets": "136,228"}, "to": {"startLines": "105,106", "startColumns": "4,4", "startOffsets": "11535,11621", "endColumns": "85,91", "endOffsets": "11616,11708"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ec6bd2faa3e0a998fc0a19c7dd0c22ed\\transformed\\browser-1.4.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,263,374", "endColumns": "102,104,110,104", "endOffsets": "153,258,369,474"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "3386,3778,3883,3994", "endColumns": "102,104,110,104", "endOffsets": "3484,3878,3989,4094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\bc87254a1d5a4bb9d2cd1a5740dba329\\transformed\\play-services-base-18.1.0\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,468,602,707,861,993,1111,1220,1395,1498,1672,1806,1964,2139,2203,2265", "endColumns": "102,171,133,104,153,131,117,108,174,102,173,133,157,174,63,61,76", "endOffsets": "295,467,601,706,860,992,1110,1219,1394,1497,1671,1805,1963,2138,2202,2264,2341"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1024,1131,1307,1445,1554,1712,1848,1970,2228,2407,2514,2692,2830,2992,3171,3239,3305", "endColumns": "106,175,137,108,157,135,121,112,178,106,177,137,161,178,67,65,80", "endOffsets": "1126,1302,1440,1549,1707,1843,1965,2078,2402,2509,2687,2825,2987,3166,3234,3300,3381"}}]}]}