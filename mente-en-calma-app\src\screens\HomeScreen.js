import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useSubscription } from '../contexts/SubscriptionContext';

const HomeScreen = ({ navigation }) => {
  const { user, userProfile, logout } = useAuth();
  const { isSubscriptionActive, getSubscriptionPlan } = useSubscription();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const subscriptionActive = isSubscriptionActive();
  const subscriptionPlan = getSubscriptionPlan();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.greeting}>
              <PERSON><PERSON>, {userProfile?.displayName || user?.email?.split('@')[0] || 'Usuario'}
            </Text>
            <Text style={styles.subtitle}>¿Cómo te sientes hoy?</Text>
          </View>
          <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
            <Text style={styles.logoutText}>Salir</Text>
          </TouchableOpacity>
        </View>

        {/* Subscription Status */}
        <View style={styles.subscriptionCard}>
          <Text style={styles.subscriptionTitle}>Estado de Suscripción</Text>
          <View style={styles.subscriptionStatus}>
            <View style={[
              styles.statusIndicator, 
              { backgroundColor: subscriptionActive ? '#4CAF50' : '#FF9800' }
            ]} />
            <Text style={styles.statusText}>
              {subscriptionActive ? 
                `Plan ${subscriptionPlan} Activo` : 
                'Plan Gratuito'
              }
            </Text>
          </View>
          {!subscriptionActive && (
            <TouchableOpacity 
              style={styles.upgradeButton}
              onPress={() => navigation.navigate('Subscription')}
            >
              <Text style={styles.upgradeButtonText}>Actualizar a Premium</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Acciones Rápidas</Text>
          <View style={styles.actionsGrid}>
            <TouchableOpacity style={styles.actionCard}>
              <Text style={styles.actionIcon}>🧘‍♀️</Text>
              <Text style={styles.actionTitle}>Meditación</Text>
              <Text style={styles.actionSubtitle}>Encuentra tu paz</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionCard}>
              <Text style={styles.actionIcon}>😌</Text>
              <Text style={styles.actionTitle}>Relajación</Text>
              <Text style={styles.actionSubtitle}>Libera el estrés</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionCard}>
              <Text style={styles.actionIcon}>💭</Text>
              <Text style={styles.actionTitle}>Reflexión</Text>
              <Text style={styles.actionSubtitle}>Conoce tus pensamientos</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionCard}>
              <Text style={styles.actionIcon}>📝</Text>
              <Text style={styles.actionTitle}>Diario</Text>
              <Text style={styles.actionSubtitle}>Escribe tus emociones</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Premium Features */}
        {subscriptionActive && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contenido Premium</Text>
            <View style={styles.premiumCard}>
              <Text style={styles.premiumIcon}>⭐</Text>
              <Text style={styles.premiumTitle}>Sesiones Exclusivas</Text>
              <Text style={styles.premiumSubtitle}>
                Accede a meditaciones guiadas premium y contenido exclusivo
              </Text>
            </View>
          </View>
        )}

        {/* Daily Quote */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Reflexión del Día</Text>
          <View style={styles.quoteCard}>
            <Text style={styles.quote}>
              "La paz viene de dentro. No la busques fuera."
            </Text>
            <Text style={styles.quoteAuthor}>- Buda</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  headerContent: {
    flex: 1,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  logoutButton: {
    padding: 8,
  },
  logoutText: {
    color: '#4A90E2',
    fontSize: 16,
  },
  subscriptionCard: {
    backgroundColor: 'white',
    margin: 20,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  subscriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subscriptionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 10,
  },
  statusText: {
    fontSize: 16,
    color: '#333',
  },
  upgradeButton: {
    backgroundColor: '#4A90E2',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  upgradeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  section: {
    margin: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    backgroundColor: 'white',
    width: '48%',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionIcon: {
    fontSize: 30,
    marginBottom: 10,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  actionSubtitle: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  premiumCard: {
    backgroundColor: '#FFF3E0',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFB74D',
  },
  premiumIcon: {
    fontSize: 40,
    marginBottom: 10,
  },
  premiumTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#E65100',
    marginBottom: 8,
  },
  premiumSubtitle: {
    fontSize: 14,
    color: '#BF360C',
    textAlign: 'center',
  },
  quoteCard: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quote: {
    fontSize: 16,
    fontStyle: 'italic',
    color: '#333',
    textAlign: 'center',
    marginBottom: 10,
  },
  quoteAuthor: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});

export default HomeScreen;
