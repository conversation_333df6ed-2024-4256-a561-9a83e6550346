package com.menteencalma.app.presentation.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.menteencalma.app.domain.model.User
import com.menteencalma.app.domain.repository.DatabaseRepository
import com.menteencalma.app.domain.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class SubscribeUiState(
    val currentUser: User? = null,
    val isLoading: Boolean = true,
    val isPurchasing: Boolean = false,
    val errorMessage: String? = null,
    val purchaseSuccess: Boolean = false,
    val selectedPlan: SubscriptionPlan? = null,
    val availablePlans: List<SubscriptionPlan> = emptyList()
)

data class SubscriptionPlan(
    val id: String,
    val name: String,
    val price: String,
    val originalPrice: String? = null, // Para mostrar descuentos
    val billingPeriod: String,
    val features: List<SubscriptionFeature>,
    val isPopular: Boolean = false,
    val savings: String? = null, // Ej: "Ahorra 20%"
    val pricePerMonth: String? = null // Para planes anuales
)

data class SubscriptionFeature(
    val name: String,
    val freeLimit: String,
    val premiumLimit: String,
    val isAvailable: Boolean = true
)

@HiltViewModel
class SubscribeViewModel @Inject constructor(
    private val databaseRepository: DatabaseRepository,
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(SubscribeUiState())
    val uiState: StateFlow<SubscribeUiState> = _uiState.asStateFlow()

    init {
        loadUserAndPlans()
    }

    private fun loadUserAndPlans() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
                
                val firebaseUser = authRepository.getCurrentUser()
                if (firebaseUser != null) {
                    val userResult = databaseRepository.getUser(firebaseUser.uid)
                    
                    if (userResult.isSuccess) {
                        val user = userResult.getOrNull()
                        _uiState.value = _uiState.value.copy(
                            currentUser = user,
                            availablePlans = getAvailableSubscriptionPlans(),
                            isLoading = false
                        )
                    } else {
                        _uiState.value = _uiState.value.copy(
                            errorMessage = "Error al cargar el usuario: ${userResult.exceptionOrNull()?.message}",
                            isLoading = false
                        )
                    }
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Usuario no autenticado",
                        isLoading = false
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error inesperado: ${e.message}",
                    isLoading = false
                )
            }
        }
    }

    private fun getAvailableSubscriptionPlans(): List<SubscriptionPlan> {
        val features = listOf(
            SubscriptionFeature(
                name = "Chat con IA",
                freeLimit = "10 mensajes/día",
                premiumLimit = "Ilimitado"
            ),
            SubscriptionFeature(
                name = "Recomendaciones",
                freeLimit = "3/día",
                premiumLimit = "Ilimitado"
            ),
            SubscriptionFeature(
                name = "Artículos generados",
                freeLimit = "2/día",
                premiumLimit = "Ilimitado"
            ),
            SubscriptionFeature(
                name = "Artículos guardados",
                freeLimit = "5 artículos",
                premiumLimit = "Ilimitado"
            ),
            SubscriptionFeature(
                name = "Análisis de progreso",
                freeLimit = "Básico",
                premiumLimit = "Avanzado"
            ),
            SubscriptionFeature(
                name = "Soporte prioritario",
                freeLimit = "No disponible",
                premiumLimit = "24/7"
            ),
            SubscriptionFeature(
                name = "Contenido exclusivo",
                freeLimit = "No disponible",
                premiumLimit = "Acceso completo"
            ),
            SubscriptionFeature(
                name = "Modo sin conexión",
                freeLimit = "No disponible",
                premiumLimit = "Disponible"
            )
        )

        return listOf(
            SubscriptionPlan(
                id = "premium_monthly",
                name = "Mensual",
                price = "$9.99",
                billingPeriod = "por mes",
                features = features,
                isPopular = false
            ),
            SubscriptionPlan(
                id = "premium_annual",
                name = "Anual",
                price = "$79.99",
                originalPrice = "$119.88",
                billingPeriod = "por año",
                features = features,
                isPopular = true,
                savings = "Ahorra 33%",
                pricePerMonth = "$6.67/mes"
            )
        )
    }

    fun selectPlan(plan: SubscriptionPlan) {
        _uiState.value = _uiState.value.copy(selectedPlan = plan)
    }

    fun purchasePlan(plan: SubscriptionPlan) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isPurchasing = true,
                    errorMessage = null,
                    purchaseSuccess = false
                )

                // TODO: Integrar con RevenueCat o Google Play Billing
                // Por ahora, simulamos la compra
                simulatePurchase(plan)

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error en la compra: ${e.message}",
                    isPurchasing = false
                )
            }
        }
    }

    private suspend fun simulatePurchase(plan: SubscriptionPlan) {
        try {
            // Simular delay de procesamiento
            kotlinx.coroutines.delay(2000)
            
            // TODO: Aquí iría la lógica real de RevenueCat
            // 1. Iniciar compra con RevenueCat
            // 2. Verificar el recibo
            // 3. Actualizar el usuario en Firestore
            
            // Por ahora, solo logueamos y mostramos éxito
            println("🛒 PURCHASE SIMULATION:")
            println("   Plan: ${plan.name}")
            println("   Price: ${plan.price}")
            println("   User: ${_uiState.value.currentUser?.email}")
            println("   TODO: Integrate with RevenueCat")
            
            // Simular actualización del usuario (en producción esto vendría del webhook)
            val currentUser = _uiState.value.currentUser
            if (currentUser != null) {
                val updatedUser = currentUser.copy(
                    subscriptionStatus = "premium",
                    subscriptionPlan = plan.id,
                    subscriptionPlatform = "google_play", // o "app_store"
                    subscriptionExpiresAt = if (plan.id.contains("annual")) {
                        System.currentTimeMillis() + (365L * 24 * 60 * 60 * 1000) // 1 año
                    } else {
                        System.currentTimeMillis() + (30L * 24 * 60 * 60 * 1000) // 1 mes
                    },
                    updatedAt = System.currentTimeMillis()
                )
                
                val result = databaseRepository.updateUser(updatedUser)
                if (result.isSuccess) {
                    _uiState.value = _uiState.value.copy(
                        currentUser = updatedUser,
                        isPurchasing = false,
                        purchaseSuccess = true
                    )
                } else {
                    throw Exception("Error al actualizar la suscripción en la base de datos")
                }
            } else {
                throw Exception("Usuario no encontrado")
            }
            
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "Error en la compra: ${e.message}",
                isPurchasing = false
            )
        }
    }

    fun restorePurchases() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isPurchasing = true,
                    errorMessage = null
                )

                // TODO: Implementar restauración de compras con RevenueCat
                kotlinx.coroutines.delay(1500)
                
                println("🔄 RESTORE PURCHASES SIMULATION:")
                println("   TODO: Integrate with RevenueCat.restorePurchases()")
                
                _uiState.value = _uiState.value.copy(
                    isPurchasing = false,
                    errorMessage = "No se encontraron compras anteriores"
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error al restaurar compras: ${e.message}",
                    isPurchasing = false
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun clearPurchaseSuccess() {
        _uiState.value = _uiState.value.copy(purchaseSuccess = false)
    }

    fun isUserSubscribed(): Boolean {
        return _uiState.value.currentUser?.hasActiveSubscription() ?: false
    }

    fun getCurrentSubscriptionPlan(): String? {
        return _uiState.value.currentUser?.subscriptionPlan
    }

    fun getSubscriptionFeatures(): List<SubscriptionFeature> {
        return getAvailableSubscriptionPlans().firstOrNull()?.features ?: emptyList()
    }

    // Método para integración futura con RevenueCat
    fun initializeRevenueCat() {
        // TODO: Configurar RevenueCat
        /*
        Purchases.configure(
            PurchasesConfiguration.Builder(context, "your_revenuecat_api_key")
                .appUserID(currentUser?.id)
                .build()
        )
        */
        println("📱 TODO: Initialize RevenueCat with API key")
    }

    // Método para manejar el resultado de compras de RevenueCat
    fun handlePurchaseResult(
        purchaseResult: Any?, // CustomerInfo en RevenueCat
        error: String?
    ) {
        if (error != null) {
            _uiState.value = _uiState.value.copy(
                errorMessage = error,
                isPurchasing = false
            )
        } else {
            // TODO: Procesar el resultado de RevenueCat
            _uiState.value = _uiState.value.copy(
                isPurchasing = false,
                purchaseSuccess = true
            )
        }
    }
}
