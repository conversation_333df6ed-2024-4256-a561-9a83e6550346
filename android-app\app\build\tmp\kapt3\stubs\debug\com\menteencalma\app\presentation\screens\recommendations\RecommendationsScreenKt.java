package com.menteencalma.app.presentation.screens.recommendations;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000:\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\u001a\u001e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a\u001e\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a.\u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\b2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a*\u0010\u0010\u001a\u00020\u00012\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\f0\u00122\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u0014H\u0003\u001a\u001e\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\f2\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a\b\u0010\u0017\u001a\u00020\u0001H\u0003\u001a \u0010\u0018\u001a\u00020\u00012\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0019\u001a\u00020\u001aH\u0007\u00a8\u0006\u001b"}, d2 = {"ErrorCard", "", "message", "", "onDismiss", "Lkotlin/Function0;", "GetRecommendationButton", "isLoading", "", "onGetRecommendation", "RecommendationCard", "recommendation", "Lcom/menteencalma/app/domain/model/Recommendation;", "isPersonalized", "isUserPremium", "onNavigateToSubscribe", "RecommendationHistory", "recommendations", "", "onRecommendationClick", "Lkotlin/Function1;", "RecommendationHistoryItem", "onClick", "RecommendationsHeader", "RecommendationsScreen", "recommendationsViewModel", "Lcom/menteencalma/app/presentation/screens/recommendations/RecommendationsViewModel;", "app_debug"})
public final class RecommendationsScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void RecommendationsScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSubscribe, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel recommendationsViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void RecommendationsHeader() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void GetRecommendationButton(boolean isLoading, kotlin.jvm.functions.Function0<kotlin.Unit> onGetRecommendation) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void RecommendationCard(com.menteencalma.app.domain.model.Recommendation recommendation, boolean isPersonalized, boolean isUserPremium, kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSubscribe) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void RecommendationHistory(java.util.List<com.menteencalma.app.domain.model.Recommendation> recommendations, kotlin.jvm.functions.Function1<? super com.menteencalma.app.domain.model.Recommendation, kotlin.Unit> onRecommendationClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void RecommendationHistoryItem(com.menteencalma.app.domain.model.Recommendation recommendation, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ErrorCard(java.lang.String message, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
}