package com.menteencalma.app.presentation.screens.articles;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000v\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001B\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u0006\u0010\u0014\u001a\u00020\u0015J\u0006\u0010\u0016\u001a\u00020\u0015J\u000e\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u0019J\"\u0010\u001a\u001a\u00020\u00152\u0006\u0010\u001b\u001a\u00020\u00192\b\b\u0002\u0010\u001c\u001a\u00020\u00192\b\b\u0002\u0010\u001d\u001a\u00020\u0019J\u0006\u0010\u001e\u001a\u00020\u001fJ\u0014\u0010 \u001a\b\u0012\u0004\u0012\u00020\"0!2\u0006\u0010#\u001a\u00020\u0019J\u0006\u0010$\u001a\u00020%J\u0016\u0010&\u001a\b\u0012\u0004\u0012\u00020\"0!2\b\b\u0002\u0010\'\u001a\u00020%J\u0006\u0010(\u001a\u00020%J\u0006\u0010)\u001a\u00020*J\b\u0010+\u001a\u00020\u0015H\u0002J\b\u0010,\u001a\u00020\u0015H\u0002J\u001c\u0010-\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\"0!0.2\u0006\u0010/\u001a\u00020\u0019H\u0002J\b\u00100\u001a\u00020\u0015H\u0014J\u0006\u00101\u001a\u00020\u0015J\u0014\u00102\u001a\b\u0012\u0004\u0012\u00020\"0!2\u0006\u00103\u001a\u00020\u0019R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\r0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013\u00a8\u00064"}, d2 = {"Lcom/menteencalma/app/presentation/screens/articles/ArticlesViewModel;", "Landroidx/lifecycle/ViewModel;", "cloudFunctionsService", "Lcom/menteencalma/app/data/service/CloudFunctionsService;", "databaseRepository", "Lcom/menteencalma/app/domain/repository/DatabaseRepository;", "authRepository", "Lcom/menteencalma/app/domain/repository/AuthRepository;", "firestore", "Lcom/google/firebase/firestore/FirebaseFirestore;", "(Lcom/menteencalma/app/data/service/CloudFunctionsService;Lcom/menteencalma/app/domain/repository/DatabaseRepository;Lcom/menteencalma/app/domain/repository/AuthRepository;Lcom/google/firebase/firestore/FirebaseFirestore;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/menteencalma/app/presentation/screens/articles/ArticlesUiState;", "savedArticlesListener", "Lcom/google/firebase/firestore/ListenerRegistration;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "clearError", "", "clearGeneratedArticle", "deleteSavedArticle", "articleId", "", "generateArticle", "topic", "difficulty", "length", "getArticleStats", "Lcom/menteencalma/app/presentation/screens/articles/ArticleStats;", "getArticlesByCategory", "", "Lcom/menteencalma/app/domain/model/GeneratedArticle;", "category", "getDailyArticleLimit", "", "getRecentArticles", "limit", "getTotalReadTime", "isUserPremium", "", "loadCurrentUser", "observeSavedArticles", "observeSavedArticlesFlow", "Lkotlinx/coroutines/flow/Flow;", "userId", "onCleared", "saveGeneratedArticle", "searchArticles", "query", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class ArticlesViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.data.service.CloudFunctionsService cloudFunctionsService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.repository.DatabaseRepository databaseRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.repository.AuthRepository authRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.firebase.firestore.FirebaseFirestore firestore = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.menteencalma.app.presentation.screens.articles.ArticlesUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.screens.articles.ArticlesUiState> uiState = null;
    @org.jetbrains.annotations.Nullable()
    private com.google.firebase.firestore.ListenerRegistration savedArticlesListener;
    
    @javax.inject.Inject()
    public ArticlesViewModel(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.data.service.CloudFunctionsService cloudFunctionsService, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.DatabaseRepository databaseRepository, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.AuthRepository authRepository, @org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.screens.articles.ArticlesUiState> getUiState() {
        return null;
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
    
    private final void loadCurrentUser() {
    }
    
    private final void observeSavedArticles() {
    }
    
    private final kotlinx.coroutines.flow.Flow<java.util.List<com.menteencalma.app.domain.model.GeneratedArticle>> observeSavedArticlesFlow(java.lang.String userId) {
        return null;
    }
    
    public final void generateArticle(@org.jetbrains.annotations.NotNull()
    java.lang.String topic, @org.jetbrains.annotations.NotNull()
    java.lang.String difficulty, @org.jetbrains.annotations.NotNull()
    java.lang.String length) {
    }
    
    public final void saveGeneratedArticle() {
    }
    
    public final void deleteSavedArticle(@org.jetbrains.annotations.NotNull()
    java.lang.String articleId) {
    }
    
    public final void clearGeneratedArticle() {
    }
    
    public final void clearError() {
    }
    
    public final boolean isUserPremium() {
        return false;
    }
    
    public final int getDailyArticleLimit() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.domain.model.GeneratedArticle> getArticlesByCategory(@org.jetbrains.annotations.NotNull()
    java.lang.String category) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.domain.model.GeneratedArticle> searchArticles(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.domain.model.GeneratedArticle> getRecentArticles(int limit) {
        return null;
    }
    
    public final int getTotalReadTime() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.presentation.screens.articles.ArticleStats getArticleStats() {
        return null;
    }
}