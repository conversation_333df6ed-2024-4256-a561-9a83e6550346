#!/usr/bin/env python3
"""
Script temporal para crear iconos PNG básicos para la app Android
"""

import os
from PIL import Image, ImageDraw

def create_icon(size, output_path):
    """Crear un icono simple con el tamaño especificado"""
    # Crear imagen con fondo transparente
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Color principal de la app
    primary_color = (103, 80, 164, 255)  # #6750A4
    light_color = (234, 221, 255, 255)   # #EADDFF
    
    # Dibujar círculo de fondo
    margin = size // 8
    draw.ellipse([margin, margin, size-margin, size-margin], fill=primary_color)
    
    # Dibujar círculo interior
    inner_margin = size // 4
    draw.ellipse([inner_margin, inner_margin, size-inner_margin, size-inner_margin], fill=light_color)
    
    # Dibujar símbolo simple (círculo central)
    center_margin = size // 2.5
    draw.ellipse([center_margin, center_margin, size-center_margin, size-center_margin], fill=primary_color)
    
    # Guardar imagen
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    img.save(output_path, 'PNG')
    print(f"Creado: {output_path}")

def main():
    """Crear todos los iconos necesarios"""
    base_path = "android-app/app/src/main/res"
    
    # Definir tamaños para diferentes densidades
    icon_sizes = {
        'mipmap-mdpi': 48,
        'mipmap-hdpi': 72,
        'mipmap-xhdpi': 96,
        'mipmap-xxhdpi': 144,
        'mipmap-xxxhdpi': 192
    }
    
    for density, size in icon_sizes.items():
        # Crear ic_launcher.png
        create_icon(size, f"{base_path}/{density}/ic_launcher.png")
        # Crear ic_launcher_round.png (mismo diseño)
        create_icon(size, f"{base_path}/{density}/ic_launcher_round.png")

if __name__ == "__main__":
    try:
        main()
        print("\n✅ Todos los iconos creados exitosamente!")
        print("Ahora puedes compilar la app en Android Studio.")
    except ImportError:
        print("❌ Error: PIL (Pillow) no está instalado.")
        print("Instala con: pip install Pillow")
        print("\nAlternativamente, usaremos iconos XML temporales.")
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Usaremos iconos XML temporales en su lugar.")
