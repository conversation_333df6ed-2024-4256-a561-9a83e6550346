package com.menteencalma.app.presentation.screens.recommendations

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.menteencalma.app.R
import com.menteencalma.app.domain.model.Recommendation

@Composable
fun RecommendationsScreen(
    onNavigateToSubscribe: () -> Unit,
    recommendationsViewModel: RecommendationsViewModel = hiltViewModel()
) {
    val uiState by recommendationsViewModel.uiState.collectAsState()
    val scrollState = rememberScrollState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        RecommendationsHeader()

        // Get Recommendation Button
        GetRecommendationButton(
            isLoading = uiState.isLoading,
            onGetRecommendation = { recommendationsViewModel.getRecommendation() }
        )

        // Current Recommendation Card
        uiState.currentRecommendation?.let { recommendation ->
            RecommendationCard(
                recommendation = recommendation,
                isPersonalized = uiState.isPersonalized,
                isUserPremium = recommendationsViewModel.isUserPremium(),
                onNavigateToSubscribe = onNavigateToSubscribe
            )
        }

        // Recommendation History
        if (uiState.recommendationHistory.isNotEmpty()) {
            RecommendationHistory(
                recommendations = uiState.recommendationHistory,
                onRecommendationClick = { /* TODO: Show recommendation detail */ }
            )
        }

        // Error Message
        uiState.errorMessage?.let { error ->
            ErrorCard(
                message = error,
                onDismiss = { recommendationsViewModel.clearError() }
            )
        }
    }
}

@Composable
private fun RecommendationsHeader() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.Lightbulb,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier
                .size(48.dp)
                .padding(bottom = 8.dp)
        )

        Text(
            text = stringResource(R.string.recommendations_title),
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Text(
            text = stringResource(R.string.recommendations_description),
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}

@Composable
private fun GetRecommendationButton(
    isLoading: Boolean,
    onGetRecommendation: () -> Unit
) {
    Button(
        onClick = onGetRecommendation,
        modifier = Modifier.fillMaxWidth(),
        enabled = !isLoading,
        shape = RoundedCornerShape(12.dp)
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                color = MaterialTheme.colorScheme.onPrimary
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("Generando...")
        } else {
            Icon(Icons.Default.AutoAwesome, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text("Obtener Recomendación")
        }
    }
}

@Composable
private fun RecommendationCard(
    recommendation: Recommendation,
    isPersonalized: Boolean,
    isUserPremium: Boolean,
    onNavigateToSubscribe: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header with icon and title
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = if (isPersonalized) Icons.Default.Star else Icons.Default.Bolt,
                    contentDescription = null,
                    tint = if (isPersonalized)
                        MaterialTheme.colorScheme.primary
                    else MaterialTheme.colorScheme.secondary
                )

                Text(
                    text = if (isPersonalized) "Recomendación Personalizada" else "Un Consejo para Ti",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = if (isPersonalized)
                        MaterialTheme.colorScheme.primary
                    else MaterialTheme.colorScheme.secondary
                )
            }

            // Subscription link for free users
            if (!isUserPremium) {
                TextButton(
                    onClick = onNavigateToSubscribe,
                    modifier = Modifier.padding(vertical = 4.dp)
                ) {
                    Text(
                        text = "Obtén recomendaciones ilimitadas con Premium",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Icon(
                        Icons.Default.ArrowForward,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Recommendation title
            Text(
                text = recommendation.title,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Recommendation content
            Text(
                text = recommendation.content,
                style = MaterialTheme.typography.bodyMedium,
                lineHeight = MaterialTheme.typography.bodyMedium.lineHeight
            )

            Spacer(modifier = Modifier.height(12.dp))

            // Metadata
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Category and duration
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AssistChip(
                        onClick = { },
                        label = { Text(recommendation.category.replaceFirstChar { it.uppercase() }) }
                    )

                    if (recommendation.estimatedDuration > 0) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            Icon(
                                Icons.Default.Schedule,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp),
                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = "${recommendation.estimatedDuration} min",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }

                // Action buttons
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    IconButton(onClick = { /* TODO: Share recommendation */ }) {
                        Icon(Icons.Default.Share, contentDescription = "Compartir")
                    }

                    IconButton(onClick = { /* TODO: Save to favorites */ }) {
                        Icon(Icons.Default.FavoriteBorder, contentDescription = "Guardar")
                    }
                }
            }
        }
    }
}

@Composable
private fun RecommendationHistory(
    recommendations: List<Recommendation>,
    onRecommendationClick: (Recommendation) -> Unit
) {
    Column {
        Text(
            text = "Recomendaciones Anteriores",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        recommendations.take(5).forEach { recommendation ->
            RecommendationHistoryItem(
                recommendation = recommendation,
                onClick = { onRecommendationClick(recommendation) }
            )
        }

        if (recommendations.size > 5) {
            TextButton(
                onClick = { /* TODO: Show all recommendations */ },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Ver todas las recomendaciones")
                Spacer(modifier = Modifier.width(4.dp))
                Icon(
                    Icons.Default.ArrowForward,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

@Composable
private fun RecommendationHistoryItem(
    recommendation: Recommendation,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = if (recommendation.isPersonalized) Icons.Default.Star else Icons.Default.Bolt,
                contentDescription = null,
                tint = if (recommendation.isPersonalized)
                    MaterialTheme.colorScheme.primary
                else MaterialTheme.colorScheme.secondary,
                modifier = Modifier.size(20.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = recommendation.title,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1
                )

                Text(
                    text = recommendation.category.replaceFirstChar { it.uppercase() },
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Text(
                text = java.text.SimpleDateFormat("dd/MM", java.util.Locale.getDefault())
                    .format(java.util.Date(recommendation.createdAt)),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun ErrorCard(
    message: String,
    onDismiss: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                Icons.Default.Error,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onErrorContainer
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onErrorContainer,
                modifier = Modifier.weight(1f)
            )

            IconButton(onClick = onDismiss) {
                Icon(
                    Icons.Default.Close,
                    contentDescription = "Cerrar",
                    tint = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
    }
}
