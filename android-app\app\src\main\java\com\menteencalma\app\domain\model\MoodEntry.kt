package com.menteencalma.app.domain.model

import kotlinx.serialization.Serializable

/**
 * Modelo para entradas de seguimiento de ánimo
 */
@Serializable
data class MoodEntry(
    val id: String = "",
    val userId: String = "",
    val mood: MoodType = MoodType.NEUTRAL,
    val intensity: Int = 5, // 1-10 scale
    val description: String = "",
    val timestamp: Long = System.currentTimeMillis(),
    val date: String = "", // Format: "yyyy-MM-dd" for easy grouping
    val tags: List<String> = emptyList(),
    val activities: List<String> = emptyList(), // What the user was doing
    val location: String? = null, // Optional location context
    val weather: String? = null, // Optional weather context
    
    // Metadata for analytics
    val metadata: MoodMetadata? = null
) {
    
    @Serializable
    data class MoodMetadata(
        val appVersion: String = "1.0.0",
        val deviceType: String = "android",
        val entryMethod: String = "manual", // manual, reminder, quick_entry
        val sessionDuration: Long = 0, // Time spent on entry in ms
        val previousMood: MoodType? = null,
        val moodTrend: String? = null, // improving, declining, stable
        val customData: Map<String, String> = emptyMap()
    )
    
    enum class MoodType(val displayName: String, val emoji: String, val color: String) {
        VERY_SAD("Muy triste", "😢", "#FF5252"),
        SAD("Triste", "😔", "#FF7043"),
        NEUTRAL("Neutral", "😐", "#FFC107"),
        HAPPY("Feliz", "😊", "#66BB6A"),
        VERY_HAPPY("Muy feliz", "😄", "#4CAF50");
        
        companion object {
            fun fromIntensity(intensity: Int): MoodType {
                return when (intensity) {
                    1, 2 -> VERY_SAD
                    3, 4 -> SAD
                    5, 6 -> NEUTRAL
                    7, 8 -> HAPPY
                    9, 10 -> VERY_HAPPY
                    else -> NEUTRAL
                }
            }
        }
    }
    
    companion object {
        const val COLLECTION_NAME = "mood_entries"
        const val SUBCOLLECTION_NAME = "moodEntries"
        
        // Predefined activities
        val COMMON_ACTIVITIES = listOf(
            "Trabajo", "Ejercicio", "Familia", "Amigos", "Estudio",
            "Relajación", "Hobbies", "Comida", "Viaje", "Descanso",
            "Meditación", "Lectura", "Música", "Naturaleza", "Casa"
        )
        
        // Predefined tags for mood context
        val COMMON_TAGS = listOf(
            "Estrés", "Ansiedad", "Alegría", "Cansancio", "Energía",
            "Motivación", "Gratitud", "Preocupación", "Paz", "Frustración",
            "Esperanza", "Soledad", "Conexión", "Logro", "Desafío"
        )
        
        // SQL Schema for future migration
        const val SQL_SCHEMA = """
            CREATE TABLE mood_entries (
                id VARCHAR(255) PRIMARY KEY,
                user_id VARCHAR(255) NOT NULL,
                mood VARCHAR(50) NOT NULL,
                intensity INTEGER NOT NULL CHECK (intensity >= 1 AND intensity <= 10),
                description TEXT,
                timestamp BIGINT NOT NULL,
                date VARCHAR(10) NOT NULL,
                tags TEXT[], -- JSON array for compatibility
                activities TEXT[], -- JSON array for compatibility
                location VARCHAR(255),
                weather VARCHAR(100),
                metadata JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            );
            
            CREATE INDEX idx_mood_entries_user_date ON mood_entries (user_id, date DESC);
            CREATE INDEX idx_mood_entries_timestamp ON mood_entries (user_id, timestamp DESC);
            CREATE INDEX idx_mood_entries_mood ON mood_entries (user_id, mood, timestamp DESC);
        """
    }
    
    /**
     * Convierte a formato para Firestore
     */
    fun toFirestoreMap(): Map<String, Any?> = mapOf(
        "id" to id,
        "userId" to userId,
        "mood" to mood.name,
        "intensity" to intensity,
        "description" to description,
        "timestamp" to timestamp,
        "date" to date,
        "tags" to tags,
        "activities" to activities,
        "location" to location,
        "weather" to weather,
        "metadata" to metadata?.let { meta ->
            mapOf(
                "appVersion" to meta.appVersion,
                "deviceType" to meta.deviceType,
                "entryMethod" to meta.entryMethod,
                "sessionDuration" to meta.sessionDuration,
                "previousMood" to meta.previousMood?.name,
                "moodTrend" to meta.moodTrend,
                "customData" to meta.customData
            )
        }
    )
    
    /**
     * Convierte a formato SQL
     */
    fun toSQLMap(): Map<String, Any?> = mapOf(
        "id" to id,
        "user_id" to userId,
        "mood" to mood.name,
        "intensity" to intensity,
        "description" to description,
        "timestamp" to timestamp,
        "date" to date,
        "tags" to tags.joinToString(","), // Simple comma-separated for basic SQL
        "activities" to activities.joinToString(","),
        "location" to location,
        "weather" to weather,
        "metadata" to metadata?.let { kotlinx.serialization.json.Json.encodeToString(it) }
    )
    
    /**
     * Valida la entrada de ánimo
     */
    fun validate(): Result<Unit> {
        return when {
            id.isBlank() -> Result.failure(IllegalArgumentException("Mood entry ID cannot be blank"))
            userId.isBlank() -> Result.failure(IllegalArgumentException("User ID cannot be blank"))
            intensity !in 1..10 -> Result.failure(IllegalArgumentException("Intensity must be between 1 and 10"))
            date.isBlank() -> Result.failure(IllegalArgumentException("Date cannot be blank"))
            timestamp <= 0 -> Result.failure(IllegalArgumentException("Timestamp must be positive"))
            else -> Result.success(Unit)
        }
    }
    
    /**
     * Obtiene el color del ánimo como Int para Compose
     */
    fun getMoodColorInt(): Long {
        return android.graphics.Color.parseColor(mood.color).toLong() or 0xFF000000
    }
    
    /**
     * Obtiene una descripción resumida para mostrar en listas
     */
    fun getSummary(): String {
        val moodText = "${mood.emoji} ${mood.displayName}"
        val intensityText = "Intensidad: $intensity/10"
        return if (description.isNotBlank()) {
            "$moodText • $intensityText • ${description.take(50)}${if (description.length > 50) "..." else ""}"
        } else {
            "$moodText • $intensityText"
        }
    }
    
    /**
     * Obtiene el tiempo relativo (ej: "Hace 2 horas")
     */
    fun getRelativeTime(): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < 60_000 -> "Hace un momento"
            diff < 3_600_000 -> "Hace ${diff / 60_000} min"
            diff < 86_400_000 -> "Hace ${diff / 3_600_000} h"
            diff < 604_800_000 -> "Hace ${diff / 86_400_000} días"
            else -> {
                val formatter = java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale.getDefault())
                formatter.format(java.util.Date(timestamp))
            }
        }
    }
}
