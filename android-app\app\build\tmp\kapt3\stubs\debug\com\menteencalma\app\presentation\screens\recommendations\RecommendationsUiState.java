package com.menteencalma.app.presentation.screens.recommendations;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0016\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BW\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0007\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0007\u0012\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00050\r\u00a2\u0006\u0002\u0010\u000eJ\u000b\u0010\u0019\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0007H\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0007H\u00c6\u0003J\u000f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00050\rH\u00c6\u0003J[\u0010 \u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\b\b\u0002\u0010\u000b\u001a\u00020\u00072\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00050\rH\u00c6\u0001J\u0013\u0010!\u001a\u00020\u00072\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010#\u001a\u00020$H\u00d6\u0001J\t\u0010%\u001a\u00020\nH\u00d6\u0001R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0013\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u000b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0016R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0016R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00050\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018\u00a8\u0006&"}, d2 = {"Lcom/menteencalma/app/presentation/screens/recommendations/RecommendationsUiState;", "", "currentUser", "Lcom/menteencalma/app/domain/model/User;", "currentRecommendation", "Lcom/menteencalma/app/domain/model/Recommendation;", "isLoading", "", "isPersonalized", "errorMessage", "", "hasReachedLimit", "recommendationHistory", "", "(Lcom/menteencalma/app/domain/model/User;Lcom/menteencalma/app/domain/model/Recommendation;ZZLjava/lang/String;ZLjava/util/List;)V", "getCurrentRecommendation", "()Lcom/menteencalma/app/domain/model/Recommendation;", "getCurrentUser", "()Lcom/menteencalma/app/domain/model/User;", "getErrorMessage", "()Ljava/lang/String;", "getHasReachedLimit", "()Z", "getRecommendationHistory", "()Ljava/util/List;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class RecommendationsUiState {
    @org.jetbrains.annotations.Nullable()
    private final com.menteencalma.app.domain.model.User currentUser = null;
    @org.jetbrains.annotations.Nullable()
    private final com.menteencalma.app.domain.model.Recommendation currentRecommendation = null;
    private final boolean isLoading = false;
    private final boolean isPersonalized = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    private final boolean hasReachedLimit = false;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.menteencalma.app.domain.model.Recommendation> recommendationHistory = null;
    
    public RecommendationsUiState(@org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.User currentUser, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.Recommendation currentRecommendation, boolean isLoading, boolean isPersonalized, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, boolean hasReachedLimit, @org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.domain.model.Recommendation> recommendationHistory) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.User getCurrentUser() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.Recommendation getCurrentRecommendation() {
        return null;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isPersonalized() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    public final boolean getHasReachedLimit() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.domain.model.Recommendation> getRecommendationHistory() {
        return null;
    }
    
    public RecommendationsUiState() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.User component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.Recommendation component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.domain.model.Recommendation> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.presentation.screens.recommendations.RecommendationsUiState copy(@org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.User currentUser, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.Recommendation currentRecommendation, boolean isLoading, boolean isPersonalized, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, boolean hasReachedLimit, @org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.domain.model.Recommendation> recommendationHistory) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}