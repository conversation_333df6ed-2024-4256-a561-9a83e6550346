package com.menteencalma.app.navigation;

/**
 * Sealed class que define todas las pantallas de la aplicación
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u0000 \n2\u00020\u0001:\u000e\u0007\b\t\n\u000b\f\r\u000e\u000f\u0010\u0011\u0012\u0013\u0014B\u000f\b\u0004\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u0082\u0001\r\u0015\u0016\u0017\u0018\u0019\u001a\u001b\u001c\u001d\u001e\u001f !\u00a8\u0006\""}, d2 = {"Lcom/menteencalma/app/navigation/Screen;", "", "route", "", "(Ljava/lang/String;)V", "getRoute", "()Ljava/lang/String;", "ArticleDetail", "Articles", "Chat", "Companion", "CreateProfile", "Disclaimer", "Doctors", "Login", "MoodTracker", "Profile", "Recommendations", "Settings", "Splash", "Subscribe", "Lcom/menteencalma/app/navigation/Screen$ArticleDetail;", "Lcom/menteencalma/app/navigation/Screen$Articles;", "Lcom/menteencalma/app/navigation/Screen$Chat;", "Lcom/menteencalma/app/navigation/Screen$CreateProfile;", "Lcom/menteencalma/app/navigation/Screen$Disclaimer;", "Lcom/menteencalma/app/navigation/Screen$Doctors;", "Lcom/menteencalma/app/navigation/Screen$Login;", "Lcom/menteencalma/app/navigation/Screen$MoodTracker;", "Lcom/menteencalma/app/navigation/Screen$Profile;", "Lcom/menteencalma/app/navigation/Screen$Recommendations;", "Lcom/menteencalma/app/navigation/Screen$Settings;", "Lcom/menteencalma/app/navigation/Screen$Splash;", "Lcom/menteencalma/app/navigation/Screen$Subscribe;", "app_debug"})
public abstract class Screen {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String route = null;
    
    /**
     * Rutas que no deben mostrar la barra de navegación inferior
     */
    @org.jetbrains.annotations.NotNull()
    private static final java.util.Set<java.lang.String> authScreens = null;
    
    /**
     * Rutas principales que forman parte de la navegación inferior
     */
    @org.jetbrains.annotations.NotNull()
    private static final java.util.Set<java.lang.String> mainScreens = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.menteencalma.app.navigation.Screen.Companion Companion = null;
    
    private Screen(java.lang.String route) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRoute() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004\u00a8\u0006\u0006"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$ArticleDetail;", "Lcom/menteencalma/app/navigation/Screen;", "()V", "createRoute", "", "articleId", "app_debug"})
    public static final class ArticleDetail extends com.menteencalma.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.menteencalma.app.navigation.Screen.ArticleDetail INSTANCE = null;
        
        private ArticleDetail() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String createRoute(@org.jetbrains.annotations.NotNull()
        java.lang.String articleId) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$Articles;", "Lcom/menteencalma/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Articles extends com.menteencalma.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.menteencalma.app.navigation.Screen.Articles INSTANCE = null;
        
        private Articles() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$Chat;", "Lcom/menteencalma/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Chat extends com.menteencalma.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.menteencalma.app.navigation.Screen.Chat INSTANCE = null;
        
        private Chat() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0017\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0007\u00a8\u0006\n"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$Companion;", "", "()V", "authScreens", "", "", "getAuthScreens", "()Ljava/util/Set;", "mainScreens", "getMainScreens", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * Rutas que no deben mostrar la barra de navegación inferior
         */
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getAuthScreens() {
            return null;
        }
        
        /**
         * Rutas principales que forman parte de la navegación inferior
         */
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getMainScreens() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$CreateProfile;", "Lcom/menteencalma/app/navigation/Screen;", "()V", "app_debug"})
    public static final class CreateProfile extends com.menteencalma.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.menteencalma.app.navigation.Screen.CreateProfile INSTANCE = null;
        
        private CreateProfile() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$Disclaimer;", "Lcom/menteencalma/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Disclaimer extends com.menteencalma.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.menteencalma.app.navigation.Screen.Disclaimer INSTANCE = null;
        
        private Disclaimer() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$Doctors;", "Lcom/menteencalma/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Doctors extends com.menteencalma.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.menteencalma.app.navigation.Screen.Doctors INSTANCE = null;
        
        private Doctors() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$Login;", "Lcom/menteencalma/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Login extends com.menteencalma.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.menteencalma.app.navigation.Screen.Login INSTANCE = null;
        
        private Login() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$MoodTracker;", "Lcom/menteencalma/app/navigation/Screen;", "()V", "app_debug"})
    public static final class MoodTracker extends com.menteencalma.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.menteencalma.app.navigation.Screen.MoodTracker INSTANCE = null;
        
        private MoodTracker() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$Profile;", "Lcom/menteencalma/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Profile extends com.menteencalma.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.menteencalma.app.navigation.Screen.Profile INSTANCE = null;
        
        private Profile() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$Recommendations;", "Lcom/menteencalma/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Recommendations extends com.menteencalma.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.menteencalma.app.navigation.Screen.Recommendations INSTANCE = null;
        
        private Recommendations() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$Settings;", "Lcom/menteencalma/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Settings extends com.menteencalma.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.menteencalma.app.navigation.Screen.Settings INSTANCE = null;
        
        private Settings() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$Splash;", "Lcom/menteencalma/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Splash extends com.menteencalma.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.menteencalma.app.navigation.Screen.Splash INSTANCE = null;
        
        private Splash() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/menteencalma/app/navigation/Screen$Subscribe;", "Lcom/menteencalma/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Subscribe extends com.menteencalma.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.menteencalma.app.navigation.Screen.Subscribe INSTANCE = null;
        
        private Subscribe() {
        }
    }
}