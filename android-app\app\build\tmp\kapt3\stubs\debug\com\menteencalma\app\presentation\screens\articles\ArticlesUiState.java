package com.menteencalma.app.presentation.screens.articles;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0019\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Bg\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\t\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r\u0012\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\r0\u0007\u00a2\u0006\u0002\u0010\u000fJ\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\tH\u00c6\u0003J\t\u0010 \u001a\u00020\tH\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\rH\u00c6\u0003J\u000f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\r0\u0007H\u00c6\u0003Jk\u0010#\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\t2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\r0\u0007H\u00c6\u0001J\u0013\u0010$\u001a\u00020\t2\b\u0010%\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010&\u001a\u00020\'H\u00d6\u0001J\t\u0010(\u001a\u00020\rH\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0013\u0010\f\u001a\u0004\u0018\u00010\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u000b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0017R\u0011\u0010\n\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0017R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\r0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0019\u00a8\u0006)"}, d2 = {"Lcom/menteencalma/app/presentation/screens/articles/ArticlesUiState;", "", "currentUser", "Lcom/menteencalma/app/domain/model/User;", "generatedArticle", "Lcom/menteencalma/app/domain/model/GeneratedArticle;", "savedArticles", "", "isGenerating", "", "isSaving", "hasReachedLimit", "errorMessage", "", "suggestedTopics", "(Lcom/menteencalma/app/domain/model/User;Lcom/menteencalma/app/domain/model/GeneratedArticle;Ljava/util/List;ZZZLjava/lang/String;Ljava/util/List;)V", "getCurrentUser", "()Lcom/menteencalma/app/domain/model/User;", "getErrorMessage", "()Ljava/lang/String;", "getGeneratedArticle", "()Lcom/menteencalma/app/domain/model/GeneratedArticle;", "getHasReachedLimit", "()Z", "getSavedArticles", "()Ljava/util/List;", "getSuggestedTopics", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class ArticlesUiState {
    @org.jetbrains.annotations.Nullable()
    private final com.menteencalma.app.domain.model.User currentUser = null;
    @org.jetbrains.annotations.Nullable()
    private final com.menteencalma.app.domain.model.GeneratedArticle generatedArticle = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.menteencalma.app.domain.model.GeneratedArticle> savedArticles = null;
    private final boolean isGenerating = false;
    private final boolean isSaving = false;
    private final boolean hasReachedLimit = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> suggestedTopics = null;
    
    public ArticlesUiState(@org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.User currentUser, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.GeneratedArticle generatedArticle, @org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.domain.model.GeneratedArticle> savedArticles, boolean isGenerating, boolean isSaving, boolean hasReachedLimit, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> suggestedTopics) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.User getCurrentUser() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.GeneratedArticle getGeneratedArticle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.domain.model.GeneratedArticle> getSavedArticles() {
        return null;
    }
    
    public final boolean isGenerating() {
        return false;
    }
    
    public final boolean isSaving() {
        return false;
    }
    
    public final boolean getHasReachedLimit() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getSuggestedTopics() {
        return null;
    }
    
    public ArticlesUiState() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.User component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.GeneratedArticle component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.domain.model.GeneratedArticle> component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.presentation.screens.articles.ArticlesUiState copy(@org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.User currentUser, @org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.GeneratedArticle generatedArticle, @org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.domain.model.GeneratedArticle> savedArticles, boolean isGenerating, boolean isSaving, boolean hasReachedLimit, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> suggestedTopics) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}