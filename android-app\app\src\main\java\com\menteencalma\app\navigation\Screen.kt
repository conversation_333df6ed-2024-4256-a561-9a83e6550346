package com.menteencalma.app.navigation

/**
 * Sealed class que define todas las pantallas de la aplicación
 */
sealed class Screen(val route: String) {
    // Pantalla de carga inicial
    object Splash : Screen("splash")
    
    // Pantallas de autenticación
    object Login : Screen("login")
    object CreateProfile : Screen("create_profile")
    
    // Pantallas principales (con bottom navigation)
    object Chat : Screen("chat")
    object Recommendations : Screen("recommendations")
    object Articles : Screen("articles")
    object MoodTracker : Screen("mood_tracker")
    object Doctors : Screen("doctors")
    object Profile : Screen("profile")
    
    // Pantallas adicionales
    object Subscribe : Screen("subscribe")
    object Disclaimer : Screen("disclaimer")
    object Settings : Screen("settings")
    object ArticleDetail : Screen("article_detail/{articleId}") {
        fun createRoute(articleId: String) = "article_detail/$articleId"
    }
    
    companion object {
        /**
         * Rutas que no deben mostrar la barra de navegación inferior
         */
        val authScreens = setOf(
            Splash.route,
            Login.route,
            CreateProfile.route,
            Subscribe.route,
            Disclaimer.route
        )
        
        /**
         * Rutas principales que forman parte de la navegación inferior
         */
        val mainScreens = setOf(
            Chat.route,
            Recommendations.route,
            Articles.route,
            MoodTracker.route,
            Doctors.route,
            Profile.route
        )
    }
}
