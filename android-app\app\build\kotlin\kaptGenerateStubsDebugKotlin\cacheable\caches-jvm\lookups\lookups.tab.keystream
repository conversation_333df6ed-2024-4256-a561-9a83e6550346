  Activity android.app  Application android.app  Bundle android.app.Activity  Bundle android.content.Context  Bundle android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  Log android.util  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  Image androidx.compose.foundation  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  Pair "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  
LazyListState  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  rememberLazyListState  androidx.compose.foundation.lazy  	GridCells %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  
selectable %androidx.compose.foundation.selection  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  	ArrowBack &androidx.compose.material.icons.filled  Article &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Email &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  Lock &androidx.compose.material.icons.filled  Pair &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  
Visibility &androidx.compose.material.icons.filled  
VisibilityOff &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  com &androidx.compose.material.icons.filled  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  Icon androidx.compose.material3  
MaterialTheme androidx.compose.material3  
NavigationBar androidx.compose.material3  NavigationBarItem androidx.compose.material3  NavigationBarItemDefaults androidx.compose.material3  Pair androidx.compose.material3  Scaffold androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  androidx androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  Pair androidx.compose.runtime  
SideEffect androidx.compose.runtime  androidx androidx.compose.runtime  collectAsState androidx.compose.runtime  com androidx.compose.runtime  getValue androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  clip androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  ContentScale androidx.compose.ui.layout  LocalContext androidx.compose.ui.platform  LocalSoftwareKeyboardController androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  painterResource androidx.compose.ui.res  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  VisualTransformation androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  TextDecoration androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Bundle #androidx.core.app.ComponentActivity  SplashScreen androidx.core.splashscreen  	Companion 'androidx.core.splashscreen.SplashScreen  installSplashScreen 1androidx.core.splashscreen.SplashScreen.Companion  WindowCompat androidx.core.view  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  Any androidx.lifecycle.ViewModel  ArticleStats androidx.lifecycle.ViewModel  ArticlesUiState androidx.lifecycle.ViewModel  AuthRepository androidx.lifecycle.ViewModel  	AuthState androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  ChatMessage androidx.lifecycle.ViewModel  ChatUiState androidx.lifecycle.ViewModel  CloudFunctionsService androidx.lifecycle.ViewModel  ConversationStats androidx.lifecycle.ViewModel  CreateProfileUiState androidx.lifecycle.ViewModel  DatabaseMigrationService androidx.lifecycle.ViewModel  DatabaseMonitoringService androidx.lifecycle.ViewModel  DatabaseMonitoringUiState androidx.lifecycle.ViewModel  DatabaseRepository androidx.lifecycle.ViewModel  FirebaseFirestore androidx.lifecycle.ViewModel  FirebaseFunctions androidx.lifecycle.ViewModel  Float androidx.lifecycle.ViewModel  Flow androidx.lifecycle.ViewModel  GeneratedArticle androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  ListenerRegistration androidx.lifecycle.ViewModel  LoginUiState androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  
MoodAnalytics androidx.lifecycle.ViewModel  	MoodEntry androidx.lifecycle.ViewModel  MoodTrackerUiState androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  Pair androidx.lifecycle.ViewModel  ProfileUiState androidx.lifecycle.ViewModel  Recommendation androidx.lifecycle.ViewModel  RecommendationsUiState androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  SubscribeUiState androidx.lifecycle.ViewModel  SubscriptionDisplayInfo androidx.lifecycle.ViewModel  SubscriptionFeature androidx.lifecycle.ViewModel  SubscriptionPlan androidx.lifecycle.ViewModel  TargetDatabase androidx.lifecycle.ViewModel  	Throwable androidx.lifecycle.ViewModel  User androidx.lifecycle.ViewModel  UserRepository androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  
NavController androidx.navigation  NavHostController androidx.navigation  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  
AsyncImage coil.compose  	LineChart #com.github.mikephil.charting.charts  XAxis 'com.github.mikephil.charting.components  Entry !com.github.mikephil.charting.data  LineData !com.github.mikephil.charting.data  LineDataSet !com.github.mikephil.charting.data  ValueFormatter &com.github.mikephil.charting.formatter  FirebaseAuth com.google.firebase.auth  FirebaseUser com.google.firebase.auth  GoogleAuthProvider com.google.firebase.auth  userProfileChangeRequest com.google.firebase.auth  CollectionReference com.google.firebase.firestore  FirebaseFirestore com.google.firebase.firestore  ListenerRegistration com.google.firebase.firestore  Query com.google.firebase.firestore  
collection /com.google.firebase.firestore.FirebaseFirestore  FirebaseFunctions com.google.firebase.functions  HttpsCallableResult com.google.firebase.functions  MainActivity com.menteencalma.app  MenteEnCalmaApp com.menteencalma.app  MenteEnCalmaApplication com.menteencalma.app  R com.menteencalma.app  Bundle !com.menteencalma.app.MainActivity  Boolean #com.menteencalma.app.data.migration  DatabaseMigrationService #com.menteencalma.app.data.migration  Double #com.menteencalma.app.data.migration  
ExportData #com.menteencalma.app.data.migration  Float #com.menteencalma.app.data.migration  Int #com.menteencalma.app.data.migration  Json #com.menteencalma.app.data.migration  List #com.menteencalma.app.data.migration  Long #com.menteencalma.app.data.migration  MigrationEstimate #com.menteencalma.app.data.migration  MigrationStatus #com.menteencalma.app.data.migration  MutableList #com.menteencalma.app.data.migration  MutableStateFlow #com.menteencalma.app.data.migration  Result #com.menteencalma.app.data.migration  String #com.menteencalma.app.data.migration  TargetDatabase #com.menteencalma.app.data.migration  User #com.menteencalma.app.data.migration  ValidationReport #com.menteencalma.app.data.migration  asStateFlow #com.menteencalma.app.data.migration  kotlinx #com.menteencalma.app.data.migration  DatabaseRepository <com.menteencalma.app.data.migration.DatabaseMigrationService  Double <com.menteencalma.app.data.migration.DatabaseMigrationService  Inject <com.menteencalma.app.data.migration.DatabaseMigrationService  Int <com.menteencalma.app.data.migration.DatabaseMigrationService  Json <com.menteencalma.app.data.migration.DatabaseMigrationService  MigrationEstimate <com.menteencalma.app.data.migration.DatabaseMigrationService  MigrationStatus <com.menteencalma.app.data.migration.DatabaseMigrationService  MutableStateFlow <com.menteencalma.app.data.migration.DatabaseMigrationService  Result <com.menteencalma.app.data.migration.DatabaseMigrationService  	StateFlow <com.menteencalma.app.data.migration.DatabaseMigrationService  String <com.menteencalma.app.data.migration.DatabaseMigrationService  TargetDatabase <com.menteencalma.app.data.migration.DatabaseMigrationService  ValidationReport <com.menteencalma.app.data.migration.DatabaseMigrationService  _migrationStatus <com.menteencalma.app.data.migration.DatabaseMigrationService  asStateFlow <com.menteencalma.app.data.migration.DatabaseMigrationService  getASStateFlow <com.menteencalma.app.data.migration.DatabaseMigrationService  getAsStateFlow <com.menteencalma.app.data.migration.DatabaseMigrationService  invoke <com.menteencalma.app.data.migration.DatabaseMigrationService  DatabaseRepository Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  Double Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  Inject Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  Int Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  Json Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  MigrationEstimate Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  MigrationStatus Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  MutableStateFlow Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  Result Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  	StateFlow Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  String Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  TargetDatabase Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  ValidationReport Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  asStateFlow Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  getASStateFlow Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  getAsStateFlow Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  invoke Fcom.menteencalma.app.data.migration.DatabaseMigrationService.Companion  Int .com.menteencalma.app.data.migration.ExportData  List .com.menteencalma.app.data.migration.ExportData  Long .com.menteencalma.app.data.migration.ExportData  String .com.menteencalma.app.data.migration.ExportData  User .com.menteencalma.app.data.migration.ExportData  Int 8com.menteencalma.app.data.migration.ExportData.Companion  List 8com.menteencalma.app.data.migration.ExportData.Companion  Long 8com.menteencalma.app.data.migration.ExportData.Companion  String 8com.menteencalma.app.data.migration.ExportData.Companion  User 8com.menteencalma.app.data.migration.ExportData.Companion  Double 5com.menteencalma.app.data.migration.MigrationEstimate  Int 5com.menteencalma.app.data.migration.MigrationEstimate  String 5com.menteencalma.app.data.migration.MigrationEstimate  Boolean 3com.menteencalma.app.data.migration.MigrationStatus  Float 3com.menteencalma.app.data.migration.MigrationStatus  Long 3com.menteencalma.app.data.migration.MigrationStatus  String 3com.menteencalma.app.data.migration.MigrationStatus  Boolean 4com.menteencalma.app.data.migration.ValidationReport  Int 4com.menteencalma.app.data.migration.ValidationReport  MutableList 4com.menteencalma.app.data.migration.ValidationReport  String 4com.menteencalma.app.data.migration.ValidationReport  errors 4com.menteencalma.app.data.migration.ValidationReport  	AlertType $com.menteencalma.app.data.monitoring  
DatabaseAlert $com.menteencalma.app.data.monitoring  DatabaseMetrics $com.menteencalma.app.data.monitoring  DatabaseMonitoringService $com.menteencalma.app.data.monitoring  Double $com.menteencalma.app.data.monitoring  Int $com.menteencalma.app.data.monitoring  List $com.menteencalma.app.data.monitoring  Long $com.menteencalma.app.data.monitoring  Map $com.menteencalma.app.data.monitoring  MutableStateFlow $com.menteencalma.app.data.monitoring  OptimizationRecommendation $com.menteencalma.app.data.monitoring  Priority $com.menteencalma.app.data.monitoring  RecommendationType $com.menteencalma.app.data.monitoring  String $com.menteencalma.app.data.monitoring  asStateFlow $com.menteencalma.app.data.monitoring  	emptyList $com.menteencalma.app.data.monitoring  	AlertType 2com.menteencalma.app.data.monitoring.DatabaseAlert  Long 2com.menteencalma.app.data.monitoring.DatabaseAlert  Map 2com.menteencalma.app.data.monitoring.DatabaseAlert  String 2com.menteencalma.app.data.monitoring.DatabaseAlert  Int 4com.menteencalma.app.data.monitoring.DatabaseMetrics  Long 4com.menteencalma.app.data.monitoring.DatabaseMetrics  Map 4com.menteencalma.app.data.monitoring.DatabaseMetrics  String 4com.menteencalma.app.data.monitoring.DatabaseMetrics  
DatabaseAlert >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  DatabaseMetrics >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  Double >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  Inject >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  Int >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  List >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  Long >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  MutableStateFlow >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  OptimizationRecommendation >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  	StateFlow >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  String >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  _alerts >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  _metrics >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  asStateFlow >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  	emptyList >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  getASStateFlow >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  getAsStateFlow >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  getEMPTYList >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  getEmptyList >com.menteencalma.app.data.monitoring.DatabaseMonitoringService  
DatabaseAlert Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  DatabaseMetrics Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  Double Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  Inject Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  Int Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  List Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  Long Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  MutableStateFlow Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  OptimizationRecommendation Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  	StateFlow Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  String Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  asStateFlow Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  	emptyList Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  getASStateFlow Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  getAsStateFlow Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  getEMPTYList Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  getEmptyList Hcom.menteencalma.app.data.monitoring.DatabaseMonitoringService.Companion  Priority ?com.menteencalma.app.data.monitoring.OptimizationRecommendation  RecommendationType ?com.menteencalma.app.data.monitoring.OptimizationRecommendation  String ?com.menteencalma.app.data.monitoring.OptimizationRecommendation  Any $com.menteencalma.app.data.repository  Article $com.menteencalma.app.data.repository  AuthRepositoryImpl $com.menteencalma.app.data.repository  Boolean $com.menteencalma.app.data.repository  ChatMessage $com.menteencalma.app.data.repository  FirebaseDatabaseRepository $com.menteencalma.app.data.repository  Int $com.menteencalma.app.data.repository  IntRange $com.menteencalma.app.data.repository  List $com.menteencalma.app.data.repository  Long $com.menteencalma.app.data.repository  Map $com.menteencalma.app.data.repository  MonitoredDatabaseRepository $com.menteencalma.app.data.repository  Result $com.menteencalma.app.data.repository  String $com.menteencalma.app.data.repository  Subscription $com.menteencalma.app.data.repository  Unit $com.menteencalma.app.data.repository  User $com.menteencalma.app.data.repository  UserRepositoryImpl $com.menteencalma.app.data.repository  FirebaseAuth 7com.menteencalma.app.data.repository.AuthRepositoryImpl  FirebaseUser 7com.menteencalma.app.data.repository.AuthRepositoryImpl  Inject 7com.menteencalma.app.data.repository.AuthRepositoryImpl  Result 7com.menteencalma.app.data.repository.AuthRepositoryImpl  String 7com.menteencalma.app.data.repository.AuthRepositoryImpl  Unit 7com.menteencalma.app.data.repository.AuthRepositoryImpl  Any ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  Article ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  Boolean ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  ChatMessage ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  DatabaseRepository ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  FirebaseFirestore ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  Flow ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  Inject ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  Int ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  IntRange ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  List ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  Long ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  Map ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  Result ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  String ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  Subscription ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  Unit ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  User ?com.menteencalma.app.data.repository.FirebaseDatabaseRepository  Any @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  Article @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  Boolean @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  ChatMessage @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  DatabaseMonitoringService @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  DatabaseRepository @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  Flow @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  Inject @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  Int @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  IntRange @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  List @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  Long @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  Map @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  Result @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  String @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  Subscription @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  Unit @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  User @com.menteencalma.app.data.repository.MonitoredDatabaseRepository  FirebaseFirestore 7com.menteencalma.app.data.repository.UserRepositoryImpl  Flow 7com.menteencalma.app.data.repository.UserRepositoryImpl  Inject 7com.menteencalma.app.data.repository.UserRepositoryImpl  Long 7com.menteencalma.app.data.repository.UserRepositoryImpl  Result 7com.menteencalma.app.data.repository.UserRepositoryImpl  String 7com.menteencalma.app.data.repository.UserRepositoryImpl  Unit 7com.menteencalma.app.data.repository.UserRepositoryImpl  User 7com.menteencalma.app.data.repository.UserRepositoryImpl  	firestore 7com.menteencalma.app.data.repository.UserRepositoryImpl  Any !com.menteencalma.app.data.service  ArticleResponse !com.menteencalma.app.data.service  Boolean !com.menteencalma.app.data.service  ChatbotResponse !com.menteencalma.app.data.service  CloudFunctionException !com.menteencalma.app.data.service  CloudFunctionsService !com.menteencalma.app.data.service  	Exception !com.menteencalma.app.data.service  Float !com.menteencalma.app.data.service  Int !com.menteencalma.app.data.service  List !com.menteencalma.app.data.service  Map !com.menteencalma.app.data.service  MockCloudFunctionsService !com.menteencalma.app.data.service  RecommendationResponse !com.menteencalma.app.data.service  Result !com.menteencalma.app.data.service  String !com.menteencalma.app.data.service  UserProfileResponse !com.menteencalma.app.data.service  Any 1com.menteencalma.app.data.service.ArticleResponse  Int 1com.menteencalma.app.data.service.ArticleResponse  List 1com.menteencalma.app.data.service.ArticleResponse  Map 1com.menteencalma.app.data.service.ArticleResponse  String 1com.menteencalma.app.data.service.ArticleResponse  Any 1com.menteencalma.app.data.service.ChatbotResponse  Float 1com.menteencalma.app.data.service.ChatbotResponse  List 1com.menteencalma.app.data.service.ChatbotResponse  Map 1com.menteencalma.app.data.service.ChatbotResponse  String 1com.menteencalma.app.data.service.ChatbotResponse  	Exception 8com.menteencalma.app.data.service.CloudFunctionException  String 8com.menteencalma.app.data.service.CloudFunctionException  ArticleResponse 7com.menteencalma.app.data.service.CloudFunctionsService  ChatMessage 7com.menteencalma.app.data.service.CloudFunctionsService  ChatbotResponse 7com.menteencalma.app.data.service.CloudFunctionsService  CloudFunctionException 7com.menteencalma.app.data.service.CloudFunctionsService  	Exception 7com.menteencalma.app.data.service.CloudFunctionsService  FirebaseFunctions 7com.menteencalma.app.data.service.CloudFunctionsService  HttpsCallableResult 7com.menteencalma.app.data.service.CloudFunctionsService  Inject 7com.menteencalma.app.data.service.CloudFunctionsService  List 7com.menteencalma.app.data.service.CloudFunctionsService  MockCloudFunctionsService 7com.menteencalma.app.data.service.CloudFunctionsService  RecommendationResponse 7com.menteencalma.app.data.service.CloudFunctionsService  Result 7com.menteencalma.app.data.service.CloudFunctionsService  String 7com.menteencalma.app.data.service.CloudFunctionsService  UserProfileResponse 7com.menteencalma.app.data.service.CloudFunctionsService  ArticleResponse Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  ChatMessage Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  ChatbotResponse Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  CloudFunctionException Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  	Exception Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  FirebaseFunctions Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  HttpsCallableResult Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  Inject Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  List Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  MockCloudFunctionsService Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  RecommendationResponse Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  Result Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  String Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  UserProfileResponse Acom.menteencalma.app.data.service.CloudFunctionsService.Companion  Any ;com.menteencalma.app.data.service.MockCloudFunctionsService  ArticleResponse ;com.menteencalma.app.data.service.MockCloudFunctionsService  ChatbotResponse ;com.menteencalma.app.data.service.MockCloudFunctionsService  Inject ;com.menteencalma.app.data.service.MockCloudFunctionsService  List ;com.menteencalma.app.data.service.MockCloudFunctionsService  Map ;com.menteencalma.app.data.service.MockCloudFunctionsService  RecommendationResponse ;com.menteencalma.app.data.service.MockCloudFunctionsService  Result ;com.menteencalma.app.data.service.MockCloudFunctionsService  String ;com.menteencalma.app.data.service.MockCloudFunctionsService  UserProfileResponse ;com.menteencalma.app.data.service.MockCloudFunctionsService  Any Ecom.menteencalma.app.data.service.MockCloudFunctionsService.Companion  ArticleResponse Ecom.menteencalma.app.data.service.MockCloudFunctionsService.Companion  ChatbotResponse Ecom.menteencalma.app.data.service.MockCloudFunctionsService.Companion  Inject Ecom.menteencalma.app.data.service.MockCloudFunctionsService.Companion  List Ecom.menteencalma.app.data.service.MockCloudFunctionsService.Companion  Map Ecom.menteencalma.app.data.service.MockCloudFunctionsService.Companion  RecommendationResponse Ecom.menteencalma.app.data.service.MockCloudFunctionsService.Companion  Result Ecom.menteencalma.app.data.service.MockCloudFunctionsService.Companion  String Ecom.menteencalma.app.data.service.MockCloudFunctionsService.Companion  UserProfileResponse Ecom.menteencalma.app.data.service.MockCloudFunctionsService.Companion  Any 8com.menteencalma.app.data.service.RecommendationResponse  Boolean 8com.menteencalma.app.data.service.RecommendationResponse  Int 8com.menteencalma.app.data.service.RecommendationResponse  List 8com.menteencalma.app.data.service.RecommendationResponse  Map 8com.menteencalma.app.data.service.RecommendationResponse  String 8com.menteencalma.app.data.service.RecommendationResponse  Boolean 5com.menteencalma.app.data.service.UserProfileResponse  String 5com.menteencalma.app.data.service.UserProfileResponse  AnnotationRetention com.menteencalma.app.di  	AppModule com.menteencalma.app.di  FirebaseDatabase com.menteencalma.app.di  MonitoredDatabase com.menteencalma.app.di  	Retention com.menteencalma.app.di  SingletonComponent com.menteencalma.app.di  AuthRepository !com.menteencalma.app.di.AppModule  CloudFunctionsService !com.menteencalma.app.di.AppModule  DatabaseMigrationService !com.menteencalma.app.di.AppModule  DatabaseMonitoringService !com.menteencalma.app.di.AppModule  DatabaseRepository !com.menteencalma.app.di.AppModule  FirebaseAuth !com.menteencalma.app.di.AppModule  FirebaseDatabase !com.menteencalma.app.di.AppModule  FirebaseFirestore !com.menteencalma.app.di.AppModule  FirebaseFunctions !com.menteencalma.app.di.AppModule  MockCloudFunctionsService !com.menteencalma.app.di.AppModule  MonitoredDatabase !com.menteencalma.app.di.AppModule  Provides !com.menteencalma.app.di.AppModule  	Singleton !com.menteencalma.app.di.AppModule  UserRepository !com.menteencalma.app.di.AppModule  Any !com.menteencalma.app.domain.model  Article !com.menteencalma.app.domain.model  	AuthState !com.menteencalma.app.domain.model  Boolean !com.menteencalma.app.domain.model  ChatMessage !com.menteencalma.app.domain.model  Float !com.menteencalma.app.domain.model  GeneratedArticle !com.menteencalma.app.domain.model  Int !com.menteencalma.app.domain.model  Json !com.menteencalma.app.domain.model  List !com.menteencalma.app.domain.model  Long !com.menteencalma.app.domain.model  Map !com.menteencalma.app.domain.model  MigrationStatus !com.menteencalma.app.domain.model  	MoodEntry !com.menteencalma.app.domain.model  MutableStateFlow !com.menteencalma.app.domain.model  Recommendation !com.menteencalma.app.domain.model  Result !com.menteencalma.app.domain.model  String !com.menteencalma.app.domain.model  Subscription !com.menteencalma.app.domain.model  Unit !com.menteencalma.app.domain.model  User !com.menteencalma.app.domain.model  asStateFlow !com.menteencalma.app.domain.model  kotlinx !com.menteencalma.app.domain.model  listOf !com.menteencalma.app.domain.model  setOf !com.menteencalma.app.domain.model  Any )com.menteencalma.app.domain.model.Article  ArticleMetadata )com.menteencalma.app.domain.model.Article  Boolean )com.menteencalma.app.domain.model.Article  Float )com.menteencalma.app.domain.model.Article  Int )com.menteencalma.app.domain.model.Article  List )com.menteencalma.app.domain.model.Article  Long )com.menteencalma.app.domain.model.Article  Map )com.menteencalma.app.domain.model.Article  Result )com.menteencalma.app.domain.model.Article  Serializable )com.menteencalma.app.domain.model.Article  String )com.menteencalma.app.domain.model.Article  Unit )com.menteencalma.app.domain.model.Article  listOf )com.menteencalma.app.domain.model.Article  Float 9com.menteencalma.app.domain.model.Article.ArticleMetadata  List 9com.menteencalma.app.domain.model.Article.ArticleMetadata  Map 9com.menteencalma.app.domain.model.Article.ArticleMetadata  String 9com.menteencalma.app.domain.model.Article.ArticleMetadata  Float Ccom.menteencalma.app.domain.model.Article.ArticleMetadata.Companion  List Ccom.menteencalma.app.domain.model.Article.ArticleMetadata.Companion  Map Ccom.menteencalma.app.domain.model.Article.ArticleMetadata.Companion  String Ccom.menteencalma.app.domain.model.Article.ArticleMetadata.Companion  Any 3com.menteencalma.app.domain.model.Article.Companion  Boolean 3com.menteencalma.app.domain.model.Article.Companion  Float 3com.menteencalma.app.domain.model.Article.Companion  Int 3com.menteencalma.app.domain.model.Article.Companion  List 3com.menteencalma.app.domain.model.Article.Companion  Long 3com.menteencalma.app.domain.model.Article.Companion  Map 3com.menteencalma.app.domain.model.Article.Companion  Result 3com.menteencalma.app.domain.model.Article.Companion  Serializable 3com.menteencalma.app.domain.model.Article.Companion  String 3com.menteencalma.app.domain.model.Article.Companion  Unit 3com.menteencalma.app.domain.model.Article.Companion  	getLISTOf 3com.menteencalma.app.domain.model.Article.Companion  	getListOf 3com.menteencalma.app.domain.model.Article.Companion  listOf 3com.menteencalma.app.domain.model.Article.Companion  	AuthState +com.menteencalma.app.domain.model.AuthState  Loading +com.menteencalma.app.domain.model.AuthState  String +com.menteencalma.app.domain.model.AuthState  String 1com.menteencalma.app.domain.model.AuthState.Error  Any -com.menteencalma.app.domain.model.ChatMessage  Boolean -com.menteencalma.app.domain.model.ChatMessage  Float -com.menteencalma.app.domain.model.ChatMessage  Int -com.menteencalma.app.domain.model.ChatMessage  List -com.menteencalma.app.domain.model.ChatMessage  Long -com.menteencalma.app.domain.model.ChatMessage  Map -com.menteencalma.app.domain.model.ChatMessage  MessageMetadata -com.menteencalma.app.domain.model.ChatMessage  MessageType -com.menteencalma.app.domain.model.ChatMessage  Result -com.menteencalma.app.domain.model.ChatMessage  Serializable -com.menteencalma.app.domain.model.ChatMessage  String -com.menteencalma.app.domain.model.ChatMessage  Unit -com.menteencalma.app.domain.model.ChatMessage  listOf -com.menteencalma.app.domain.model.ChatMessage  Any 7com.menteencalma.app.domain.model.ChatMessage.Companion  Boolean 7com.menteencalma.app.domain.model.ChatMessage.Companion  Float 7com.menteencalma.app.domain.model.ChatMessage.Companion  Int 7com.menteencalma.app.domain.model.ChatMessage.Companion  List 7com.menteencalma.app.domain.model.ChatMessage.Companion  Long 7com.menteencalma.app.domain.model.ChatMessage.Companion  Map 7com.menteencalma.app.domain.model.ChatMessage.Companion  Result 7com.menteencalma.app.domain.model.ChatMessage.Companion  Serializable 7com.menteencalma.app.domain.model.ChatMessage.Companion  String 7com.menteencalma.app.domain.model.ChatMessage.Companion  Unit 7com.menteencalma.app.domain.model.ChatMessage.Companion  	getLISTOf 7com.menteencalma.app.domain.model.ChatMessage.Companion  	getListOf 7com.menteencalma.app.domain.model.ChatMessage.Companion  listOf 7com.menteencalma.app.domain.model.ChatMessage.Companion  Float =com.menteencalma.app.domain.model.ChatMessage.MessageMetadata  List =com.menteencalma.app.domain.model.ChatMessage.MessageMetadata  Long =com.menteencalma.app.domain.model.ChatMessage.MessageMetadata  Map =com.menteencalma.app.domain.model.ChatMessage.MessageMetadata  String =com.menteencalma.app.domain.model.ChatMessage.MessageMetadata  Float Gcom.menteencalma.app.domain.model.ChatMessage.MessageMetadata.Companion  List Gcom.menteencalma.app.domain.model.ChatMessage.MessageMetadata.Companion  Long Gcom.menteencalma.app.domain.model.ChatMessage.MessageMetadata.Companion  Map Gcom.menteencalma.app.domain.model.ChatMessage.MessageMetadata.Companion  String Gcom.menteencalma.app.domain.model.ChatMessage.MessageMetadata.Companion  Any 2com.menteencalma.app.domain.model.GeneratedArticle  ArticleMetadata 2com.menteencalma.app.domain.model.GeneratedArticle  Boolean 2com.menteencalma.app.domain.model.GeneratedArticle  Float 2com.menteencalma.app.domain.model.GeneratedArticle  Int 2com.menteencalma.app.domain.model.GeneratedArticle  List 2com.menteencalma.app.domain.model.GeneratedArticle  Long 2com.menteencalma.app.domain.model.GeneratedArticle  Map 2com.menteencalma.app.domain.model.GeneratedArticle  Result 2com.menteencalma.app.domain.model.GeneratedArticle  Serializable 2com.menteencalma.app.domain.model.GeneratedArticle  String 2com.menteencalma.app.domain.model.GeneratedArticle  Unit 2com.menteencalma.app.domain.model.GeneratedArticle  listOf 2com.menteencalma.app.domain.model.GeneratedArticle  Float Bcom.menteencalma.app.domain.model.GeneratedArticle.ArticleMetadata  Int Bcom.menteencalma.app.domain.model.GeneratedArticle.ArticleMetadata  List Bcom.menteencalma.app.domain.model.GeneratedArticle.ArticleMetadata  Long Bcom.menteencalma.app.domain.model.GeneratedArticle.ArticleMetadata  Map Bcom.menteencalma.app.domain.model.GeneratedArticle.ArticleMetadata  String Bcom.menteencalma.app.domain.model.GeneratedArticle.ArticleMetadata  Float Lcom.menteencalma.app.domain.model.GeneratedArticle.ArticleMetadata.Companion  Int Lcom.menteencalma.app.domain.model.GeneratedArticle.ArticleMetadata.Companion  List Lcom.menteencalma.app.domain.model.GeneratedArticle.ArticleMetadata.Companion  Long Lcom.menteencalma.app.domain.model.GeneratedArticle.ArticleMetadata.Companion  Map Lcom.menteencalma.app.domain.model.GeneratedArticle.ArticleMetadata.Companion  String Lcom.menteencalma.app.domain.model.GeneratedArticle.ArticleMetadata.Companion  Any <com.menteencalma.app.domain.model.GeneratedArticle.Companion  Boolean <com.menteencalma.app.domain.model.GeneratedArticle.Companion  Float <com.menteencalma.app.domain.model.GeneratedArticle.Companion  Int <com.menteencalma.app.domain.model.GeneratedArticle.Companion  List <com.menteencalma.app.domain.model.GeneratedArticle.Companion  Long <com.menteencalma.app.domain.model.GeneratedArticle.Companion  Map <com.menteencalma.app.domain.model.GeneratedArticle.Companion  Result <com.menteencalma.app.domain.model.GeneratedArticle.Companion  Serializable <com.menteencalma.app.domain.model.GeneratedArticle.Companion  String <com.menteencalma.app.domain.model.GeneratedArticle.Companion  Unit <com.menteencalma.app.domain.model.GeneratedArticle.Companion  	getLISTOf <com.menteencalma.app.domain.model.GeneratedArticle.Companion  	getListOf <com.menteencalma.app.domain.model.GeneratedArticle.Companion  listOf <com.menteencalma.app.domain.model.GeneratedArticle.Companion  Any +com.menteencalma.app.domain.model.MoodEntry  Int +com.menteencalma.app.domain.model.MoodEntry  List +com.menteencalma.app.domain.model.MoodEntry  Long +com.menteencalma.app.domain.model.MoodEntry  Map +com.menteencalma.app.domain.model.MoodEntry  MoodMetadata +com.menteencalma.app.domain.model.MoodEntry  MoodType +com.menteencalma.app.domain.model.MoodEntry  Result +com.menteencalma.app.domain.model.MoodEntry  Serializable +com.menteencalma.app.domain.model.MoodEntry  String +com.menteencalma.app.domain.model.MoodEntry  Unit +com.menteencalma.app.domain.model.MoodEntry  listOf +com.menteencalma.app.domain.model.MoodEntry  Any 5com.menteencalma.app.domain.model.MoodEntry.Companion  Int 5com.menteencalma.app.domain.model.MoodEntry.Companion  List 5com.menteencalma.app.domain.model.MoodEntry.Companion  Long 5com.menteencalma.app.domain.model.MoodEntry.Companion  Map 5com.menteencalma.app.domain.model.MoodEntry.Companion  Result 5com.menteencalma.app.domain.model.MoodEntry.Companion  Serializable 5com.menteencalma.app.domain.model.MoodEntry.Companion  String 5com.menteencalma.app.domain.model.MoodEntry.Companion  Unit 5com.menteencalma.app.domain.model.MoodEntry.Companion  	getLISTOf 5com.menteencalma.app.domain.model.MoodEntry.Companion  	getListOf 5com.menteencalma.app.domain.model.MoodEntry.Companion  listOf 5com.menteencalma.app.domain.model.MoodEntry.Companion  Long 8com.menteencalma.app.domain.model.MoodEntry.MoodMetadata  Map 8com.menteencalma.app.domain.model.MoodEntry.MoodMetadata  MoodType 8com.menteencalma.app.domain.model.MoodEntry.MoodMetadata  String 8com.menteencalma.app.domain.model.MoodEntry.MoodMetadata  Long Bcom.menteencalma.app.domain.model.MoodEntry.MoodMetadata.Companion  Map Bcom.menteencalma.app.domain.model.MoodEntry.MoodMetadata.Companion  MoodType Bcom.menteencalma.app.domain.model.MoodEntry.MoodMetadata.Companion  String Bcom.menteencalma.app.domain.model.MoodEntry.MoodMetadata.Companion  Int 4com.menteencalma.app.domain.model.MoodEntry.MoodType  MoodType 4com.menteencalma.app.domain.model.MoodEntry.MoodType  String 4com.menteencalma.app.domain.model.MoodEntry.MoodType  Int >com.menteencalma.app.domain.model.MoodEntry.MoodType.Companion  MoodType >com.menteencalma.app.domain.model.MoodEntry.MoodType.Companion  String >com.menteencalma.app.domain.model.MoodEntry.MoodType.Companion  Boolean 0com.menteencalma.app.domain.model.Recommendation  Float 0com.menteencalma.app.domain.model.Recommendation  Int 0com.menteencalma.app.domain.model.Recommendation  List 0com.menteencalma.app.domain.model.Recommendation  Long 0com.menteencalma.app.domain.model.Recommendation  Map 0com.menteencalma.app.domain.model.Recommendation  RecommendationMetadata 0com.menteencalma.app.domain.model.Recommendation  Result 0com.menteencalma.app.domain.model.Recommendation  Serializable 0com.menteencalma.app.domain.model.Recommendation  String 0com.menteencalma.app.domain.model.Recommendation  Unit 0com.menteencalma.app.domain.model.Recommendation  Boolean :com.menteencalma.app.domain.model.Recommendation.Companion  Float :com.menteencalma.app.domain.model.Recommendation.Companion  Int :com.menteencalma.app.domain.model.Recommendation.Companion  List :com.menteencalma.app.domain.model.Recommendation.Companion  Long :com.menteencalma.app.domain.model.Recommendation.Companion  Map :com.menteencalma.app.domain.model.Recommendation.Companion  Result :com.menteencalma.app.domain.model.Recommendation.Companion  Serializable :com.menteencalma.app.domain.model.Recommendation.Companion  String :com.menteencalma.app.domain.model.Recommendation.Companion  Unit :com.menteencalma.app.domain.model.Recommendation.Companion  Float Gcom.menteencalma.app.domain.model.Recommendation.RecommendationMetadata  List Gcom.menteencalma.app.domain.model.Recommendation.RecommendationMetadata  Map Gcom.menteencalma.app.domain.model.Recommendation.RecommendationMetadata  String Gcom.menteencalma.app.domain.model.Recommendation.RecommendationMetadata  Float Qcom.menteencalma.app.domain.model.Recommendation.RecommendationMetadata.Companion  List Qcom.menteencalma.app.domain.model.Recommendation.RecommendationMetadata.Companion  Map Qcom.menteencalma.app.domain.model.Recommendation.RecommendationMetadata.Companion  String Qcom.menteencalma.app.domain.model.Recommendation.RecommendationMetadata.Companion  Boolean .com.menteencalma.app.domain.model.Subscription  Long .com.menteencalma.app.domain.model.Subscription  String .com.menteencalma.app.domain.model.Subscription  Boolean 8com.menteencalma.app.domain.model.Subscription.Companion  Long 8com.menteencalma.app.domain.model.Subscription.Companion  String 8com.menteencalma.app.domain.model.Subscription.Companion  Any &com.menteencalma.app.domain.model.User  Boolean &com.menteencalma.app.domain.model.User  Int &com.menteencalma.app.domain.model.User  List &com.menteencalma.app.domain.model.User  Long &com.menteencalma.app.domain.model.User  Map &com.menteencalma.app.domain.model.User  	MoodEntry &com.menteencalma.app.domain.model.User  NotificationSettings &com.menteencalma.app.domain.model.User  Result &com.menteencalma.app.domain.model.User  Serializable &com.menteencalma.app.domain.model.User  String &com.menteencalma.app.domain.model.User  Unit &com.menteencalma.app.domain.model.User  
UserAnalytics &com.menteencalma.app.domain.model.User  UserPreferences &com.menteencalma.app.domain.model.User  listOf &com.menteencalma.app.domain.model.User  setOf &com.menteencalma.app.domain.model.User  Any 0com.menteencalma.app.domain.model.User.Companion  Boolean 0com.menteencalma.app.domain.model.User.Companion  Int 0com.menteencalma.app.domain.model.User.Companion  List 0com.menteencalma.app.domain.model.User.Companion  Long 0com.menteencalma.app.domain.model.User.Companion  Map 0com.menteencalma.app.domain.model.User.Companion  Result 0com.menteencalma.app.domain.model.User.Companion  Serializable 0com.menteencalma.app.domain.model.User.Companion  String 0com.menteencalma.app.domain.model.User.Companion  Unit 0com.menteencalma.app.domain.model.User.Companion  	getLISTOf 0com.menteencalma.app.domain.model.User.Companion  	getListOf 0com.menteencalma.app.domain.model.User.Companion  getSETOf 0com.menteencalma.app.domain.model.User.Companion  getSetOf 0com.menteencalma.app.domain.model.User.Companion  listOf 0com.menteencalma.app.domain.model.User.Companion  setOf 0com.menteencalma.app.domain.model.User.Companion  Int 0com.menteencalma.app.domain.model.User.MoodEntry  Long 0com.menteencalma.app.domain.model.User.MoodEntry  String 0com.menteencalma.app.domain.model.User.MoodEntry  Int :com.menteencalma.app.domain.model.User.MoodEntry.Companion  Long :com.menteencalma.app.domain.model.User.MoodEntry.Companion  String :com.menteencalma.app.domain.model.User.MoodEntry.Companion  Boolean ;com.menteencalma.app.domain.model.User.NotificationSettings  String ;com.menteencalma.app.domain.model.User.NotificationSettings  Boolean Ecom.menteencalma.app.domain.model.User.NotificationSettings.Companion  String Ecom.menteencalma.app.domain.model.User.NotificationSettings.Companion  Int 4com.menteencalma.app.domain.model.User.UserAnalytics  List 4com.menteencalma.app.domain.model.User.UserAnalytics  Long 4com.menteencalma.app.domain.model.User.UserAnalytics  	MoodEntry 4com.menteencalma.app.domain.model.User.UserAnalytics  String 4com.menteencalma.app.domain.model.User.UserAnalytics  Int >com.menteencalma.app.domain.model.User.UserAnalytics.Companion  List >com.menteencalma.app.domain.model.User.UserAnalytics.Companion  Long >com.menteencalma.app.domain.model.User.UserAnalytics.Companion  	MoodEntry >com.menteencalma.app.domain.model.User.UserAnalytics.Companion  String >com.menteencalma.app.domain.model.User.UserAnalytics.Companion  Boolean 6com.menteencalma.app.domain.model.User.UserPreferences  Int 6com.menteencalma.app.domain.model.User.UserPreferences  String 6com.menteencalma.app.domain.model.User.UserPreferences  Boolean @com.menteencalma.app.domain.model.User.UserPreferences.Companion  Int @com.menteencalma.app.domain.model.User.UserPreferences.Companion  String @com.menteencalma.app.domain.model.User.UserPreferences.Companion  Any &com.menteencalma.app.domain.repository  AuthRepository &com.menteencalma.app.domain.repository  Boolean &com.menteencalma.app.domain.repository  DatabaseRepository &com.menteencalma.app.domain.repository  Int &com.menteencalma.app.domain.repository  IntRange &com.menteencalma.app.domain.repository  List &com.menteencalma.app.domain.repository  Long &com.menteencalma.app.domain.repository  Map &com.menteencalma.app.domain.repository  Result &com.menteencalma.app.domain.repository  String &com.menteencalma.app.domain.repository  Unit &com.menteencalma.app.domain.repository  UserRepository &com.menteencalma.app.domain.repository  FirebaseUser 5com.menteencalma.app.domain.repository.AuthRepository  Result 5com.menteencalma.app.domain.repository.AuthRepository  String 5com.menteencalma.app.domain.repository.AuthRepository  Unit 5com.menteencalma.app.domain.repository.AuthRepository  Any 9com.menteencalma.app.domain.repository.DatabaseRepository  Article 9com.menteencalma.app.domain.repository.DatabaseRepository  Boolean 9com.menteencalma.app.domain.repository.DatabaseRepository  ChatMessage 9com.menteencalma.app.domain.repository.DatabaseRepository  DatabaseOperation 9com.menteencalma.app.domain.repository.DatabaseRepository  Flow 9com.menteencalma.app.domain.repository.DatabaseRepository  Int 9com.menteencalma.app.domain.repository.DatabaseRepository  IntRange 9com.menteencalma.app.domain.repository.DatabaseRepository  List 9com.menteencalma.app.domain.repository.DatabaseRepository  Long 9com.menteencalma.app.domain.repository.DatabaseRepository  Map 9com.menteencalma.app.domain.repository.DatabaseRepository  Result 9com.menteencalma.app.domain.repository.DatabaseRepository  String 9com.menteencalma.app.domain.repository.DatabaseRepository  Subscription 9com.menteencalma.app.domain.repository.DatabaseRepository  Unit 9com.menteencalma.app.domain.repository.DatabaseRepository  User 9com.menteencalma.app.domain.repository.DatabaseRepository  ChatMessage Kcom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation  DatabaseOperation Kcom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation  Subscription Kcom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation  User Kcom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation  ChatMessage Ycom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation.CreateMessage  User Vcom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation.CreateUser  Subscription ^com.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation.UpdateSubscription  User Vcom.menteencalma.app.domain.repository.DatabaseRepository.DatabaseOperation.UpdateUser  Flow 5com.menteencalma.app.domain.repository.UserRepository  Long 5com.menteencalma.app.domain.repository.UserRepository  Result 5com.menteencalma.app.domain.repository.UserRepository  String 5com.menteencalma.app.domain.repository.UserRepository  Unit 5com.menteencalma.app.domain.repository.UserRepository  User 5com.menteencalma.app.domain.repository.UserRepository  Articles com.menteencalma.app.navigation  Chat com.menteencalma.app.navigation  
CreateProfile com.menteencalma.app.navigation  
Disclaimer com.menteencalma.app.navigation  Doctors com.menteencalma.app.navigation  Login com.menteencalma.app.navigation  MenteEnCalmaNavigation com.menteencalma.app.navigation  MoodTracker com.menteencalma.app.navigation  Profile com.menteencalma.app.navigation  Recommendations com.menteencalma.app.navigation  Screen com.menteencalma.app.navigation  Splash com.menteencalma.app.navigation  String com.menteencalma.app.navigation  	Subscribe com.menteencalma.app.navigation  setOf com.menteencalma.app.navigation  Articles &com.menteencalma.app.navigation.Screen  Chat &com.menteencalma.app.navigation.Screen  
CreateProfile &com.menteencalma.app.navigation.Screen  
Disclaimer &com.menteencalma.app.navigation.Screen  Doctors &com.menteencalma.app.navigation.Screen  Login &com.menteencalma.app.navigation.Screen  MoodTracker &com.menteencalma.app.navigation.Screen  Profile &com.menteencalma.app.navigation.Screen  Recommendations &com.menteencalma.app.navigation.Screen  Screen &com.menteencalma.app.navigation.Screen  Splash &com.menteencalma.app.navigation.Screen  String &com.menteencalma.app.navigation.Screen  	Subscribe &com.menteencalma.app.navigation.Screen  setOf &com.menteencalma.app.navigation.Screen  String 4com.menteencalma.app.navigation.Screen.ArticleDetail  route /com.menteencalma.app.navigation.Screen.Articles  route +com.menteencalma.app.navigation.Screen.Chat  Articles 0com.menteencalma.app.navigation.Screen.Companion  Chat 0com.menteencalma.app.navigation.Screen.Companion  
CreateProfile 0com.menteencalma.app.navigation.Screen.Companion  
Disclaimer 0com.menteencalma.app.navigation.Screen.Companion  Doctors 0com.menteencalma.app.navigation.Screen.Companion  Login 0com.menteencalma.app.navigation.Screen.Companion  MoodTracker 0com.menteencalma.app.navigation.Screen.Companion  Profile 0com.menteencalma.app.navigation.Screen.Companion  Recommendations 0com.menteencalma.app.navigation.Screen.Companion  Screen 0com.menteencalma.app.navigation.Screen.Companion  Splash 0com.menteencalma.app.navigation.Screen.Companion  String 0com.menteencalma.app.navigation.Screen.Companion  	Subscribe 0com.menteencalma.app.navigation.Screen.Companion  getSETOf 0com.menteencalma.app.navigation.Screen.Companion  getSetOf 0com.menteencalma.app.navigation.Screen.Companion  setOf 0com.menteencalma.app.navigation.Screen.Companion  route 4com.menteencalma.app.navigation.Screen.CreateProfile  route 1com.menteencalma.app.navigation.Screen.Disclaimer  route .com.menteencalma.app.navigation.Screen.Doctors  route ,com.menteencalma.app.navigation.Screen.Login  route 2com.menteencalma.app.navigation.Screen.MoodTracker  route .com.menteencalma.app.navigation.Screen.Profile  route 6com.menteencalma.app.navigation.Screen.Recommendations  route -com.menteencalma.app.navigation.Screen.Splash  route 0com.menteencalma.app.navigation.Screen.Subscribe  
BottomNavItem ,com.menteencalma.app.presentation.components  BottomNavigationBar ,com.menteencalma.app.presentation.components  Int ,com.menteencalma.app.presentation.components  String ,com.menteencalma.app.presentation.components  ImageVector :com.menteencalma.app.presentation.components.BottomNavItem  Int :com.menteencalma.app.presentation.components.BottomNavItem  String :com.menteencalma.app.presentation.components.BottomNavItem  	AlertItem /com.menteencalma.app.presentation.screens.admin  	AlertsTab /com.menteencalma.app.presentation.screens.admin  Boolean /com.menteencalma.app.presentation.screens.admin  
Composable /com.menteencalma.app.presentation.screens.admin  DatabaseMonitoringScreen /com.menteencalma.app.presentation.screens.admin  ExperimentalMaterial3Api /com.menteencalma.app.presentation.screens.admin  
MetricItem /com.menteencalma.app.presentation.screens.admin  
MetricsTab /com.menteencalma.app.presentation.screens.admin  MigrationEstimateCard /com.menteencalma.app.presentation.screens.admin  MigrationTab /com.menteencalma.app.presentation.screens.admin  OptIn /com.menteencalma.app.presentation.screens.admin  RecommendationItem /com.menteencalma.app.presentation.screens.admin  String /com.menteencalma.app.presentation.screens.admin  Unit /com.menteencalma.app.presentation.screens.admin  ValidationReportCard /com.menteencalma.app.presentation.screens.admin  androidx /com.menteencalma.app.presentation.screens.admin  com /com.menteencalma.app.presentation.screens.admin  ArticleDetailScreen 2com.menteencalma.app.presentation.screens.articles  ArticleGenerationCard 2com.menteencalma.app.presentation.screens.articles  ArticlePaywallCard 2com.menteencalma.app.presentation.screens.articles  ArticleStats 2com.menteencalma.app.presentation.screens.articles  ArticlesHeader 2com.menteencalma.app.presentation.screens.articles  ArticlesScreen 2com.menteencalma.app.presentation.screens.articles  ArticlesUiState 2com.menteencalma.app.presentation.screens.articles  ArticlesViewModel 2com.menteencalma.app.presentation.screens.articles  Boolean 2com.menteencalma.app.presentation.screens.articles  
Composable 2com.menteencalma.app.presentation.screens.articles  	ErrorCard 2com.menteencalma.app.presentation.screens.articles  GeneratedArticleCard 2com.menteencalma.app.presentation.screens.articles  Int 2com.menteencalma.app.presentation.screens.articles  List 2com.menteencalma.app.presentation.screens.articles  MutableStateFlow 2com.menteencalma.app.presentation.screens.articles  SavedArticleCard 2com.menteencalma.app.presentation.screens.articles  SavedArticlesSection 2com.menteencalma.app.presentation.screens.articles  String 2com.menteencalma.app.presentation.screens.articles  Unit 2com.menteencalma.app.presentation.screens.articles  asStateFlow 2com.menteencalma.app.presentation.screens.articles  Int ?com.menteencalma.app.presentation.screens.articles.ArticleStats  String ?com.menteencalma.app.presentation.screens.articles.ArticleStats  Boolean Bcom.menteencalma.app.presentation.screens.articles.ArticlesUiState  GeneratedArticle Bcom.menteencalma.app.presentation.screens.articles.ArticlesUiState  List Bcom.menteencalma.app.presentation.screens.articles.ArticlesUiState  String Bcom.menteencalma.app.presentation.screens.articles.ArticlesUiState  User Bcom.menteencalma.app.presentation.screens.articles.ArticlesUiState  ArticleStats Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  ArticlesUiState Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  AuthRepository Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  Boolean Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  CloudFunctionsService Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  DatabaseRepository Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  FirebaseFirestore Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  Flow Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  GeneratedArticle Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  Inject Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  Int Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  List Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  ListenerRegistration Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  MutableStateFlow Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  	StateFlow Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  String Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  _uiState Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  asStateFlow Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  getASStateFlow Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  getAsStateFlow Dcom.menteencalma.app.presentation.screens.articles.ArticlesViewModel  Boolean .com.menteencalma.app.presentation.screens.auth  
Composable .com.menteencalma.app.presentation.screens.auth  CreateProfileScreen .com.menteencalma.app.presentation.screens.auth  ExperimentalMaterial3Api .com.menteencalma.app.presentation.screens.auth  GenderSelection .com.menteencalma.app.presentation.screens.auth  Int .com.menteencalma.app.presentation.screens.auth  LoginScreen .com.menteencalma.app.presentation.screens.auth  LoginTabContent .com.menteencalma.app.presentation.screens.auth  LogoSection .com.menteencalma.app.presentation.screens.auth  OptIn .com.menteencalma.app.presentation.screens.auth  ProfileForm .com.menteencalma.app.presentation.screens.auth  
ProfileHeader .com.menteencalma.app.presentation.screens.auth  RegisterTabContent .com.menteencalma.app.presentation.screens.auth  String .com.menteencalma.app.presentation.screens.auth  
TabSection .com.menteencalma.app.presentation.screens.auth  
TherapistCard .com.menteencalma.app.presentation.screens.auth  TherapistSelection .com.menteencalma.app.presentation.screens.auth  Unit .com.menteencalma.app.presentation.screens.auth  com .com.menteencalma.app.presentation.screens.auth  Boolean .com.menteencalma.app.presentation.screens.chat  ChatInputBar .com.menteencalma.app.presentation.screens.chat  ChatMessageBubble .com.menteencalma.app.presentation.screens.chat  ChatMessagesList .com.menteencalma.app.presentation.screens.chat  
ChatScreen .com.menteencalma.app.presentation.screens.chat  
ChatTopBar .com.menteencalma.app.presentation.screens.chat  ChatUiState .com.menteencalma.app.presentation.screens.chat  
ChatViewModel .com.menteencalma.app.presentation.screens.chat  
Composable .com.menteencalma.app.presentation.screens.chat  ConversationStats .com.menteencalma.app.presentation.screens.chat  
EmptyState .com.menteencalma.app.presentation.screens.chat  
ErrorSnackbar .com.menteencalma.app.presentation.screens.chat  ExperimentalMaterial3Api .com.menteencalma.app.presentation.screens.chat  Int .com.menteencalma.app.presentation.screens.chat  List .com.menteencalma.app.presentation.screens.chat  LoadingState .com.menteencalma.app.presentation.screens.chat  Long .com.menteencalma.app.presentation.screens.chat  MutableStateFlow .com.menteencalma.app.presentation.screens.chat  OptIn .com.menteencalma.app.presentation.screens.chat  PaywallBottomBar .com.menteencalma.app.presentation.screens.chat  String .com.menteencalma.app.presentation.screens.chat  Unit .com.menteencalma.app.presentation.screens.chat  androidx .com.menteencalma.app.presentation.screens.chat  asStateFlow .com.menteencalma.app.presentation.screens.chat  Boolean :com.menteencalma.app.presentation.screens.chat.ChatUiState  ChatMessage :com.menteencalma.app.presentation.screens.chat.ChatUiState  List :com.menteencalma.app.presentation.screens.chat.ChatUiState  String :com.menteencalma.app.presentation.screens.chat.ChatUiState  User :com.menteencalma.app.presentation.screens.chat.ChatUiState  AuthRepository <com.menteencalma.app.presentation.screens.chat.ChatViewModel  ChatMessage <com.menteencalma.app.presentation.screens.chat.ChatViewModel  ChatUiState <com.menteencalma.app.presentation.screens.chat.ChatViewModel  CloudFunctionsService <com.menteencalma.app.presentation.screens.chat.ChatViewModel  ConversationStats <com.menteencalma.app.presentation.screens.chat.ChatViewModel  DatabaseRepository <com.menteencalma.app.presentation.screens.chat.ChatViewModel  Inject <com.menteencalma.app.presentation.screens.chat.ChatViewModel  List <com.menteencalma.app.presentation.screens.chat.ChatViewModel  Long <com.menteencalma.app.presentation.screens.chat.ChatViewModel  MutableStateFlow <com.menteencalma.app.presentation.screens.chat.ChatViewModel  	StateFlow <com.menteencalma.app.presentation.screens.chat.ChatViewModel  String <com.menteencalma.app.presentation.screens.chat.ChatViewModel  _uiState <com.menteencalma.app.presentation.screens.chat.ChatViewModel  asStateFlow <com.menteencalma.app.presentation.screens.chat.ChatViewModel  getASStateFlow <com.menteencalma.app.presentation.screens.chat.ChatViewModel  getAsStateFlow <com.menteencalma.app.presentation.screens.chat.ChatViewModel  Int @com.menteencalma.app.presentation.screens.chat.ConversationStats  Long @com.menteencalma.app.presentation.screens.chat.ConversationStats  AILimitationsSection 4com.menteencalma.app.presentation.screens.disclaimer  
AIToolSection 4com.menteencalma.app.presentation.screens.disclaimer  
Composable 4com.menteencalma.app.presentation.screens.disclaimer  ContactSection 4com.menteencalma.app.presentation.screens.disclaimer  DisclaimerContent 4com.menteencalma.app.presentation.screens.disclaimer  DisclaimerHeader 4com.menteencalma.app.presentation.screens.disclaimer  DisclaimerScreen 4com.menteencalma.app.presentation.screens.disclaimer  EmergencySection 4com.menteencalma.app.presentation.screens.disclaimer  ExperimentalMaterial3Api 4com.menteencalma.app.presentation.screens.disclaimer  NotProfessionalHelpSection 4com.menteencalma.app.presentation.screens.disclaimer  OptIn 4com.menteencalma.app.presentation.screens.disclaimer  Unit 4com.menteencalma.app.presentation.screens.disclaimer  UserResponsibilitySection 4com.menteencalma.app.presentation.screens.disclaimer  ComingSoonCard 1com.menteencalma.app.presentation.screens.doctors  
Composable 1com.menteencalma.app.presentation.screens.doctors  ContactCard 1com.menteencalma.app.presentation.screens.doctors  
DoctorsHeader 1com.menteencalma.app.presentation.screens.doctors  
DoctorsScreen 1com.menteencalma.app.presentation.screens.doctors  EmergencyResourcesCard 1com.menteencalma.app.presentation.screens.doctors  ExperimentalMaterial3Api 1com.menteencalma.app.presentation.screens.doctors  FeatureItem 1com.menteencalma.app.presentation.screens.doctors  
FeatureRow 1com.menteencalma.app.presentation.screens.doctors  FeaturesPreview 1com.menteencalma.app.presentation.screens.doctors  OptIn 1com.menteencalma.app.presentation.screens.doctors  String 1com.menteencalma.app.presentation.screens.doctors  androidx 1com.menteencalma.app.presentation.screens.doctors  String =com.menteencalma.app.presentation.screens.doctors.FeatureItem  androidx =com.menteencalma.app.presentation.screens.doctors.FeatureItem  AnalyticItem .com.menteencalma.app.presentation.screens.mood  AnalyticsOverview .com.menteencalma.app.presentation.screens.mood  Boolean .com.menteencalma.app.presentation.screens.mood  
Composable .com.menteencalma.app.presentation.screens.mood  	ErrorCard .com.menteencalma.app.presentation.screens.mood  ExperimentalMaterial3Api .com.menteencalma.app.presentation.screens.mood  Float .com.menteencalma.app.presentation.screens.mood  Int .com.menteencalma.app.presentation.screens.mood  List .com.menteencalma.app.presentation.screens.mood  LoadingState .com.menteencalma.app.presentation.screens.mood  Long .com.menteencalma.app.presentation.screens.mood  
MoodButton .com.menteencalma.app.presentation.screens.mood  	MoodChart .com.menteencalma.app.presentation.screens.mood  
MoodEntryForm .com.menteencalma.app.presentation.screens.mood  
MoodEntryItem .com.menteencalma.app.presentation.screens.mood  MoodTrackerScreen .com.menteencalma.app.presentation.screens.mood  OptIn .com.menteencalma.app.presentation.screens.mood  Pair .com.menteencalma.app.presentation.screens.mood  RecentEntriesSection .com.menteencalma.app.presentation.screens.mood  String .com.menteencalma.app.presentation.screens.mood  SuccessCard .com.menteencalma.app.presentation.screens.mood  Unit .com.menteencalma.app.presentation.screens.mood  androidx .com.menteencalma.app.presentation.screens.mood  getMoodColor .com.menteencalma.app.presentation.screens.mood  Boolean 1com.menteencalma.app.presentation.screens.profile  
Composable 1com.menteencalma.app.presentation.screens.profile  	ErrorCard 1com.menteencalma.app.presentation.screens.profile  ExperimentalMaterial3Api 1com.menteencalma.app.presentation.screens.profile  LoadingState 1com.menteencalma.app.presentation.screens.profile  OptIn 1com.menteencalma.app.presentation.screens.profile  ProfileEditCard 1com.menteencalma.app.presentation.screens.profile  
ProfileHeader 1com.menteencalma.app.presentation.screens.profile  
ProfileScreen 1com.menteencalma.app.presentation.screens.profile  ProfileViewModel 1com.menteencalma.app.presentation.screens.profile  String 1com.menteencalma.app.presentation.screens.profile  SubscriptionCard 1com.menteencalma.app.presentation.screens.profile  SuccessCard 1com.menteencalma.app.presentation.screens.profile  TherapistSelectionCard 1com.menteencalma.app.presentation.screens.profile  Unit 1com.menteencalma.app.presentation.screens.profile  com 1com.menteencalma.app.presentation.screens.profile  Inject Bcom.menteencalma.app.presentation.screens.profile.ProfileViewModel  Boolean 9com.menteencalma.app.presentation.screens.recommendations  
Composable 9com.menteencalma.app.presentation.screens.recommendations  	ErrorCard 9com.menteencalma.app.presentation.screens.recommendations  GetRecommendationButton 9com.menteencalma.app.presentation.screens.recommendations  List 9com.menteencalma.app.presentation.screens.recommendations  MutableStateFlow 9com.menteencalma.app.presentation.screens.recommendations  RecommendationCard 9com.menteencalma.app.presentation.screens.recommendations  RecommendationHistory 9com.menteencalma.app.presentation.screens.recommendations  RecommendationHistoryItem 9com.menteencalma.app.presentation.screens.recommendations  RecommendationsHeader 9com.menteencalma.app.presentation.screens.recommendations  RecommendationsScreen 9com.menteencalma.app.presentation.screens.recommendations  RecommendationsUiState 9com.menteencalma.app.presentation.screens.recommendations  RecommendationsViewModel 9com.menteencalma.app.presentation.screens.recommendations  String 9com.menteencalma.app.presentation.screens.recommendations  Unit 9com.menteencalma.app.presentation.screens.recommendations  asStateFlow 9com.menteencalma.app.presentation.screens.recommendations  Boolean Pcom.menteencalma.app.presentation.screens.recommendations.RecommendationsUiState  List Pcom.menteencalma.app.presentation.screens.recommendations.RecommendationsUiState  Recommendation Pcom.menteencalma.app.presentation.screens.recommendations.RecommendationsUiState  String Pcom.menteencalma.app.presentation.screens.recommendations.RecommendationsUiState  User Pcom.menteencalma.app.presentation.screens.recommendations.RecommendationsUiState  AuthRepository Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  Boolean Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  CloudFunctionsService Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  DatabaseRepository Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  Inject Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  List Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  MutableStateFlow Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  Recommendation Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  RecommendationsUiState Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  	StateFlow Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  String Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  _uiState Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  asStateFlow Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  getASStateFlow Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  getAsStateFlow Rcom.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel  SplashScreen 0com.menteencalma.app.presentation.screens.splash  Unit 0com.menteencalma.app.presentation.screens.splash  Boolean 6com.menteencalma.app.presentation.screens.subscription  
Composable 6com.menteencalma.app.presentation.screens.subscription  	ErrorCard 6com.menteencalma.app.presentation.screens.subscription  ExperimentalMaterial3Api 6com.menteencalma.app.presentation.screens.subscription  
FeatureRow 6com.menteencalma.app.presentation.screens.subscription  FeaturesComparisonTable 6com.menteencalma.app.presentation.screens.subscription  FreePlanCard 6com.menteencalma.app.presentation.screens.subscription  List 6com.menteencalma.app.presentation.screens.subscription  LoadingState 6com.menteencalma.app.presentation.screens.subscription  OptIn 6com.menteencalma.app.presentation.screens.subscription  PlanCard 6com.menteencalma.app.presentation.screens.subscription  PlansSection 6com.menteencalma.app.presentation.screens.subscription  String 6com.menteencalma.app.presentation.screens.subscription  SubscribeScreen 6com.menteencalma.app.presentation.screens.subscription  SubscriptionHeader 6com.menteencalma.app.presentation.screens.subscription  SuccessCard 6com.menteencalma.app.presentation.screens.subscription  TermsAndPrivacy 6com.menteencalma.app.presentation.screens.subscription  Unit 6com.menteencalma.app.presentation.screens.subscription  com 6com.menteencalma.app.presentation.screens.subscription  Any ,com.menteencalma.app.presentation.viewmodels  	AuthState ,com.menteencalma.app.presentation.viewmodels  
AuthViewModel ,com.menteencalma.app.presentation.viewmodels  Boolean ,com.menteencalma.app.presentation.viewmodels  CreateProfileUiState ,com.menteencalma.app.presentation.viewmodels  CreateProfileViewModel ,com.menteencalma.app.presentation.viewmodels  DatabaseMonitoringUiState ,com.menteencalma.app.presentation.viewmodels  DatabaseMonitoringViewModel ,com.menteencalma.app.presentation.viewmodels  Double ,com.menteencalma.app.presentation.viewmodels  Float ,com.menteencalma.app.presentation.viewmodels  Int ,com.menteencalma.app.presentation.viewmodels  List ,com.menteencalma.app.presentation.viewmodels  LoginUiState ,com.menteencalma.app.presentation.viewmodels  LoginViewModel ,com.menteencalma.app.presentation.viewmodels  Long ,com.menteencalma.app.presentation.viewmodels  
MoodAnalytics ,com.menteencalma.app.presentation.viewmodels  MoodTrackerUiState ,com.menteencalma.app.presentation.viewmodels  MoodTrackerViewModel ,com.menteencalma.app.presentation.viewmodels  MutableStateFlow ,com.menteencalma.app.presentation.viewmodels  Pair ,com.menteencalma.app.presentation.viewmodels  ProfileUiState ,com.menteencalma.app.presentation.viewmodels  ProfileViewModel ,com.menteencalma.app.presentation.viewmodels  String ,com.menteencalma.app.presentation.viewmodels  SubscribeUiState ,com.menteencalma.app.presentation.viewmodels  SubscribeViewModel ,com.menteencalma.app.presentation.viewmodels  SubscriptionDisplayInfo ,com.menteencalma.app.presentation.viewmodels  SubscriptionFeature ,com.menteencalma.app.presentation.viewmodels  SubscriptionPlan ,com.menteencalma.app.presentation.viewmodels  	Throwable ,com.menteencalma.app.presentation.viewmodels  asStateFlow ,com.menteencalma.app.presentation.viewmodels  com ,com.menteencalma.app.presentation.viewmodels  AuthRepository :com.menteencalma.app.presentation.viewmodels.AuthViewModel  	AuthState :com.menteencalma.app.presentation.viewmodels.AuthViewModel  Boolean :com.menteencalma.app.presentation.viewmodels.AuthViewModel  Inject :com.menteencalma.app.presentation.viewmodels.AuthViewModel  Int :com.menteencalma.app.presentation.viewmodels.AuthViewModel  List :com.menteencalma.app.presentation.viewmodels.AuthViewModel  MutableStateFlow :com.menteencalma.app.presentation.viewmodels.AuthViewModel  	StateFlow :com.menteencalma.app.presentation.viewmodels.AuthViewModel  String :com.menteencalma.app.presentation.viewmodels.AuthViewModel  User :com.menteencalma.app.presentation.viewmodels.AuthViewModel  UserRepository :com.menteencalma.app.presentation.viewmodels.AuthViewModel  
_authState :com.menteencalma.app.presentation.viewmodels.AuthViewModel  _currentUser :com.menteencalma.app.presentation.viewmodels.AuthViewModel  asStateFlow :com.menteencalma.app.presentation.viewmodels.AuthViewModel  getASStateFlow :com.menteencalma.app.presentation.viewmodels.AuthViewModel  getAsStateFlow :com.menteencalma.app.presentation.viewmodels.AuthViewModel  Boolean Acom.menteencalma.app.presentation.viewmodels.CreateProfileUiState  String Acom.menteencalma.app.presentation.viewmodels.CreateProfileUiState  AuthRepository Ccom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel  CreateProfileUiState Ccom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel  FirebaseFunctions Ccom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel  Inject Ccom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel  MutableStateFlow Ccom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel  	StateFlow Ccom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel  String Ccom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel  UserRepository Ccom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel  _uiState Ccom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel  asStateFlow Ccom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel  getASStateFlow Ccom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel  getAsStateFlow Ccom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel  AuthRepository Mcom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.Companion  CreateProfileUiState Mcom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.Companion  FirebaseFunctions Mcom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.Companion  Inject Mcom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.Companion  MutableStateFlow Mcom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.Companion  	StateFlow Mcom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.Companion  String Mcom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.Companion  UserRepository Mcom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.Companion  asStateFlow Mcom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.Companion  getASStateFlow Mcom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.Companion  getAsStateFlow Mcom.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.Companion  Boolean Fcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState  Double Fcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState  List Fcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState  MigrationEstimate Fcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState  OptimizationRecommendation Fcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState  String Fcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState  TargetDatabase Fcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState  com Fcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState  DatabaseMigrationService Hcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel  DatabaseMonitoringService Hcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel  DatabaseMonitoringUiState Hcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel  Inject Hcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel  MutableStateFlow Hcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel  	StateFlow Hcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel  TargetDatabase Hcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel  _uiState Hcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel  asStateFlow Hcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel  getASStateFlow Hcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel  getAsStateFlow Hcom.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel  Boolean 9com.menteencalma.app.presentation.viewmodels.LoginUiState  String 9com.menteencalma.app.presentation.viewmodels.LoginUiState  AuthRepository ;com.menteencalma.app.presentation.viewmodels.LoginViewModel  Boolean ;com.menteencalma.app.presentation.viewmodels.LoginViewModel  Inject ;com.menteencalma.app.presentation.viewmodels.LoginViewModel  LoginUiState ;com.menteencalma.app.presentation.viewmodels.LoginViewModel  MutableStateFlow ;com.menteencalma.app.presentation.viewmodels.LoginViewModel  	StateFlow ;com.menteencalma.app.presentation.viewmodels.LoginViewModel  String ;com.menteencalma.app.presentation.viewmodels.LoginViewModel  	Throwable ;com.menteencalma.app.presentation.viewmodels.LoginViewModel  UserRepository ;com.menteencalma.app.presentation.viewmodels.LoginViewModel  _uiState ;com.menteencalma.app.presentation.viewmodels.LoginViewModel  asStateFlow ;com.menteencalma.app.presentation.viewmodels.LoginViewModel  getASStateFlow ;com.menteencalma.app.presentation.viewmodels.LoginViewModel  getAsStateFlow ;com.menteencalma.app.presentation.viewmodels.LoginViewModel  Float :com.menteencalma.app.presentation.viewmodels.MoodAnalytics  Int :com.menteencalma.app.presentation.viewmodels.MoodAnalytics  String :com.menteencalma.app.presentation.viewmodels.MoodAnalytics  Boolean ?com.menteencalma.app.presentation.viewmodels.MoodTrackerUiState  Float ?com.menteencalma.app.presentation.viewmodels.MoodTrackerUiState  Int ?com.menteencalma.app.presentation.viewmodels.MoodTrackerUiState  List ?com.menteencalma.app.presentation.viewmodels.MoodTrackerUiState  	MoodEntry ?com.menteencalma.app.presentation.viewmodels.MoodTrackerUiState  String ?com.menteencalma.app.presentation.viewmodels.MoodTrackerUiState  User ?com.menteencalma.app.presentation.viewmodels.MoodTrackerUiState  AuthRepository Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  DatabaseRepository Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  FirebaseFirestore Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  Float Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  Flow Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  Inject Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  Int Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  List Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  ListenerRegistration Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  Long Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  
MoodAnalytics Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  	MoodEntry Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  MoodTrackerUiState Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  MutableStateFlow Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  Pair Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  	StateFlow Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  String Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  _uiState Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  asStateFlow Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  getASStateFlow Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  getAsStateFlow Acom.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel  Boolean ;com.menteencalma.app.presentation.viewmodels.ProfileUiState  Long ;com.menteencalma.app.presentation.viewmodels.ProfileUiState  String ;com.menteencalma.app.presentation.viewmodels.ProfileUiState  User ;com.menteencalma.app.presentation.viewmodels.ProfileUiState  AuthRepository =com.menteencalma.app.presentation.viewmodels.ProfileViewModel  DatabaseRepository =com.menteencalma.app.presentation.viewmodels.ProfileViewModel  Inject =com.menteencalma.app.presentation.viewmodels.ProfileViewModel  MutableStateFlow =com.menteencalma.app.presentation.viewmodels.ProfileViewModel  ProfileUiState =com.menteencalma.app.presentation.viewmodels.ProfileViewModel  	StateFlow =com.menteencalma.app.presentation.viewmodels.ProfileViewModel  String =com.menteencalma.app.presentation.viewmodels.ProfileViewModel  SubscriptionDisplayInfo =com.menteencalma.app.presentation.viewmodels.ProfileViewModel  _uiState =com.menteencalma.app.presentation.viewmodels.ProfileViewModel  asStateFlow =com.menteencalma.app.presentation.viewmodels.ProfileViewModel  getASStateFlow =com.menteencalma.app.presentation.viewmodels.ProfileViewModel  getAsStateFlow =com.menteencalma.app.presentation.viewmodels.ProfileViewModel  Boolean =com.menteencalma.app.presentation.viewmodels.SubscribeUiState  List =com.menteencalma.app.presentation.viewmodels.SubscribeUiState  String =com.menteencalma.app.presentation.viewmodels.SubscribeUiState  SubscriptionPlan =com.menteencalma.app.presentation.viewmodels.SubscribeUiState  User =com.menteencalma.app.presentation.viewmodels.SubscribeUiState  Any ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  AuthRepository ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  Boolean ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  DatabaseRepository ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  Inject ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  List ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  MutableStateFlow ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  	StateFlow ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  String ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  SubscribeUiState ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  SubscriptionFeature ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  SubscriptionPlan ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  _uiState ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  asStateFlow ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  getASStateFlow ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  getAsStateFlow ?com.menteencalma.app.presentation.viewmodels.SubscribeViewModel  Boolean Dcom.menteencalma.app.presentation.viewmodels.SubscriptionDisplayInfo  String Dcom.menteencalma.app.presentation.viewmodels.SubscriptionDisplayInfo  Boolean @com.menteencalma.app.presentation.viewmodels.SubscriptionFeature  String @com.menteencalma.app.presentation.viewmodels.SubscriptionFeature  Boolean =com.menteencalma.app.presentation.viewmodels.SubscriptionPlan  List =com.menteencalma.app.presentation.viewmodels.SubscriptionPlan  String =com.menteencalma.app.presentation.viewmodels.SubscriptionPlan  SubscriptionFeature =com.menteencalma.app.presentation.viewmodels.SubscriptionPlan  Boolean com.menteencalma.app.ui.theme  DarkColorScheme com.menteencalma.app.ui.theme  LightColorScheme com.menteencalma.app.ui.theme  MenteEnCalmaBackgroundDark com.menteencalma.app.ui.theme  MenteEnCalmaBackgroundLight com.menteencalma.app.ui.theme  MenteEnCalmaError com.menteencalma.app.ui.theme  MenteEnCalmaInfo com.menteencalma.app.ui.theme  MenteEnCalmaOnBackgroundDark com.menteencalma.app.ui.theme  MenteEnCalmaOnBackgroundLight com.menteencalma.app.ui.theme  MenteEnCalmaOnPrimary com.menteencalma.app.ui.theme  MenteEnCalmaOnSecondary com.menteencalma.app.ui.theme  MenteEnCalmaOnSurfaceDark com.menteencalma.app.ui.theme  MenteEnCalmaOnSurfaceLight com.menteencalma.app.ui.theme  MenteEnCalmaOnTertiary com.menteencalma.app.ui.theme  MenteEnCalmaPrimary com.menteencalma.app.ui.theme  MenteEnCalmaSecondary com.menteencalma.app.ui.theme  MenteEnCalmaSuccess com.menteencalma.app.ui.theme  MenteEnCalmaSurfaceDark com.menteencalma.app.ui.theme  MenteEnCalmaSurfaceLight com.menteencalma.app.ui.theme  MenteEnCalmaTertiary com.menteencalma.app.ui.theme  MenteEnCalmaTheme com.menteencalma.app.ui.theme  MenteEnCalmaWarning com.menteencalma.app.ui.theme  
Typography com.menteencalma.app.ui.theme  Unit com.menteencalma.app.ui.theme  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  SingletonComponent dagger.hilt.components  AnnotationRetention 	java.lang  Articles 	java.lang  ArticlesUiState 	java.lang  	AuthState 	java.lang  Chat 	java.lang  ChatUiState 	java.lang  
CreateProfile 	java.lang  CreateProfileUiState 	java.lang  DatabaseMetrics 	java.lang  DatabaseMonitoringUiState 	java.lang  
Disclaimer 	java.lang  Doctors 	java.lang  ExperimentalMaterial3Api 	java.lang  Json 	java.lang  Login 	java.lang  LoginUiState 	java.lang  MigrationStatus 	java.lang  MoodTracker 	java.lang  MoodTrackerUiState 	java.lang  MutableStateFlow 	java.lang  Profile 	java.lang  ProfileUiState 	java.lang  Recommendations 	java.lang  RecommendationsUiState 	java.lang  SingletonComponent 	java.lang  Splash 	java.lang  	Subscribe 	java.lang  SubscribeUiState 	java.lang  androidx 	java.lang  asStateFlow 	java.lang  com 	java.lang  	emptyList 	java.lang  kotlinx 	java.lang  listOf 	java.lang  setOf 	java.lang  	Exception java.lang.Exception  String java.lang.Exception  SimpleDateFormat 	java.text  
Composable 	java.util  ExperimentalMaterial3Api 	java.util  MoodTrackerUiState 	java.util  MutableStateFlow 	java.util  Pair 	java.util  UUID 	java.util  androidx 	java.util  asStateFlow 	java.util  Inject javax.inject  	Qualifier javax.inject  	Singleton javax.inject  AnnotationRetention kotlin  Any kotlin  Articles kotlin  ArticlesUiState kotlin  	AuthState kotlin  Boolean kotlin  Chat kotlin  ChatUiState kotlin  
CreateProfile kotlin  CreateProfileUiState kotlin  DatabaseMetrics kotlin  DatabaseMonitoringUiState kotlin  
Disclaimer kotlin  Doctors kotlin  Double kotlin  	Exception kotlin  ExperimentalMaterial3Api kotlin  Float kotlin  	Function1 kotlin  Int kotlin  Json kotlin  Login kotlin  LoginUiState kotlin  Long kotlin  MigrationStatus kotlin  MoodTracker kotlin  MoodTrackerUiState kotlin  MutableStateFlow kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Profile kotlin  ProfileUiState kotlin  Recommendations kotlin  RecommendationsUiState kotlin  Result kotlin  SingletonComponent kotlin  Splash kotlin  String kotlin  	Subscribe kotlin  SubscribeUiState kotlin  	Throwable kotlin  Unit kotlin  androidx kotlin  asStateFlow kotlin  com kotlin  	emptyList kotlin  kotlinx kotlin  listOf kotlin  setOf kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  AnnotationRetention kotlin.annotation  Articles kotlin.annotation  ArticlesUiState kotlin.annotation  	AuthState kotlin.annotation  Chat kotlin.annotation  ChatUiState kotlin.annotation  
CreateProfile kotlin.annotation  CreateProfileUiState kotlin.annotation  DatabaseMetrics kotlin.annotation  DatabaseMonitoringUiState kotlin.annotation  
Disclaimer kotlin.annotation  Doctors kotlin.annotation  	Exception kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  Json kotlin.annotation  Login kotlin.annotation  LoginUiState kotlin.annotation  MigrationStatus kotlin.annotation  MoodTracker kotlin.annotation  MoodTrackerUiState kotlin.annotation  MutableStateFlow kotlin.annotation  Pair kotlin.annotation  Profile kotlin.annotation  ProfileUiState kotlin.annotation  Recommendations kotlin.annotation  RecommendationsUiState kotlin.annotation  Result kotlin.annotation  	Retention kotlin.annotation  SingletonComponent kotlin.annotation  Splash kotlin.annotation  	Subscribe kotlin.annotation  SubscribeUiState kotlin.annotation  androidx kotlin.annotation  asStateFlow kotlin.annotation  com kotlin.annotation  	emptyList kotlin.annotation  kotlinx kotlin.annotation  listOf kotlin.annotation  setOf kotlin.annotation  BINARY %kotlin.annotation.AnnotationRetention  AnnotationRetention kotlin.collections  Articles kotlin.collections  ArticlesUiState kotlin.collections  	AuthState kotlin.collections  Chat kotlin.collections  ChatUiState kotlin.collections  
CreateProfile kotlin.collections  CreateProfileUiState kotlin.collections  DatabaseMetrics kotlin.collections  DatabaseMonitoringUiState kotlin.collections  
Disclaimer kotlin.collections  Doctors kotlin.collections  	Exception kotlin.collections  ExperimentalMaterial3Api kotlin.collections  Json kotlin.collections  List kotlin.collections  Login kotlin.collections  LoginUiState kotlin.collections  Map kotlin.collections  MigrationStatus kotlin.collections  MoodTracker kotlin.collections  MoodTrackerUiState kotlin.collections  MutableList kotlin.collections  MutableStateFlow kotlin.collections  Pair kotlin.collections  Profile kotlin.collections  ProfileUiState kotlin.collections  Recommendations kotlin.collections  RecommendationsUiState kotlin.collections  Result kotlin.collections  Set kotlin.collections  SingletonComponent kotlin.collections  Splash kotlin.collections  	Subscribe kotlin.collections  SubscribeUiState kotlin.collections  androidx kotlin.collections  asStateFlow kotlin.collections  com kotlin.collections  	emptyList kotlin.collections  kotlinx kotlin.collections  listOf kotlin.collections  setOf kotlin.collections  AnnotationRetention kotlin.comparisons  Articles kotlin.comparisons  ArticlesUiState kotlin.comparisons  	AuthState kotlin.comparisons  Chat kotlin.comparisons  ChatUiState kotlin.comparisons  
CreateProfile kotlin.comparisons  CreateProfileUiState kotlin.comparisons  DatabaseMetrics kotlin.comparisons  DatabaseMonitoringUiState kotlin.comparisons  
Disclaimer kotlin.comparisons  Doctors kotlin.comparisons  	Exception kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  Json kotlin.comparisons  Login kotlin.comparisons  LoginUiState kotlin.comparisons  MigrationStatus kotlin.comparisons  MoodTracker kotlin.comparisons  MoodTrackerUiState kotlin.comparisons  MutableStateFlow kotlin.comparisons  Pair kotlin.comparisons  Profile kotlin.comparisons  ProfileUiState kotlin.comparisons  Recommendations kotlin.comparisons  RecommendationsUiState kotlin.comparisons  Result kotlin.comparisons  SingletonComponent kotlin.comparisons  Splash kotlin.comparisons  	Subscribe kotlin.comparisons  SubscribeUiState kotlin.comparisons  androidx kotlin.comparisons  asStateFlow kotlin.comparisons  com kotlin.comparisons  	emptyList kotlin.comparisons  kotlinx kotlin.comparisons  listOf kotlin.comparisons  setOf kotlin.comparisons  AnnotationRetention 	kotlin.io  Articles 	kotlin.io  ArticlesUiState 	kotlin.io  	AuthState 	kotlin.io  Chat 	kotlin.io  ChatUiState 	kotlin.io  
CreateProfile 	kotlin.io  CreateProfileUiState 	kotlin.io  DatabaseMetrics 	kotlin.io  DatabaseMonitoringUiState 	kotlin.io  
Disclaimer 	kotlin.io  Doctors 	kotlin.io  	Exception 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  Json 	kotlin.io  Login 	kotlin.io  LoginUiState 	kotlin.io  MigrationStatus 	kotlin.io  MoodTracker 	kotlin.io  MoodTrackerUiState 	kotlin.io  MutableStateFlow 	kotlin.io  Pair 	kotlin.io  Profile 	kotlin.io  ProfileUiState 	kotlin.io  Recommendations 	kotlin.io  RecommendationsUiState 	kotlin.io  Result 	kotlin.io  SingletonComponent 	kotlin.io  Splash 	kotlin.io  	Subscribe 	kotlin.io  SubscribeUiState 	kotlin.io  androidx 	kotlin.io  asStateFlow 	kotlin.io  com 	kotlin.io  	emptyList 	kotlin.io  kotlinx 	kotlin.io  listOf 	kotlin.io  setOf 	kotlin.io  AnnotationRetention 
kotlin.jvm  Articles 
kotlin.jvm  ArticlesUiState 
kotlin.jvm  	AuthState 
kotlin.jvm  Chat 
kotlin.jvm  ChatUiState 
kotlin.jvm  
CreateProfile 
kotlin.jvm  CreateProfileUiState 
kotlin.jvm  DatabaseMetrics 
kotlin.jvm  DatabaseMonitoringUiState 
kotlin.jvm  
Disclaimer 
kotlin.jvm  Doctors 
kotlin.jvm  	Exception 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  Json 
kotlin.jvm  Login 
kotlin.jvm  LoginUiState 
kotlin.jvm  MigrationStatus 
kotlin.jvm  MoodTracker 
kotlin.jvm  MoodTrackerUiState 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  Pair 
kotlin.jvm  Profile 
kotlin.jvm  ProfileUiState 
kotlin.jvm  Recommendations 
kotlin.jvm  RecommendationsUiState 
kotlin.jvm  Result 
kotlin.jvm  SingletonComponent 
kotlin.jvm  Splash 
kotlin.jvm  	Subscribe 
kotlin.jvm  SubscribeUiState 
kotlin.jvm  androidx 
kotlin.jvm  asStateFlow 
kotlin.jvm  com 
kotlin.jvm  	emptyList 
kotlin.jvm  kotlinx 
kotlin.jvm  listOf 
kotlin.jvm  setOf 
kotlin.jvm  AnnotationRetention 
kotlin.ranges  Articles 
kotlin.ranges  ArticlesUiState 
kotlin.ranges  	AuthState 
kotlin.ranges  Chat 
kotlin.ranges  ChatUiState 
kotlin.ranges  
CreateProfile 
kotlin.ranges  CreateProfileUiState 
kotlin.ranges  DatabaseMetrics 
kotlin.ranges  DatabaseMonitoringUiState 
kotlin.ranges  
Disclaimer 
kotlin.ranges  Doctors 
kotlin.ranges  	Exception 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  IntRange 
kotlin.ranges  Json 
kotlin.ranges  Login 
kotlin.ranges  LoginUiState 
kotlin.ranges  MigrationStatus 
kotlin.ranges  MoodTracker 
kotlin.ranges  MoodTrackerUiState 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  Pair 
kotlin.ranges  Profile 
kotlin.ranges  ProfileUiState 
kotlin.ranges  Recommendations 
kotlin.ranges  RecommendationsUiState 
kotlin.ranges  Result 
kotlin.ranges  SingletonComponent 
kotlin.ranges  Splash 
kotlin.ranges  	Subscribe 
kotlin.ranges  SubscribeUiState 
kotlin.ranges  androidx 
kotlin.ranges  asStateFlow 
kotlin.ranges  com 
kotlin.ranges  	emptyList 
kotlin.ranges  kotlinx 
kotlin.ranges  listOf 
kotlin.ranges  setOf 
kotlin.ranges  KClass kotlin.reflect  AnnotationRetention kotlin.sequences  Articles kotlin.sequences  ArticlesUiState kotlin.sequences  	AuthState kotlin.sequences  Chat kotlin.sequences  ChatUiState kotlin.sequences  
CreateProfile kotlin.sequences  CreateProfileUiState kotlin.sequences  DatabaseMetrics kotlin.sequences  DatabaseMonitoringUiState kotlin.sequences  
Disclaimer kotlin.sequences  Doctors kotlin.sequences  	Exception kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  Json kotlin.sequences  Login kotlin.sequences  LoginUiState kotlin.sequences  MigrationStatus kotlin.sequences  MoodTracker kotlin.sequences  MoodTrackerUiState kotlin.sequences  MutableStateFlow kotlin.sequences  Pair kotlin.sequences  Profile kotlin.sequences  ProfileUiState kotlin.sequences  Recommendations kotlin.sequences  RecommendationsUiState kotlin.sequences  Result kotlin.sequences  SingletonComponent kotlin.sequences  Splash kotlin.sequences  	Subscribe kotlin.sequences  SubscribeUiState kotlin.sequences  androidx kotlin.sequences  asStateFlow kotlin.sequences  com kotlin.sequences  	emptyList kotlin.sequences  kotlinx kotlin.sequences  listOf kotlin.sequences  setOf kotlin.sequences  measureTimeMillis 
kotlin.system  AnnotationRetention kotlin.text  Articles kotlin.text  ArticlesUiState kotlin.text  	AuthState kotlin.text  Chat kotlin.text  ChatUiState kotlin.text  
CreateProfile kotlin.text  CreateProfileUiState kotlin.text  DatabaseMetrics kotlin.text  DatabaseMonitoringUiState kotlin.text  
Disclaimer kotlin.text  Doctors kotlin.text  	Exception kotlin.text  ExperimentalMaterial3Api kotlin.text  Json kotlin.text  Login kotlin.text  LoginUiState kotlin.text  MigrationStatus kotlin.text  MoodTracker kotlin.text  MoodTrackerUiState kotlin.text  MutableStateFlow kotlin.text  Pair kotlin.text  Profile kotlin.text  ProfileUiState kotlin.text  Recommendations kotlin.text  RecommendationsUiState kotlin.text  Result kotlin.text  SingletonComponent kotlin.text  Splash kotlin.text  	Subscribe kotlin.text  SubscribeUiState kotlin.text  androidx kotlin.text  asStateFlow kotlin.text  com kotlin.text  	emptyList kotlin.text  kotlinx kotlin.text  listOf kotlin.text  setOf kotlin.text  delay kotlinx.coroutines  launch kotlinx.coroutines  
awaitClose kotlinx.coroutines.channels  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  callbackFlow kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  await kotlinx.coroutines.tasks  Serializable kotlinx.serialization  encodeToString kotlinx.serialization  Json kotlinx.serialization.json  JsonBuilder kotlinx.serialization.json  invoke kotlinx.serialization.json.Json  invoke 'kotlinx.serialization.json.Json.Default  ignoreUnknownKeys &kotlinx.serialization.json.JsonBuilder  prettyPrint &kotlinx.serialization.json.JsonBuilder                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         