package com.menteencalma.app.presentation.viewmodels;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u000e\u001a\u00020\u000fJ\u0006\u0010\u0010\u001a\u00020\u000fJ\u0006\u0010\u0011\u001a\u00020\u000fJ\u0006\u0010\u0012\u001a\u00020\u000fJ\u000e\u0010\u0013\u001a\u00020\u000f2\u0006\u0010\u0014\u001a\u00020\u0015J\b\u0010\u0016\u001a\u00020\u000fH\u0002J\u0006\u0010\u0017\u001a\u00020\u000fJ\u0006\u0010\u0018\u001a\u00020\u000fR\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u0019"}, d2 = {"Lcom/menteencalma/app/presentation/viewmodels/DatabaseMonitoringViewModel;", "Landroidx/lifecycle/ViewModel;", "monitoringService", "Lcom/menteencalma/app/data/monitoring/DatabaseMonitoringService;", "migrationService", "Lcom/menteencalma/app/data/migration/DatabaseMigrationService;", "(Lcom/menteencalma/app/data/monitoring/DatabaseMonitoringService;Lcom/menteencalma/app/data/migration/DatabaseMigrationService;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/menteencalma/app/presentation/viewmodels/DatabaseMonitoringUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "clearError", "", "clearMigrationData", "exportData", "generateMigrationEstimate", "generateMigrationScript", "targetDatabase", "Lcom/menteencalma/app/data/migration/TargetDatabase;", "observeMetrics", "resetMetrics", "validateDataIntegrity", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class DatabaseMonitoringViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.data.monitoring.DatabaseMonitoringService monitoringService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.data.migration.DatabaseMigrationService migrationService = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState> uiState = null;
    
    @javax.inject.Inject()
    public DatabaseMonitoringViewModel(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.data.monitoring.DatabaseMonitoringService monitoringService, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.data.migration.DatabaseMigrationService migrationService) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringUiState> getUiState() {
        return null;
    }
    
    private final void observeMetrics() {
    }
    
    public final void generateMigrationEstimate() {
    }
    
    public final void generateMigrationScript(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.data.migration.TargetDatabase targetDatabase) {
    }
    
    public final void exportData() {
    }
    
    public final void validateDataIntegrity() {
    }
    
    public final void resetMetrics() {
    }
    
    public final void clearError() {
    }
    
    public final void clearMigrationData() {
    }
}