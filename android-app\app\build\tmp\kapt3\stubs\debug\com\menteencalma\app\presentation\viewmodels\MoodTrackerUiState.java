package com.menteencalma.app.presentation.viewmodels;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0002\b0\b\u0086\b\u0018\u00002\u00020\u0001B\u00bb\u0001\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\b\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\b\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0010\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u000b\u0012\u000e\b\u0002\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0005\u0012\u000e\b\u0002\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0005\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0015\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0015\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u000b\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u0010\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u0010\u00a2\u0006\u0002\u0010\u001aJ\u000b\u00100\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000f\u00101\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0005H\u00c6\u0003J\u000f\u00102\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0005H\u00c6\u0003J\t\u00103\u001a\u00020\u0015H\u00c6\u0003J\t\u00104\u001a\u00020\u0015H\u00c6\u0003J\t\u00105\u001a\u00020\u000bH\u00c6\u0003J\t\u00106\u001a\u00020\u0010H\u00c6\u0003J\t\u00107\u001a\u00020\u0010H\u00c6\u0003J\u000f\u00108\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u00109\u001a\u00020\bH\u00c6\u0003J\t\u0010:\u001a\u00020\bH\u00c6\u0003J\u000b\u0010;\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J\t\u0010<\u001a\u00020\bH\u00c6\u0003J\t\u0010=\u001a\u00020\u000eH\u00c6\u0003J\t\u0010>\u001a\u00020\u0010H\u00c6\u0003J\t\u0010?\u001a\u00020\u000bH\u00c6\u0003J\u00bf\u0001\u0010@\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\f\u001a\u00020\b2\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u000b2\u000e\b\u0002\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00052\u000e\b\u0002\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00052\b\b\u0002\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u00152\b\b\u0002\u0010\u0017\u001a\u00020\u000b2\b\b\u0002\u0010\u0018\u001a\u00020\u00102\b\b\u0002\u0010\u0019\u001a\u00020\u0010H\u00c6\u0001J\u0013\u0010A\u001a\u00020\b2\b\u0010B\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010C\u001a\u00020\u0010H\u00d6\u0001J\t\u0010D\u001a\u00020\u000bH\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\u0011\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0013\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001eR\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\"R\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\"R\u0011\u0010\u0016\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0011\u0010\u0017\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001eR\u0011\u0010\f\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\"R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010&R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010+R\u0017\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010&R\u0011\u0010\u0018\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010!R\u0011\u0010\u0019\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010!R\u0011\u0010\u0014\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010$\u00a8\u0006E"}, d2 = {"Lcom/menteencalma/app/presentation/viewmodels/MoodTrackerUiState;", "", "currentUser", "Lcom/menteencalma/app/domain/model/User;", "moodEntries", "", "Lcom/menteencalma/app/domain/model/MoodEntry;", "isLoading", "", "isSaving", "errorMessage", "", "saveSuccess", "selectedMood", "Lcom/menteencalma/app/domain/model/MoodEntry$MoodType;", "intensity", "", "description", "selectedTags", "selectedActivities", "weeklyAverage", "", "monthlyAverage", "moodTrend", "streakDays", "totalEntries", "(Lcom/menteencalma/app/domain/model/User;Ljava/util/List;ZZLjava/lang/String;ZLcom/menteencalma/app/domain/model/MoodEntry$MoodType;ILjava/lang/String;Ljava/util/List;Ljava/util/List;FFLjava/lang/String;II)V", "getCurrentUser", "()Lcom/menteencalma/app/domain/model/User;", "getDescription", "()Ljava/lang/String;", "getErrorMessage", "getIntensity", "()I", "()Z", "getMonthlyAverage", "()F", "getMoodEntries", "()Ljava/util/List;", "getMoodTrend", "getSaveSuccess", "getSelectedActivities", "getSelectedMood", "()Lcom/menteencalma/app/domain/model/MoodEntry$MoodType;", "getSelectedTags", "getStreakDays", "getTotalEntries", "getWeeklyAverage", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
public final class MoodTrackerUiState {
    @org.jetbrains.annotations.Nullable()
    private final com.menteencalma.app.domain.model.User currentUser = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.menteencalma.app.domain.model.MoodEntry> moodEntries = null;
    private final boolean isLoading = false;
    private final boolean isSaving = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    private final boolean saveSuccess = false;
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.model.MoodEntry.MoodType selectedMood = null;
    private final int intensity = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String description = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> selectedTags = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> selectedActivities = null;
    private final float weeklyAverage = 0.0F;
    private final float monthlyAverage = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String moodTrend = null;
    private final int streakDays = 0;
    private final int totalEntries = 0;
    
    public MoodTrackerUiState(@org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.User currentUser, @org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.domain.model.MoodEntry> moodEntries, boolean isLoading, boolean isSaving, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, boolean saveSuccess, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.model.MoodEntry.MoodType selectedMood, int intensity, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> selectedTags, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> selectedActivities, float weeklyAverage, float monthlyAverage, @org.jetbrains.annotations.NotNull()
    java.lang.String moodTrend, int streakDays, int totalEntries) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.User getCurrentUser() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.domain.model.MoodEntry> getMoodEntries() {
        return null;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isSaving() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    public final boolean getSaveSuccess() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.domain.model.MoodEntry.MoodType getSelectedMood() {
        return null;
    }
    
    public final int getIntensity() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getSelectedTags() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getSelectedActivities() {
        return null;
    }
    
    public final float getWeeklyAverage() {
        return 0.0F;
    }
    
    public final float getMonthlyAverage() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getMoodTrend() {
        return null;
    }
    
    public final int getStreakDays() {
        return 0;
    }
    
    public final int getTotalEntries() {
        return 0;
    }
    
    public MoodTrackerUiState() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.menteencalma.app.domain.model.User component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component11() {
        return null;
    }
    
    public final float component12() {
        return 0.0F;
    }
    
    public final float component13() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component14() {
        return null;
    }
    
    public final int component15() {
        return 0;
    }
    
    public final int component16() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.menteencalma.app.domain.model.MoodEntry> component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.domain.model.MoodEntry.MoodType component7() {
        return null;
    }
    
    public final int component8() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.menteencalma.app.presentation.viewmodels.MoodTrackerUiState copy(@org.jetbrains.annotations.Nullable()
    com.menteencalma.app.domain.model.User currentUser, @org.jetbrains.annotations.NotNull()
    java.util.List<com.menteencalma.app.domain.model.MoodEntry> moodEntries, boolean isLoading, boolean isSaving, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage, boolean saveSuccess, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.model.MoodEntry.MoodType selectedMood, int intensity, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> selectedTags, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> selectedActivities, float weeklyAverage, float monthlyAverage, @org.jetbrains.annotations.NotNull()
    java.lang.String moodTrend, int streakDays, int totalEntries) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}