package com.menteencalma.app.data.service;

/**
 * Servicio para llamar a Cloud Functions de Firebase
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u0000 /2\u00020\u0001:\u0002//B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J$\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000bH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\f\u0010\rJ@\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0010\u001a\u00020\u000b2\b\b\u0002\u0010\u0011\u001a\u00020\u000b2\b\b\u0002\u0010\u0012\u001a\u00020\u000bH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0013\u0010\u0014J<\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00160\b2\u0006\u0010\n\u001a\u00020\u000b2\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u000bH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0019\u0010\u001aJ\u0010\u0010\u001b\u001a\u00020\u000f2\u0006\u0010\u001c\u001a\u00020\u001dH\u0002J\u0010\u0010\u001e\u001a\u00020\u001f2\u0006\u0010\u001c\u001a\u00020\u001dH\u0002J\u0014\u0010 \u001a\u00020!2\n\u0010\"\u001a\u00060#j\u0002`$H\u0002J\u0010\u0010%\u001a\u00020\u00162\u0006\u0010\u001c\u001a\u00020\u001dH\u0002J\u0010\u0010&\u001a\u00020\t2\u0006\u0010\u001c\u001a\u00020\u001dH\u0002JD\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u001f0\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\u000b2\u0006\u0010)\u001a\u00020\u000b2\u000e\b\u0002\u0010*\u001a\b\u0012\u0004\u0012\u00020,0+H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b-\u0010.R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00060"}, d2 = {"Lcom/menteencalma/app/data/service/CloudFunctionsService;", "", "functions", "Lcom/google/firebase/functions/FirebaseFunctions;", "mockService", "Lcom/menteencalma/app/data/service/MockCloudFunctionsService;", "(Lcom/google/firebase/functions/FirebaseFunctions;Lcom/menteencalma/app/data/service/MockCloudFunctionsService;)V", "createUserProfile", "Lkotlin/Result;", "Lcom/menteencalma/app/data/service/UserProfileResponse;", "userId", "", "createUserProfile-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateArticle", "Lcom/menteencalma/app/data/service/ArticleResponse;", "topic", "difficulty", "length", "generateArticle-yxL6bBk", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPersonalizedRecommendation", "Lcom/menteencalma/app/data/service/RecommendationResponse;", "currentMood", "preferredCategory", "getPersonalizedRecommendation-BWLJW6A", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "parseArticleResponse", "result", "Lcom/google/firebase/functions/HttpsCallableResult;", "parseChatbotResponse", "Lcom/menteencalma/app/data/service/ChatbotResponse;", "parseCloudFunctionError", "Lcom/menteencalma/app/data/service/CloudFunctionException;", "exception", "Ljava/lang/Exception;", "Lkotlin/Exception;", "parseRecommendationResponse", "parseUserProfileResponse", "sendChatMessage", "message", "therapistId", "conversationHistory", "", "Lcom/menteencalma/app/domain/model/ChatMessage;", "sendChatMessage-yxL6bBk", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public final class CloudFunctionsService {
    @org.jetbrains.annotations.NotNull()
    private final com.google.firebase.functions.FirebaseFunctions functions = null;
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.data.service.MockCloudFunctionsService mockService = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.menteencalma.app.data.service.CloudFunctionsService.Companion Companion = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.menteencalma.app.data.service.CloudFunctionsService.Companion Companion = null;
    
    @javax.inject.Inject()
    public CloudFunctionsService(@org.jetbrains.annotations.NotNull()
    com.google.firebase.functions.FirebaseFunctions functions, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.data.service.MockCloudFunctionsService mockService) {
        super();
    }
    
    private final com.menteencalma.app.data.service.ChatbotResponse parseChatbotResponse(com.google.firebase.functions.HttpsCallableResult result) {
        return null;
    }
    
    private final com.menteencalma.app.data.service.RecommendationResponse parseRecommendationResponse(com.google.firebase.functions.HttpsCallableResult result) {
        return null;
    }
    
    private final com.menteencalma.app.data.service.ArticleResponse parseArticleResponse(com.google.firebase.functions.HttpsCallableResult result) {
        return null;
    }
    
    private final com.menteencalma.app.data.service.UserProfileResponse parseUserProfileResponse(com.google.firebase.functions.HttpsCallableResult result) {
        return null;
    }
    
    private final com.menteencalma.app.data.service.CloudFunctionException parseCloudFunctionError(java.lang.Exception exception) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/menteencalma/app/data/service/CloudFunctionsService$Companion;", "", "()V", "AI_CHATBOT_SUPPORT", "", "ARTICLE_LIMIT_REACHED", "CREATE_USER_PROFILE", "GENERATE_ARTICLE", "INTERNAL_ERROR", "INVALID_INPUT", "PERSONALIZED_RECOMMENDATION", "SUBSCRIPTION_REQUIRED", "USAGE_LIMIT_REACHED", "app_debug"})
    public static final class Companion {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String AI_CHATBOT_SUPPORT = "aiChatbotSupport";
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String PERSONALIZED_RECOMMENDATION = "personalizedRecommendation";
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String GENERATE_ARTICLE = "generateArticle";
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String CREATE_USER_PROFILE = "createUserProfile";
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String USAGE_LIMIT_REACHED = "USAGE_LIMIT_REACHED";
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String ARTICLE_LIMIT_REACHED = "ARTICLE_LIMIT_REACHED";
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String SUBSCRIPTION_REQUIRED = "SUBSCRIPTION_REQUIRED";
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String INVALID_INPUT = "INVALID_INPUT";
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String INTERNAL_ERROR = "INTERNAL_ERROR";
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/menteencalma/app/data/service/CloudFunctionsService$Companion;", "", "()V", "AI_CHATBOT_SUPPORT", "", "ARTICLE_LIMIT_REACHED", "CREATE_USER_PROFILE", "GENERATE_ARTICLE", "INTERNAL_ERROR", "INVALID_INPUT", "PERSONALIZED_RECOMMENDATION", "SUBSCRIPTION_REQUIRED", "USAGE_LIMIT_REACHED", "app_debug"})
    public static final class Companion {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String AI_CHATBOT_SUPPORT = "aiChatbotSupport";
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String PERSONALIZED_RECOMMENDATION = "personalizedRecommendation";
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String GENERATE_ARTICLE = "generateArticle";
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String CREATE_USER_PROFILE = "createUserProfile";
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String USAGE_LIMIT_REACHED = "USAGE_LIMIT_REACHED";
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String ARTICLE_LIMIT_REACHED = "ARTICLE_LIMIT_REACHED";
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String SUBSCRIPTION_REQUIRED = "SUBSCRIPTION_REQUIRED";
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String INVALID_INPUT = "INVALID_INPUT";
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String INTERNAL_ERROR = "INTERNAL_ERROR";
        
        private Companion() {
            super();
        }
    }
}