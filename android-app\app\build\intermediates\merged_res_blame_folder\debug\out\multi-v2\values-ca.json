{"logs": [{"outputFile": "com.menteencalma.app-mergeDebugResources-3:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ec6bd2faa3e0a998fc0a19c7dd0c22ed\\transformed\\browser-1.4.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,271,382", "endColumns": "112,102,110,108", "endOffsets": "163,266,377,486"}, "to": {"startLines": "56,60,61,62", "startColumns": "4,4,4,4", "startOffsets": "5990,6399,6502,6613", "endColumns": "112,102,110,108", "endOffsets": "6098,6497,6608,6717"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\0a2f82cac04b16c6074a060829451b01\\transformed\\play-services-basement-18.2.0\\res\\values-ca\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "130", "endOffsets": "325"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4743", "endColumns": "134", "endOffsets": "4873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d60095cfcd5f6da5b72a1052224dd592\\transformed\\ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,390,493,582,660,751,842,928,1000,1069,1155,1246,1322,1404,1475", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "197,281,385,488,577,655,746,837,923,995,1064,1150,1241,1317,1399,1470,1590"}, "to": {"startLines": "36,37,57,58,59,63,64,121,122,123,124,125,126,128,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3561,3658,6103,6207,6310,6722,6800,13189,13280,13366,13438,13507,13593,13766,13943,14025,14096", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "3653,3737,6202,6305,6394,6795,6886,13275,13361,13433,13502,13588,13679,13837,14020,14091,14211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\bc87254a1d5a4bb9d2cd1a5740dba329\\transformed\\play-services-base-18.1.0\\res\\values-ca\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,670,816,939,1058,1162,1329,1434,1589,1716,1876,2030,2091,2155", "endColumns": "100,147,122,104,145,122,118,103,166,104,154,126,159,153,60,63,82", "endOffsets": "293,441,564,669,815,938,1057,1161,1328,1433,1588,1715,1875,2029,2090,2154,2237"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3742,3847,3999,4126,4235,4385,4512,4635,4878,5049,5158,5317,5448,5612,5770,5835,5903", "endColumns": "104,151,126,108,149,126,122,107,170,108,158,130,163,157,64,67,86", "endOffsets": "3842,3994,4121,4230,4380,4507,4630,4738,5044,5153,5312,5443,5607,5765,5830,5898,5985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\b7c5e14f30f524dc22cc8925fd453a32\\transformed\\core-1.12.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "29,30,31,32,33,34,35,129", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2830,2926,3028,3127,3224,3330,3435,13842", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "2921,3023,3122,3219,3325,3430,3556,13938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ca611de3937d8b7fad81038213315e33\\transformed\\foundation-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,101", "endOffsets": "148,250"}, "to": {"startLines": "133,134", "startColumns": "4,4", "startOffsets": "14216,14314", "endColumns": "97,101", "endOffsets": "14309,14411"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\812ae415204e3b60a6d9571a5434b24a\\transformed\\appcompat-1.6.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,13684", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,13761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\98bcb59e84dea2106bcefc907e055731\\transformed\\material3-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,414,534,634,732,847,989,1104,1263,1347,1445,1543,1644,1761,1890,1993,2134,2274,2415,2581,2714,2831,2952,3081,3180,3277,3398,3543,3649,3762,3876,4015,4160,4269,4376,4462,4563,4664,4750,4836,4947,5027,5111,5212,5320,5419,5523,5610,5723,5823,5930,6049,6129,6246", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "171,290,409,529,629,727,842,984,1099,1258,1342,1440,1538,1639,1756,1885,1988,2129,2269,2410,2576,2709,2826,2947,3076,3175,3272,3393,3538,3644,3757,3871,4010,4155,4264,4371,4457,4558,4659,4745,4831,4942,5022,5106,5207,5315,5414,5518,5605,5718,5818,5925,6044,6124,6241,6348"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6891,7012,7131,7250,7370,7470,7568,7683,7825,7940,8099,8183,8281,8379,8480,8597,8726,8829,8970,9110,9251,9417,9550,9667,9788,9917,10016,10113,10234,10379,10485,10598,10712,10851,10996,11105,11212,11298,11399,11500,11586,11672,11783,11863,11947,12048,12156,12255,12359,12446,12559,12659,12766,12885,12965,13082", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "7007,7126,7245,7365,7465,7563,7678,7820,7935,8094,8178,8276,8374,8475,8592,8721,8824,8965,9105,9246,9412,9545,9662,9783,9912,10011,10108,10229,10374,10480,10593,10707,10846,10991,11100,11207,11293,11394,11495,11581,11667,11778,11858,11942,12043,12151,12250,12354,12441,12554,12654,12761,12880,12960,13077,13184"}}]}]}