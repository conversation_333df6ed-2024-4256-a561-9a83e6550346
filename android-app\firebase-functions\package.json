{"name": "mente-en-calma-functions", "version": "1.0.0", "description": "Cloud Functions for Mente en Calma Android App", "main": "index.js", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0"}, "devDependencies": {"jest": "^29.0.0", "firebase-functions-test": "^3.1.0"}, "private": true, "keywords": ["firebase", "functions", "mental-health", "ai", "therapy"], "author": "Mente en Calma Team", "license": "MIT"}