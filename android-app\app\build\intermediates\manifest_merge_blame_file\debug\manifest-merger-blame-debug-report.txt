1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.eligi.menteencalma"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- <PERSON><PERSON>os necesarios -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:6:5-67
12-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:7:5-79
13-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="com.android.vending.BILLING" />
14-->[com.android.billingclient:billing-ktx:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\658f12a736932d07849764a7d0cd2ae4\transformed\billing-ktx-6.1.0\AndroidManifest.xml:9:5-67
14-->[com.android.billingclient:billing-ktx:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\658f12a736932d07849764a7d0cd2ae4\transformed\billing-ktx-6.1.0\AndroidManifest.xml:9:22-64
15
16    <queries>
16-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:12:5-16:15
17        <intent>
17-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:13:9-15:18
18            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
18-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:14:13-91
18-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:14:21-88
19        </intent>
20    </queries>
21
22    <uses-permission android:name="android.permission.WAKE_LOCK" />
22-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
22-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:22-65
23    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
23-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
23-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
24    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
24-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
24-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
25    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" /> <!-- Required by older versions of Google Play services to create IID tokens -->
25-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
25-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
26    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
26-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
26-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:26:22-79
27    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
27-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db33cfb2e58b6d3432243990065bd6a0\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
27-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\db33cfb2e58b6d3432243990065bd6a0\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
28    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
28-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
28-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
29
30    <permission
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
31        android:name="com.eligi.menteencalma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.eligi.menteencalma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
35
36    <application
36-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:9:5-38:19
37        android:name="com.menteencalma.app.MenteEnCalmaApplication"
37-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:10:9-48
38        android:allowBackup="true"
38-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:11:9-35
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
40        android:dataExtractionRules="@xml/data_extraction_rules"
40-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:12:9-65
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:fullBackupContent="@xml/backup_rules"
43-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:13:9-54
44        android:icon="@drawable/ic_launcher_legacy"
44-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:14:9-52
45        android:label="@string/app_name"
45-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:15:9-41
46        android:roundIcon="@drawable/ic_launcher_legacy"
46-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:16:9-57
47        android:supportsRtl="true"
47-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:17:9-35
48        android:testOnly="true"
49        android:theme="@style/Theme.MenteEnCalma" >
49-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:18:9-50
50
51        <!-- Actividad principal -->
52        <activity
52-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:22:9-30:20
53            android:name="com.menteencalma.app.MainActivity"
53-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:23:13-41
54            android:exported="true"
54-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:24:13-36
55            android:theme="@style/Theme.MenteEnCalma.Splash" >
55-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:25:13-61
56            <intent-filter>
56-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:26:13-29:29
57                <action android:name="android.intent.action.MAIN" />
57-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:27:17-69
57-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:27:25-66
58
59                <category android:name="android.intent.category.LAUNCHER" />
59-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:28:17-77
59-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:28:27-74
60            </intent-filter>
61        </activity>
62
63        <!-- Configuración para Google Play Billing -->
64        <meta-data
64-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:33:9-36:45
65            android:name="com.google.android.play.billingclient.version"
65-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:34:13-73
66            android:value="6.1.0" />
66-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:35:13-34
67
68        <activity
68-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
69            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
69-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
70            android:excludeFromRecents="true"
70-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
71            android:exported="false"
71-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
72            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
72-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
73        <!--
74            Service handling Google Sign-In user revocation. For apps that do not integrate with
75            Google Sign-In, this service will never be started.
76        -->
77        <service
77-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
78            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
78-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
79            android:exported="true"
79-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
80            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
80-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
81            android:visibleToInstantApps="true" />
81-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
82        <service
82-->[com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:8:9-14:19
83            android:name="com.google.firebase.components.ComponentDiscoveryService"
83-->[com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:9:13-84
84            android:directBootAware="true"
84-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
85            android:exported="false" >
85-->[com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:10:13-37
86            <meta-data
86-->[com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:11:13-13:85
87                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
87-->[com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:12:17-119
88                android:value="com.google.firebase.components.ComponentRegistrar" />
88-->[com.google.firebase:firebase-auth-ktx:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e49c339321fe1260071f8af1a2a01323\transformed\firebase-auth-ktx-22.3.1\AndroidManifest.xml:13:17-82
89            <meta-data
89-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
90                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
90-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
91                android:value="com.google.firebase.components.ComponentRegistrar" />
91-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
92            <meta-data
92-->[com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:12:13-14:85
93                android:name="com.google.firebase.components:com.google.firebase.functions.ktx.FirebaseFunctionsLegacyRegistrar"
93-->[com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:13:17-129
94                android:value="com.google.firebase.components.ComponentRegistrar" />
94-->[com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:14:17-82
95            <meta-data
95-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
96                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
96-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
97                android:value="com.google.firebase.components.ComponentRegistrar" />
97-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
98            <meta-data
98-->[com.google.firebase:firebase-firestore-ktx:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d4bdf51ef125aab1e5f2b0dcbac28f90\transformed\firebase-firestore-ktx-24.10.1\AndroidManifest.xml:12:13-14:85
99                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
99-->[com.google.firebase:firebase-firestore-ktx:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d4bdf51ef125aab1e5f2b0dcbac28f90\transformed\firebase-firestore-ktx-24.10.1\AndroidManifest.xml:13:17-129
100                android:value="com.google.firebase.components.ComponentRegistrar" />
100-->[com.google.firebase:firebase-firestore-ktx:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\d4bdf51ef125aab1e5f2b0dcbac28f90\transformed\firebase-firestore-ktx-24.10.1\AndroidManifest.xml:14:17-82
101            <meta-data
101-->[com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:15:13-17:85
102                android:name="com.google.firebase.components:com.google.firebase.functions.FirebaseFunctionsKtxRegistrar"
102-->[com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:16:17-122
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:17:17-82
104            <meta-data
104-->[com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:18:13-20:85
105                android:name="com.google.firebase.components:com.google.firebase.functions.FunctionsRegistrar"
105-->[com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:19:17-111
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:20:17-82
107            <meta-data
107-->[com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:17:13-19:85
108                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
108-->[com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:18:17-122
109                android:value="com.google.firebase.components.ComponentRegistrar" />
109-->[com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:19:17-82
110            <meta-data
110-->[com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:20:13-22:85
111                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
111-->[com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:21:17-111
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[com.google.firebase:firebase-firestore:24.10.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c1ab3198062b4904e9f1c7cc6e1de6d9\transformed\firebase-firestore-24.10.1\AndroidManifest.xml:22:17-82
113            <meta-data
113-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
114                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
114-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
116            <meta-data
116-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
117                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
117-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
119            <meta-data
119-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
120                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
120-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
122            <meta-data
122-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
123                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
123-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
125            <meta-data
125-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
126                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
126-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
128            <meta-data
128-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
129                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
129-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
131        </service>
132
133        <activity
133-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
134            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
134-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
135            android:excludeFromRecents="true"
135-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
136            android:exported="true"
136-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
137            android:launchMode="singleTask"
137-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
138            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
138-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
139            <intent-filter>
139-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
140                <action android:name="android.intent.action.VIEW" />
140-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
140-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
141
142                <category android:name="android.intent.category.DEFAULT" />
142-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
142-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
143                <category android:name="android.intent.category.BROWSABLE" />
143-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
143-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
144
145                <data
145-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
146                    android:host="firebase.auth"
146-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
147                    android:path="/"
147-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
148                    android:scheme="genericidp" />
148-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
149            </intent-filter>
150        </activity>
151        <activity
151-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
152            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
152-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
153            android:excludeFromRecents="true"
153-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
154            android:exported="true"
154-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
155            android:launchMode="singleTask"
155-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
156            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
156-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
157            <intent-filter>
157-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
158                <action android:name="android.intent.action.VIEW" />
158-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
158-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
159
160                <category android:name="android.intent.category.DEFAULT" />
160-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
160-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
161                <category android:name="android.intent.category.BROWSABLE" />
161-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
161-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
162
163                <data
163-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
164                    android:host="firebase.auth"
164-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
165                    android:path="/"
165-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
166                    android:scheme="recaptcha" />
166-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c907bb14970d7a73f32bf456033409e\transformed\firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
167            </intent-filter>
168        </activity>
169        <activity
169-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:23:9-27:75
170            android:name="com.android.billingclient.api.ProxyBillingActivity"
170-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:24:13-78
171            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
171-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:25:13-96
172            android:exported="false"
172-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:26:13-37
173            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
173-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:27:13-72
174
175        <property
175-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
176            android:name="android.adservices.AD_SERVICES_CONFIG"
176-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
177            android:resource="@xml/ga_ad_services_config" />
177-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
178
179        <provider
179-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
180            android:name="com.google.firebase.provider.FirebaseInitProvider"
180-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
181            android:authorities="com.eligi.menteencalma.firebaseinitprovider"
181-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
182            android:directBootAware="true"
182-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
183            android:exported="false"
183-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
184            android:initOrder="100" />
184-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
185
186        <activity
186-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\10ae2552bf3cbf45dc9cc601ea39674f\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
187            android:name="androidx.compose.ui.tooling.PreviewActivity"
187-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\10ae2552bf3cbf45dc9cc601ea39674f\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
188            android:exported="true" />
188-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\10ae2552bf3cbf45dc9cc601ea39674f\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
189        <activity
189-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9078f28263f278452faf1cbc8e846522\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
190            android:name="androidx.activity.ComponentActivity"
190-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9078f28263f278452faf1cbc8e846522\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
191            android:exported="true" />
191-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9078f28263f278452faf1cbc8e846522\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
192
193        <provider
193-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
194            android:name="androidx.startup.InitializationProvider"
194-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
195            android:authorities="com.eligi.menteencalma.androidx-startup"
195-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
196            android:exported="false" >
196-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
197            <meta-data
197-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
198                android:name="androidx.emoji2.text.EmojiCompatInitializer"
198-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
199                android:value="androidx.startup" />
199-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
200            <meta-data
200-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
201                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
201-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
202                android:value="androidx.startup" />
202-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
203            <meta-data
203-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
204                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
205                android:value="androidx.startup" />
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
206        </provider>
207
208        <receiver
208-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
209            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
209-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
210            android:enabled="true"
210-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
211            android:exported="false" >
211-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
212        </receiver>
213
214        <service
214-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
215            android:name="com.google.android.gms.measurement.AppMeasurementService"
215-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
216            android:enabled="true"
216-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
217            android:exported="false" />
217-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
218        <service
218-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
219            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
219-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
220            android:enabled="true"
220-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
221            android:exported="false"
221-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
222            android:permission="android.permission.BIND_JOB_SERVICE" />
222-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
223
224        <uses-library
224-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
225            android:name="android.ext.adservices"
225-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
226            android:required="false" />
226-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
227
228        <activity
228-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
229            android:name="com.google.android.gms.common.api.GoogleApiActivity"
229-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
230            android:exported="false"
230-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
231            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
231-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
232
233        <meta-data
233-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
234            android:name="com.google.android.gms.version"
234-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
235            android:value="@integer/google_play_services_version" />
235-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
236
237        <service
237-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
238            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
238-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
239            android:exported="false" >
239-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
240            <meta-data
240-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
241                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
241-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
242                android:value="cct" />
242-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
243        </service>
244        <service
244-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
245            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
245-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
246            android:exported="false"
246-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
247            android:permission="android.permission.BIND_JOB_SERVICE" >
247-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
248        </service>
249
250        <receiver
250-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
251            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
251-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
252            android:exported="false" />
252-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
253        <receiver
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
254            android:name="androidx.profileinstaller.ProfileInstallReceiver"
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
255            android:directBootAware="false"
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
256            android:enabled="true"
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
257            android:exported="true"
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
258            android:permission="android.permission.DUMP" >
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
259            <intent-filter>
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
260                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
261            </intent-filter>
262            <intent-filter>
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
263                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
264            </intent-filter>
265            <intent-filter>
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
266                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
267            </intent-filter>
268            <intent-filter>
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
269                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
270            </intent-filter>
271        </receiver>
272    </application>
273
274</manifest>
