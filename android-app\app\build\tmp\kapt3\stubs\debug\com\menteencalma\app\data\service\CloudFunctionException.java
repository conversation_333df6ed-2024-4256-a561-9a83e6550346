package com.menteencalma.app.data.service;

/**
 * Excepción específica para errores de Cloud Functions
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\u0018\u00002\u00060\u0001j\u0002`\u0002B!\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\n\u0010\u0006\u001a\u00060\u0001j\u0002`\u0002\u00a2\u0006\u0002\u0010\u0007R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0015\u0010\u0006\u001a\u00060\u0001j\u0002`\u0002\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\f"}, d2 = {"Lcom/menteencalma/app/data/service/CloudFunctionException;", "Ljava/lang/Exception;", "Lkotlin/Exception;", "code", "", "message", "originalException", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Exception;)V", "getCode", "()Ljava/lang/String;", "getOriginalException", "()Ljava/lang/Exception;", "app_debug"})
public final class CloudFunctionException extends java.lang.Exception {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String code = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.Exception originalException = null;
    
    public CloudFunctionException(@org.jetbrains.annotations.NotNull()
    java.lang.String code, @org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.NotNull()
    java.lang.Exception originalException) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.Exception getOriginalException() {
        return null;
    }
}