package com.menteencalma.app.presentation.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.menteencalma.app.domain.model.AuthState
import com.menteencalma.app.domain.model.User
import com.menteencalma.app.domain.repository.AuthRepository
import com.menteencalma.app.domain.repository.UserRepository
import com.menteencalma.app.navigation.Screen
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val userRepository: UserRepository
) : ViewModel() {

    private val _authState = MutableStateFlow(AuthState.Loading)
    val authState: StateFlow<AuthState> = _authState.asStateFlow()

    private val _currentUser = MutableStateFlow<User?>(null)
    val currentUser: StateFlow<User?> = _currentUser.asStateFlow()

    init {
        checkAuthState()
    }

    private fun checkAuthState() {
        viewModelScope.launch {
            try {
                val firebaseUser = authRepository.getCurrentUser()
                if (firebaseUser != null) {
                    // Usuario autenticado, verificar si tiene perfil
                    val userProfile = userRepository.getUserProfile(firebaseUser.uid)
                    if (userProfile != null) {
                        _currentUser.value = userProfile
                        _authState.value = AuthState.Authenticated
                    } else {
                        _authState.value = AuthState.NeedsProfile
                    }
                } else {
                    _authState.value = AuthState.Unauthenticated
                }
            } catch (e: Exception) {
                _authState.value = AuthState.Error(e.message ?: "Error desconocido")
            }
        }
    }

    fun signInWithEmail(email: String, password: String) {
        viewModelScope.launch {
            try {
                _authState.value = AuthState.Loading
                val result = authRepository.signInWithEmail(email, password)
                if (result.isSuccess) {
                    checkAuthState() // Recheck state after successful login
                } else {
                    _authState.value = AuthState.Error(
                        result.exceptionOrNull()?.message ?: "Error al iniciar sesión"
                    )
                }
            } catch (e: Exception) {
                _authState.value = AuthState.Error(e.message ?: "Error al iniciar sesión")
            }
        }
    }

    fun signUpWithEmail(email: String, password: String) {
        viewModelScope.launch {
            try {
                _authState.value = AuthState.Loading
                val result = authRepository.signUpWithEmail(email, password)
                if (result.isSuccess) {
                    _authState.value = AuthState.NeedsProfile
                } else {
                    _authState.value = AuthState.Error(
                        result.exceptionOrNull()?.message ?: "Error al crear cuenta"
                    )
                }
            } catch (e: Exception) {
                _authState.value = AuthState.Error(e.message ?: "Error al crear cuenta")
            }
        }
    }

    fun createUserProfile(name: String, age: Int, interests: List<String>) {
        viewModelScope.launch {
            try {
                _authState.value = AuthState.Loading
                val firebaseUser = authRepository.getCurrentUser()
                if (firebaseUser != null) {
                    val user = User(
                        id = firebaseUser.uid,
                        email = firebaseUser.email ?: "",
                        name = name,
                        age = age,
                        interests = interests,
                        subscriptionStatus = "free",
                        createdAt = System.currentTimeMillis()
                    )
                    
                    val result = userRepository.createUserProfile(user)
                    if (result.isSuccess) {
                        _currentUser.value = user
                        _authState.value = AuthState.Authenticated
                    } else {
                        _authState.value = AuthState.Error(
                            result.exceptionOrNull()?.message ?: "Error al crear perfil"
                        )
                    }
                } else {
                    _authState.value = AuthState.Error("Usuario no autenticado")
                }
            } catch (e: Exception) {
                _authState.value = AuthState.Error(e.message ?: "Error al crear perfil")
            }
        }
    }

    fun signOut() {
        viewModelScope.launch {
            try {
                authRepository.signOut()
                _currentUser.value = null
                _authState.value = AuthState.Unauthenticated
            } catch (e: Exception) {
                _authState.value = AuthState.Error(e.message ?: "Error al cerrar sesión")
            }
        }
    }

    fun isUserAuthenticated(): Boolean {
        return _authState.value is AuthState.Authenticated
    }

    fun isOnAuthScreen(route: String?): Boolean {
        return route in Screen.authScreens
    }

    fun clearError() {
        if (_authState.value is AuthState.Error) {
            _authState.value = AuthState.Unauthenticated
        }
    }
}
