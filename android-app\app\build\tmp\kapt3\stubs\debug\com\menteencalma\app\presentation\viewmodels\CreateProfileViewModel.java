package com.menteencalma.app.presentation.viewmodels;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000e\b\u0007\u0018\u0000  2\u00020\u0001:\u0001 B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0013H\u0082@\u00a2\u0006\u0002\u0010\u0014J\u0006\u0010\u0015\u001a\u00020\u0011J\u0006\u0010\u0016\u001a\u00020\u0011J\u0006\u0010\u0017\u001a\u00020\u0011J\u000e\u0010\u0018\u001a\u00020\u00112\u0006\u0010\u0019\u001a\u00020\u0013J\u000e\u0010\u001a\u001a\u00020\u00112\u0006\u0010\u001b\u001a\u00020\u0013J\u000e\u0010\u001c\u001a\u00020\u00112\u0006\u0010\u001d\u001a\u00020\u0013J\u000e\u0010\u001e\u001a\u00020\u00112\u0006\u0010\u001f\u001a\u00020\u0013R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006!"}, d2 = {"Lcom/menteencalma/app/presentation/viewmodels/CreateProfileViewModel;", "Landroidx/lifecycle/ViewModel;", "authRepository", "Lcom/menteencalma/app/domain/repository/AuthRepository;", "userRepository", "Lcom/menteencalma/app/domain/repository/UserRepository;", "functions", "Lcom/google/firebase/functions/FirebaseFunctions;", "(Lcom/menteencalma/app/domain/repository/AuthRepository;Lcom/menteencalma/app/domain/repository/UserRepository;Lcom/google/firebase/functions/FirebaseFunctions;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/menteencalma/app/presentation/viewmodels/CreateProfileUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "callCreateUserProfileFunction", "", "userId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearError", "createProfile", "resetState", "updateAge", "age", "updateDisplayName", "name", "updateGender", "gender", "updateTherapistGender", "therapistGender", "Companion", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class CreateProfileViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.repository.AuthRepository authRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.menteencalma.app.domain.repository.UserRepository userRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.firebase.functions.FirebaseFunctions functions = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.menteencalma.app.presentation.viewmodels.CreateProfileUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.viewmodels.CreateProfileUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String THERAPIST_AURORA = "aurora";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String THERAPIST_ALEJANDRO = "alejandro";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GENDER_MALE = "male";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GENDER_FEMALE = "female";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GENDER_OTHER = "other";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GENDER_PREFER_NOT_TO_SAY = "prefer_not_to_say";
    @org.jetbrains.annotations.NotNull()
    public static final com.menteencalma.app.presentation.viewmodels.CreateProfileViewModel.Companion Companion = null;
    
    @javax.inject.Inject()
    public CreateProfileViewModel(@org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.AuthRepository authRepository, @org.jetbrains.annotations.NotNull()
    com.menteencalma.app.domain.repository.UserRepository userRepository, @org.jetbrains.annotations.NotNull()
    com.google.firebase.functions.FirebaseFunctions functions) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.menteencalma.app.presentation.viewmodels.CreateProfileUiState> getUiState() {
        return null;
    }
    
    public final void updateDisplayName(@org.jetbrains.annotations.NotNull()
    java.lang.String name) {
    }
    
    public final void updateAge(@org.jetbrains.annotations.NotNull()
    java.lang.String age) {
    }
    
    public final void updateGender(@org.jetbrains.annotations.NotNull()
    java.lang.String gender) {
    }
    
    public final void updateTherapistGender(@org.jetbrains.annotations.NotNull()
    java.lang.String therapistGender) {
    }
    
    public final void createProfile() {
    }
    
    private final java.lang.Object callCreateUserProfileFunction(java.lang.String userId, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    public final void clearError() {
    }
    
    public final void resetState() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/menteencalma/app/presentation/viewmodels/CreateProfileViewModel$Companion;", "", "()V", "GENDER_FEMALE", "", "GENDER_MALE", "GENDER_OTHER", "GENDER_PREFER_NOT_TO_SAY", "THERAPIST_ALEJANDRO", "THERAPIST_AURORA", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}