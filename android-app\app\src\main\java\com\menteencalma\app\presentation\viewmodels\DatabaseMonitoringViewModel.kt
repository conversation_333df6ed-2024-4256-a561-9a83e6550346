package com.menteencalma.app.presentation.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.menteencalma.app.data.monitoring.DatabaseMonitoringService
import com.menteencalma.app.data.monitoring.OptimizationRecommendation
import com.menteencalma.app.data.migration.DatabaseMigrationService
import com.menteencalma.app.data.migration.MigrationEstimate
import com.menteencalma.app.data.migration.TargetDatabase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class DatabaseMonitoringViewModel @Inject constructor(
    private val monitoringService: DatabaseMonitoringService,
    private val migrationService: DatabaseMigrationService
) : ViewModel() {

    private val _uiState = MutableStateFlow(DatabaseMonitoringUiState())
    val uiState: StateFlow<DatabaseMonitoringUiState> = _uiState.asStateFlow()

    init {
        observeMetrics()
    }

    private fun observeMetrics() {
        viewModelScope.launch {
            combine(
                monitoringService.metrics,
                monitoringService.alerts
            ) { metrics, alerts ->
                val recommendations = monitoringService.getOptimizationRecommendations()
                val estimatedCost = monitoringService.getEstimatedMonthlyCost()
                
                _uiState.value = _uiState.value.copy(
                    metrics = metrics,
                    alerts = alerts,
                    recommendations = recommendations,
                    estimatedMonthlyCost = estimatedCost,
                    isLoading = false
                )
            }.collect { }
        }
    }

    fun generateMigrationEstimate() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isGeneratingEstimate = true)
            
            try {
                val result = migrationService.estimateMigration()
                if (result.isSuccess) {
                    _uiState.value = _uiState.value.copy(
                        migrationEstimate = result.getOrNull(),
                        isGeneratingEstimate = false
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Error generating estimate: ${result.exceptionOrNull()?.message}",
                        isGeneratingEstimate = false
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error: ${e.message}",
                    isGeneratingEstimate = false
                )
            }
        }
    }

    fun generateMigrationScript(targetDatabase: TargetDatabase) {
        viewModelScope.launch {
            try {
                val script = migrationService.generateMigrationScript(targetDatabase)
                _uiState.value = _uiState.value.copy(
                    migrationScript = script,
                    selectedTargetDatabase = targetDatabase
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error generating script: ${e.message}"
                )
            }
        }
    }

    fun exportData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isExporting = true)
            
            try {
                val result = migrationService.exportUsers()
                if (result.isSuccess) {
                    _uiState.value = _uiState.value.copy(
                        exportedData = result.getOrNull(),
                        isExporting = false
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Error exporting data: ${result.exceptionOrNull()?.message}",
                        isExporting = false
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error: ${e.message}",
                    isExporting = false
                )
            }
        }
    }

    fun validateDataIntegrity() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isValidating = true)
            
            try {
                val result = migrationService.validateDataIntegrity()
                if (result.isSuccess) {
                    _uiState.value = _uiState.value.copy(
                        validationReport = result.getOrNull(),
                        isValidating = false
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Error validating data: ${result.exceptionOrNull()?.message}",
                        isValidating = false
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Error: ${e.message}",
                    isValidating = false
                )
            }
        }
    }

    fun resetMetrics() {
        monitoringService.resetMetrics()
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun clearMigrationData() {
        _uiState.value = _uiState.value.copy(
            migrationEstimate = null,
            migrationScript = null,
            exportedData = null,
            validationReport = null,
            selectedTargetDatabase = null
        )
    }
}

data class DatabaseMonitoringUiState(
    val metrics: com.menteencalma.app.data.monitoring.DatabaseMetrics = com.menteencalma.app.data.monitoring.DatabaseMetrics(),
    val alerts: List<com.menteencalma.app.data.monitoring.DatabaseAlert> = emptyList(),
    val recommendations: List<OptimizationRecommendation> = emptyList(),
    val estimatedMonthlyCost: Double = 0.0,
    val migrationEstimate: MigrationEstimate? = null,
    val migrationScript: String? = null,
    val exportedData: String? = null,
    val validationReport: com.menteencalma.app.data.migration.ValidationReport? = null,
    val selectedTargetDatabase: TargetDatabase? = null,
    val isLoading: Boolean = true,
    val isGeneratingEstimate: Boolean = false,
    val isExporting: Boolean = false,
    val isValidating: Boolean = false,
    val errorMessage: String? = null
)
