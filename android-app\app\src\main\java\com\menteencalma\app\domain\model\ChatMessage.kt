package com.menteencalma.app.domain.model

import kotlinx.serialization.Serializable

/**
 * Modelo de mensaje de chat optimizado para múltiples backends
 */
@Serializable
data class ChatMessage(
    val id: String = "",
    val userId: String = "",
    val content: String = "",
    val type: MessageType = MessageType.USER,
    val timestamp: Long = System.currentTimeMillis(),
    val isRead: Boolean = false,
    val metadata: MessageMetadata? = null,
    
    // Campos para migración/compatibilidad
    val version: Int = 1,
    val source: String = "firebase" // firebase, supabase, postgresql
) {
    
    enum class MessageType {
        USER,           // Mensaje del usuario
        THERAPIST_AI,   // Respuesta de IA (Aurora/Alejandro)
        SYSTEM,         // Mensaje del sistema
        RECOMMENDATION, // Recomendación personalizada
        EXERCISE        // Ejercicio de mindfulness
    }
    
    @Serializable
    data class MessageMetadata(
        val therapistId: String? = null,        // aurora, alejandro
        val sentiment: String? = null,          // positive, negative, neutral
        val confidence: Float? = null,          // 0.0 - 1.0
        val topics: List<String> = emptyList(), // anxiety, depression, stress
        val exerciseId: String? = null,         // ID del ejercicio recomendado
        val sessionId: String? = null,          // ID de sesión de chat
        
        // Para analytics y migración
        val processingTime: Long? = null,
        val modelVersion: String? = null,
        val customData: Map<String, String> = emptyMap()
    )
    
    companion object {
        const val COLLECTION_NAME = "chat_messages"
        const val CURRENT_VERSION = 1
        
        // Índices recomendados para Firebase/Firestore
        val FIRESTORE_INDEXES = listOf(
            "userId",
            "timestamp",
            "type",
            "isRead",
            "userId,timestamp",
            "userId,type,timestamp"
        )
        
        // Schema SQL para migración futura
        const val SQL_SCHEMA = """
            CREATE TABLE chat_messages (
                id VARCHAR(255) PRIMARY KEY,
                user_id VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                type VARCHAR(50) NOT NULL,
                timestamp BIGINT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,
                metadata JSONB,
                version INTEGER DEFAULT 1,
                source VARCHAR(50) DEFAULT 'firebase',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                INDEX idx_user_timestamp (user_id, timestamp),
                INDEX idx_user_type (user_id, type),
                INDEX idx_timestamp (timestamp),
                INDEX idx_unread (user_id, is_read)
            );
        """
    }
    
    /**
     * Convierte a formato para Firebase
     */
    fun toFirebaseMap(): Map<String, Any?> = mapOf(
        "id" to id,
        "userId" to userId,
        "content" to content,
        "type" to type.name,
        "timestamp" to timestamp,
        "isRead" to isRead,
        "metadata" to metadata?.let { meta ->
            mapOf(
                "therapistId" to meta.therapistId,
                "sentiment" to meta.sentiment,
                "confidence" to meta.confidence,
                "topics" to meta.topics,
                "exerciseId" to meta.exerciseId,
                "sessionId" to meta.sessionId,
                "processingTime" to meta.processingTime,
                "modelVersion" to meta.modelVersion,
                "customData" to meta.customData
            )
        },
        "version" to version,
        "source" to source
    )
    
    /**
     * Convierte a formato para PostgreSQL/Supabase
     */
    fun toSQLMap(): Map<String, Any?> = mapOf(
        "id" to id,
        "user_id" to userId,
        "content" to content,
        "type" to type.name.lowercase(),
        "timestamp" to timestamp,
        "is_read" to isRead,
        "metadata" to metadata?.let { kotlinx.serialization.json.Json.encodeToString(MessageMetadata.serializer(), it) },
        "version" to version,
        "source" to source
    )
    
    /**
     * Valida el mensaje antes de guardarlo
     */
    fun validate(): Result<Unit> {
        return when {
            id.isBlank() -> Result.failure(IllegalArgumentException("Message ID cannot be blank"))
            userId.isBlank() -> Result.failure(IllegalArgumentException("User ID cannot be blank"))
            content.isBlank() -> Result.failure(IllegalArgumentException("Content cannot be blank"))
            content.length > 10000 -> Result.failure(IllegalArgumentException("Content too long (max 10000 chars)"))
            timestamp <= 0 -> Result.failure(IllegalArgumentException("Invalid timestamp"))
            else -> Result.success(Unit)
        }
    }
}
